{"compilerOptions": {"target": "esnext", "module": "esnext", "baseUrl": "./", "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "moduleResolution": "node", "lib": ["esnext", "dom", "dom.iterable"], "allowJs": true, "types": ["jest"], "paths": {"app/*": ["src/app/*"], "pages/*": ["src/pages/*"], "shared/*": ["src/shared/*"], "widgets/*": ["src/widgets/*"]}}, "exclude": ["build", "dist", "config"]}