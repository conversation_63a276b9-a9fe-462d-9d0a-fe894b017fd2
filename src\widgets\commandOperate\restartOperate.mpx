
<template>
  <t-popup
    visible="{{visible}}"
    placement="bottom"
    bind:visible-change="cancel"
  >
    <view class="popup-context">
      <view class="title">
        <text>选择重启方式</text>
        <image src="{{RemoteControlUrl.ClosePopup}}" bindtap="cancel"></image>
      </view>
      <view class="restart-btn-container">
        <view
          class="restart-btn"
          wx:class="{{{disable:item.disable}}}"
          wx:for="{{btns}}"
          wx:key="value"
          bindtap="handleClick(item.value)"
        >
          <image src="{{CommandUrl[item.value]}}"></image>
          <text>{{ item.label }}</text>
        </view>
      </view>
    </view>
  </t-popup>

  <t-dialog
    visible="{{showdialog}}"
    content="{{dialogText}}"
    confirm-btn="确定"
    cancel-btn="取消"
    bind:confirm="confirmCommand"
    bind:cancel="cancelCommand"
  />
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import {
    CommandType,
    CommandNameMap,
    CommandDialogMap
  } from 'shared/utils/constant';
  import { CommandApi } from 'shared/api/command';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { CommandUrl, RemoteControlUrl } from 'shared/assets/imageUrl';
  import { getFormattedDate } from 'shared/utils/utils';
  interface Data {
    fetchApi: any;
    CommandUrl: any;
    RemoteControlUrl: any;
    btns: any[];
    clickBtnType: any;
    showdialog: boolean;
    dialogText: any;
  }
  createComponent<Data>({
    properties: {
      vehicleName: {
        type: String,
        value: ''
      },
      visible: {
        type: Boolean,
        value: false
      }
    },
    data: {
      fetchApi: new CommandApi(),
      CommandUrl: CommandUrl,
      RemoteControlUrl: RemoteControlUrl,
      btns: [
        {
          value: CommandType.REMOTE_POWER_ON,
          label: CommandNameMap.get(CommandType.REMOTE_POWER_ON)
        },
        {
          value: CommandType.REMOTE_SHUTDOWN,
          label: CommandNameMap.get(CommandType.REMOTE_SHUTDOWN)
        },
        {
          value: CommandType.ROVER_REBOOT,
          label: CommandNameMap.get(CommandType.ROVER_REBOOT)
        },
        {
          value: CommandType.VEHICLE_REBOOT,
          label: CommandNameMap.get(CommandType.VEHICLE_REBOOT)
        }
      ],
      clickBtnType: null,
      showdialog: false,
      dialogText: ''
    },
    methods: {
      handleClick(type: CommandType) {
        if (
          [
            CommandType.VIDEO_REBOOT,
            CommandType.MCU_REBOOT,
            CommandType.ANDROID_REBOOT
          ].indexOf(type) > -1
        ) {
          return;
        }
        this.clickBtnType = type;
        this.showdialog = true;
        this.dialogText = CommandDialogMap.get(type);
      },
      cancel() {
        this.triggerEvent('onVisibleChange');
      },
      async confirmCommand() {
        const res = await this.fetchApi.sendCommand({
          vehicleName: this.vehicleName,
          commandType: this.clickBtnType,
          operateTime: getFormattedDate()
        });
        if (res.code === HTTPSTATUSCODE.Success) {
          wx.showToast({
            duration: 3000,
            title: '已下发成功！'
          });
          this.cancel();
          this.showdialog = false;
        } else {
          wx.showToast({
            duration: 3000,
            icon: 'none',
            title: res.message
          });
        }
      },
      cancelCommand() {
        this.showdialog = false;
        this.clickBtnType = null;
        this.dialogText = '';
      }
    }
  });
</script>

<style lang="scss" scoped>
  .popup-context {
    height: 222px;
    padding-bottom: 12px;
  }

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 16px;
    padding-right: 16px;
    height: 52px;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(26, 26, 26, 1);
    border-bottom: 1px solid rgba(236, 236, 236, 1);
    image {
      width: 14px;
      height: 14px;
    }
  }
  .restart-btn-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    bottom: 0px;
    padding-bottom: 50px;
    padding-right: 12px;
    padding-left: 12px;
    .restart-btn {
      width: 45%;
      height: 42px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 12px;
      background: rgba(245, 245, 245, 1);
      border: 1px solid rgba(204, 204, 204, 1);
      border-radius: 8px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(96, 96, 96, 1);
      &.disable {
        color: rgba(96, 96, 96, 0.5);
      }
      image {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "t-dialog": "tdesign-miniprogram/dialog/dialog",
      "t-popup": "tdesign-miniprogram/popup/popup"
    }
  }
</script>
