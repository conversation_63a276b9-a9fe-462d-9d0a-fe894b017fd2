<template>
  <t-popup
    visible="{{visible}}"
    bind:visible-change="onVisibleChange"
    placement="bottom"
  >
    <view class="permission-setting">
      <view class="block">
        <view class="header">
          <view class="title">维修单可见权限</view>
          <view bindtap="onVisibleChange" class="close"
            ><image src="{{TechMaintainUrl.ClosePopup}}" class="close-icon"
          /></view>
        </view>
      </view>
      <view class="factory">
        <view class="group">
          <view class="owner oem">车厂</view>
          <view class="owner-name">{{ oemName }}</view>
        </view>
        <t-switch
          defaultValue="{{isAllowOEMVisible}}"
          bindchange="handleOEMVisibleChange"
        />
      </view>
      <view class="group station">
        <view class="owner">服务站</view>
        <view class="owner-name">{{ serviceStationName }}</view>
      </view>
    </view>
  </t-popup>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { addGlobalEventListener } from 'shared/utils/emit';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { IsAllowOEMVisible } from 'shared/utils/constant';

  createComponent({
    properties: {
      visible: {
        type: Boolean,
        value: false // 是否展开通知
      }
    },
    computed: {},
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      TechMaintainUrl,
      isAllowOEMVisible: false as boolean, // 是否允许车厂可见
      serviceStationName: '', // 服务站名称
      oemName: '' // 车厂名称
    },
    lifetimes: {
      created() {
        addGlobalEventListener('getRepairOrderNumber', orderNumber => {
          this.orderNumber = orderNumber;
        });
      },
      attached() {
        this.getPermissionData();
      },
      detached() {},
      ready() {}
    },
    watch: {},
    methods: {
      onVisibleChange() {
        this.triggerEvent('close');
      },
      async getPermissionData() {
        try {
          const res = await this.techMaintenanceApi.getVisibleDetails(
            this.orderNumber
          );
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              isAllowOEMVisible: res.data.oemVisible === IsAllowOEMVisible.ALLOW,
              oemName: res.data.oemName || '',
              serviceStationName: res.data.serviceStationName || ''
            });
          } else {
            console.log('获取维修单可见权限失败:', res.message);
            wx.showToast({
              title: res.message || '获取维修单可见权限失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.log('获取维修单可见权限失败:', error);
          wx.showToast({
            title: '获取维修单可见权限失败',
            icon: 'none'
          });
        }
      },
      async handleOEMVisibleChange(event: any) {
        const isAllowOEMVisible = event.detail.value;
        console.log('是否允许车厂可见:', {
          number: this.orderNumber,
          type: 'OEM',
          oemVisible: isAllowOEMVisible
            ? IsAllowOEMVisible.ALLOW
            : IsAllowOEMVisible.NOT_ALLOW
        });
        try {
          const res = await this.techMaintenanceApi.setVisibleDetails({
            number: this.orderNumber,
            type: 'OEM',
            oemVisible: isAllowOEMVisible
              ? IsAllowOEMVisible.ALLOW
              : IsAllowOEMVisible.NOT_ALLOW
          });
          if (res.code === HTTPSTATUSCODE.Success) {
            wx.showToast({
              title: '设置成功',
              icon: 'success'
            });
          } else {
            console.log('设置维修单可见权限失败:', res.message);
            wx.showToast({
              title: res.message || '设置维修单可见权限失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.log('设置维修单可见权限失败:', error);
          wx.showToast({
            title: '设置维修单可见权限失败',
            icon: 'none'
          });
        }
      }
    }
  });
</script>

<style lang="scss" scoped>
  .permission-setting {
    padding: 0 32rpx;
    height: 180px;

    .block {
      width: 100vw;
      border-bottom: 1px solid #f0f0f0;
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
      }
      .title {
        text-align: center;
        font-weight: 600;
        font-size: 32rpx;
      }
      .close {
        width: 32rpx;
        height: 32rpx;
        .close-icon {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
    .factory {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      padding: 16rpx 0;
    }
    .station {
      padding: 16rpx 0;
      border-bottom: 1px solid #f0f0f0;
    }
    .group {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 88rpx;

      .oem {
        margin-right: 36rpx;
      }

      .owner {
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
      }
      .owner-name {
        font-size: 28rpx;
        color: #666666;
        margin-left: 24rpx;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-popup": "tdesign-miniprogram/popup/popup",
      "t-switch": "tdesign-miniprogram/switch/switch"
    }
  }
</script>