<template>
  <view class="icon-field">
    <t-image
      src="{{imageSrc}}"
      mode="aspectFill"
      width="{{imageWidth}}"
      height="{{imageHeight}}"
    />
    <text class="text">{{ text ? text : '-' }}</text>
  </view>
</template>
<script>
  import { createComponent } from '@mpxjs/core';
  import { Notification } from 'shared/assets/imageUrl';
  createComponent({
    properties: {
      imageSrc: {
        type: String,
        value: ''
      },
      text: {
        type: String,
        value: ''
      },
      imageWidth: {
        type: String,
        value: 16
      },
      imageHeight: {
        type: String,
        value: 16
      }
    }
  });
</script>
<style lang="scss">
  .icon-field {
    display: flex;
    align-items: center;
    text-align: center;
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(153, 153, 153, 1);
    .text {
      margin-left: 4px;
    }
  }
</style>
<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image"
    }
  }
</script>