/**
 * 文件上传组件相关接口定义
 */

// 基础文件信息接口
export interface BaseFileInfo {
  /** 文件名称 */
  name: string;
  /** 文件本地路径 */
  path: string;
  /** 文件大小（字节） */
  size: number;
  /** 文件类型 */
  type: 'image' | 'video' | 'file' | string;
}

// 扩展文件信息接口（用于上传组件）
export interface FileInfo extends BaseFileInfo {
  /** 文件上传后的URL */
  url?: string;
  /** 文件上传后的key */
  fileKey?: string;
  /** 上传状态 */
  status?: 'loading' | 'done' | 'failed' | 'reload';
  /** 上传进度百分比 */
  percent?: number;
  /** 是否已上传成功 */
  uploaded?: boolean;
  /** 是否为图片 */
  isImage?: boolean;
  /** 是否为视频 */
  isVideo?: boolean;
}

// 媒体文件接口（用于预览组件）
export interface MediaFileInfo extends BaseFileInfo {
  /** 媒体文件URL */
  url: string;
  /** 位置名称（用于标签显示） */
  locationName?: string;
  /** 上传状态 */
  status?: 'loading' | 'done' | 'failed' | 'reload';
  /** 上传进度百分比 */
  percent?: number;
}

// 文件上传结果接口
export interface UploadResult {
  /** 文件上传后的key */
  fileKey: string;
  /** 文件大小 */
  fileSize?: number;
  /** 文件访问URL */
  url?: string;
}

// 文件处理结果接口
export interface ProcessResult {
  /** 原始文件信息 */
  file: FileInfo;
  /** 处理状态 */
  finalStatus: 'success' | 'error';
  /** 成功时的结果 */
  result?: UploadResult;
  /** 失败时的错误信息 */
  error?: Error;
}

// 批量处理完成结果接口
export interface CompleteResult {
  /** 成功上传的文件 */
  successFiles: ProcessResult[];
  /** 上传失败的文件 */
  failedFiles: ProcessResult[];
  /** 成功文件数量 */
  successCount: number;
  /** 失败文件数量 */
  failCount: number;
}

// 文件上传函数类型
export type UploadFunction = (file: FileInfo) => Promise<UploadResult>;

// 文件删除回调函数类型
export type DeleteCallback = (file: FileInfo, index: number) => void;

// 文件点击回调函数类型
export type FileClickCallback = (file: FileInfo, index: number) => void;

// 上传配置接口
export interface UploadConfig {
  /** 最大并发数 */
  maxConcurrent?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 最大文件数量 */
  maxFiles?: number;
  /** 支持的文件类型 */
  acceptTypes?: ('image' | 'video' | 'file')[];
  /** 文件大小限制(MB) */
  maxFileSize?: number;
  /** 是否显示删除按钮 */
  showDelete?: boolean;
  /** 是否显示上传遮罩层 */
  showUploadMask?: boolean;
}

// 组件事件数据接口
export interface FileEventData {
  /** 文件信息 */
  file: FileInfo;
  /** 文件索引 */
  index: number;
}

export interface RetryUploadEventData {
  /** 需要重试的文件 */
  item: FileInfo;
  /** 文件索引 */
  index: number;
}

// 文件key信息接口（用于后端交互）
export interface FileKeyInfo {
  /** 文件key */
  fileKey: string;
  /** 文件类型 */
  type: string;
}

// tdesign上传组件文件接口
export interface TDesignFileInfo {
  /** 文件名 */
  name: string;
  /** 文件URL */
  url: string;
  /** 文件类型 */
  type: 'image' | 'video';
  /** 文件大小 */
  size?: number;
  /** 上传进度 */
  percent?: number;
  /** 上传状态 */
  status?: string;
}

// 文件上传组件通用Props接口
export interface FileUploaderProps {
  /** 最大文件数量 */
  maxNum?: number;
  /** 默认文件列表 */
  defaultAttachmentList?: TDesignFileInfo[];
  /** 支持的媒体类型 */
  mediaType?: ('image' | 'video')[];
  /** 组件类型标识 */
  type?: string;
  /** 文件大小限制配置 */
  size?: {
    size: number;
    unit: string;
    message: string;
  };
}

// 预览组件Props接口
export interface PreviewComponentProps {
  /** 媒体文件列表 */
  mediaList?: MediaFileInfo[];
  /** 文件列表 */
  fileList?: FileInfo[];
  /** 是否显示删除按钮 */
  showDelete?: boolean;
  /** 是否显示上传遮罩层 */
  showUploadMask?: boolean;
}

// 增强上传器Props接口
export interface EnhancedUploaderProps extends UploadConfig {
  /** 默认文件列表 */
  defaultFiles?: FileInfo[];
}
