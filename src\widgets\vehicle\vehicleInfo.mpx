
<template>
  <view class="takeover-info" wx:if="{{driveInfo.takeOverDescription}}">
    {{ driveInfo.takeOverDescription }}
  </view>
  <control-panel
    vehicleName="{{selectedVehicleName}}"
    bind:showPopup="showPopup"
    bind:closePopup="closePopup"
  >
  </control-panel>

  <view class="vehicle-info-container">
    <view class="tabs">
      <view
        wx:for="{{tabs}}"
        wx:key="value"
        class="tab"
        wx:class="{{{select: curSelectTab===item.value}}}"
        data-tab="{{index}}"
        bindtap="onTapTab(item.value)"
      >
        <view>
          {{ item.label }}
        </view>
        <view class="under-line" wx:if="{{curSelectTab === item.value}}"></view>
      </view>
    </view>

    <drive-info
      class="info-container"
      vehicleInfo="{{driveInfo}}"
      wx:if="{{curSelectTab === 'driveInfo'}}"
    />
    <ticket-info
      class="info-container"
      vehicleName="{{driveInfo.vehicleName}}"
      wx:if="{{curSelectTab === 'ticketInfo'}}"
    />
    <versiion-info
      class="info-container"
      vehicleName="{{driveInfo.vehicleName}}"
      wx:if="{{curSelectTab === 'versionInfo'}}"
    />
    <insurance-info
      class="info-container"
      vehicleName="{{driveInfo.vehicleName}}"
      wx:if="{{curSelectTab === 'insuranceInfo'}}"
    />
  </view>
</template>

<script  lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { SingleVehicle } from 'shared/assets/imageUrl';
  import {
    TakeOverTypeNameMap,
    TakeOverSourceNameMap,
    TakeOverType
  } from 'shared/utils/constant';
  const GPScolor: any = {
    '1': 'rgba(250,44,25,1)',
    '2': 'rgba(250,44,25,1)',
    '3': 'rgba(255,170,0,1)',
    '4': 'rgba(18,179,93,1)',
    '5': 'rgba(18,179,93,1)'
  };

  const tabs = [
    { label: '跑行信息', value: 'driveInfo' },
    { label: '工单信息', value: 'ticketInfo' },
    { label: '车型版本', value: 'versionInfo' },
    { label: '保险信息', value: 'insuranceInfo' }
  ];
  createComponent<any>({
    properties: {
      selectedVehicleName: String,
      vehicleInfo: Object
    },
    data: {
      TakeOverTypeNameMap,
      TakeOverSourceNameMap,
      TakeOverType,
      GPScolor,
      tabs,
      SingleVehicle: SingleVehicle,
      curSelectTab: 'driveInfo'
    },
    computed: {
      driveInfo: function () {
        if (!this.vehicleInfo) {
          return {};
        }
        const SignalNum = Number(this.vehicleInfo.gpsSignal);
        return {
          ...this.vehicleInfo,
          gpsSignal: [
            {
              label: 'one',
              background:
                1 <= SignalNum
                  ? this.GPScolor[this.vehicleInfo.gpsSignal]
                  : 'rgba(245,245,245,1)'
            },
            {
              label: 'two',
              background:
                2 <= SignalNum
                  ? this.GPScolor[this.vehicleInfo.gpsSignal]
                  : 'rgba(245,245,245,1)'
            },
            {
              label: 'three',
              background:
                3 <= SignalNum
                  ? this.GPScolor[this.vehicleInfo.gpsSignal]
                  : 'rgba(245,245,245,1)'
            },
            {
              label: 'four',
              background:
                4 <= SignalNum
                  ? this.GPScolor[this.vehicleInfo.gpsSignal]
                  : 'rgba(245,245,245,1)'
            },
            {
              label: 'five',
              background:
                5 <= SignalNum
                  ? this.GPScolor[this.vehicleInfo.gpsSignal]
                  : 'rgba(245,245,245,1)'
            }
          ],
          takeOverDescription: this.vehicleInfo.takeOverInfo
            ? `${this.TakeOverTypeNameMap.get(
                this.vehicleInfo.takeOverInfo.takeOverStatus
              )}-${this.TakeOverSourceNameMap.get(
                this.vehicleInfo.takeOverInfo.takeOverSource
              )}-${this.vehicleInfo.takeOverInfo.takeOverUserName}`
            : null
        };
      }
    },
    methods: {
      onTapTab(val: any) {
        this.setData({
          curSelectTab: val
        });
      },
      showPopup() {
        this.triggerEvent('showPopup');
      },
      closePopup() {
        this.triggerEvent('closePopup');
      }
    }
  });
</script>

<style lang="scss" scoped>
  .takeover-info {
    margin-left: 16px;
    height: 20px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(204, 136, 0, 1);
  }
  .vehicle-info-container {
    height: 100%;
    margin: 8px 16px 0px 16px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    padding: 8px;
  }
  .tabs {
    width: 100%;
    height: 28px;
    display: flex;
    justify-content: space-between;
    .tab {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
      &.select {
        font-weight: 500;
        color: rgba(250, 44, 25, 1);
      }
    }
    .under-line {
      margin-top: 6px;
      width: 16px;
      height: 2px;
      margin-left: 20px;
      background: rgba(250, 44, 25, 1);
      border-radius: 1px;
      transform: translateX(0) scaleX(0.7);
    }
  }

  view,
  swiper-item {
    box-sizing: border-box;
  }

  .info-container {
    flex: 1;
    width: 100%;
    overflow: hidden;
    height: 60vh;
    padding: 8px;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "disableScroll": true,
    "navigationStyle": "custom",
    "usingComponents": {
      "drive-info": "widgets/vehicle/driveInfo.mpx",
      "versiion-info": "widgets/vehicle/versionInfo.mpx",
      "ticket-info": "widgets/vehicle/ticketInfo.mpx",
      "control-panel": "widgets/vehicle/controlPanel.mpx",
      "insurance-info": "widgets/vehicle/insuranceInfo.mpx"
    }
  }
</script>
