<template>
  <view
    wx:class="{{{ 'switch-wrapper-disabled':!controlStart, 'switch-wrapper': !checked && controlStart, 'switch-wrapper-checked': checked && controlStart }}}"
    bindtap="onChange"
  >
    <view
      wx:class="{{{ 'icon-disabled':!controlStart, icon: !checked && controlStart, 'icon-checked': checked && controlStart }}}"
    ></view>
    <view
      wx:class="{{{ 'label-disabled':!controlStart, label: !checked && controlStart, 'label-checked': checked && controlStart}}}"
      >强遥</view
    >
  </view>
</template>
<script lang='ts'>
  import { createComponent } from '@mpxjs/core';
  createComponent({
    properties: {
      checked: {
        type: Boolean,
        value: false
      },
      controlStart: {
        type: Boolean,
        value: false
      }
    },
    data: {},
    methods: {
      onChange() {
        if (this.controlStart && this.checked) {
          this.triggerEvent('changechecked');
          wx.vibrateShort({
            type: 'medium'
          });
        } else if (this.controlStart && !this.checked) {
          this.openDialog();
        }
      },
      confirmForceControl() {
        this.triggerEvent('openforcecontrol');
        wx.vibrateShort({
          type: 'medium'
        });
      },
      openDialog() {
        wx.showModal({
          content: '确认开启强制遥控，用于窄路通行吗？',
          showCancel: true,
          confirmColor: '#fa2c19',
          success: res => {
            if (res.confirm) {
              this.confirmForceControl();
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      }
    }
  });
</script>
<style lang="scss">
  .switch-wrapper,
  .switch-wrapper-checked,
  .switch-wrapper-disabled {
    width: 62px;
    height: 24px;
    border-radius: 22px;
    background: #dcdcdc;
    display: flex;
    align-items: center;
    position: relative;
    box-shadow: 0 0 3px 0 rgb(0 0 0 / 8%);
    transition: all 0.5s ease-in-out;
    .label,
    .label-checked,
    .label-disabled {
      font-size: 14px;
      position: absolute;
      left: 26px;
      transition: all 0.5s ease-in-out;
    }
    .icon,
    .icon-checked,
    .icon-disabled {
      width: 20px;
      height: 20px;
      border-radius: 20px;
      background: #fefefe;
      position: absolute;
      transform: translateY(-50%);
      top: 50%;
      left: 3px;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
      transition: all 0.5s ease-in-out;
    }
  }
  .switch-wrapper-disabled {
    background: #cfd0df;
    .icon-disabled {
      background: #fdfdfd4d;
    }
    .label-disabled {
      color: rgba(60, 110, 240, 0.3);
    }
  }
  .switch-wrapper-checked {
    background: #486de8;
    .icon-checked {
      left: 39px;
    }
    .label-checked {
      left: 7px;
      color: white;
    }
  }
</style>
<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-dialog': 'tdesign-miniprogram/dialog/dialog'
    }
  };
</script>
