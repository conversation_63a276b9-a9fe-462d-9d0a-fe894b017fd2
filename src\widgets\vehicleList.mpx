<template>
  <view class="select-vehcile-cmp">
    <view class="fixed-bar">
      <view class="search-bar">
        <view class="search" bindtap="handleFocus">
          <t-icon
            name="{{VehicleUrl.SearchIcon}}"
            size="18px"
            color="#868D9F"
          />
          <text>输入车号</text>
        </view>
        <view class="change-search-mode" bindtap="changeSort">
          <t-image
            src="{{WorkBenchUrl.SwitchIcon}}"
            mode="aspectFill"
            width="20px"
            height="20px"
          />
          <text>{{ searchMode.name }}</text>
        </view>
      </view>

      <view class="tip" wx:if="{{ tip}}">{{ tip }}</view>
    </view>

    <view style="height: {{tip ? 84 : 60}}px"></view>

    <scroll-view
      class="distance-list"
      wx:if="{{searchMode.nextMode === 'station'}}"
      scroll-y
      type="list"
      style="margin-top: {{tip ? 10 : 0}}px"
    >
      <vehicle-item
        wx:for="{{vehicleList}}"
        wx:key="vehicleName"
        itemInfo="{{item}}"
        selectedVehicleName="{{selectedVehicleName}}"
        bind:handleSelectItem="handleSelectItem"
      >
      </vehicle-item>
    </scroll-view>

    <scroll-view
      class="station-list"
      style="margin-top: {{tip ? 20 : 0}}px"
      wx:if="{{searchMode.nextMode === 'distance'}}"
      scroll-y
      type="list"
    >
      <view class="list-item" wx:for="{{vehicleList}}" wx:key="stationName">
        <view class="station-name">
          <image src="{{WorkBenchUrl.StationIcon}}"></image>
          <text>{{ item.stationName }}</text>
        </view>
        <vehicle-item
          wx:for="{{item.list}}"
          wx:key="vehicleName"
          itemInfo="{{item}}"
          selectedVehicleName="{{selectedVehicleName}}"
          bind:handleSelectItem="handleSelectItem"
        >
        </vehicle-item>
      </view>
    </scroll-view>
  </view>
</template>
<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import {
    RemoteControlUrl,
    WorkBenchUrl,
    VehicleUrl
  } from 'shared/assets/imageUrl';
  import { CommonApi } from 'shared/api/common';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import {
    TakeOverTypeNameMap,
    TakeOverSourceNameMap,
    TakeOverType
  } from 'shared/utils/constant';
  import { CheckVehicle } from 'shared/utils/checkVehicle';
  import useLocationStore from 'shared/store/useLocationStore';
  import { mapStores } from '@mpxjs/pinia';
  const app = getApp();

  createComponent<any>({
    properties: {
      tip: {
        type: String,
        value: ''
      },
      selectedVehicleName: {
        type: String,
        value: ''
      },
      checkContent: {
        type: Array
      },
      whichPageToGo: {
        // 选完车后跳去哪个页面
        type: String
      }
    },
    data: {
      WorkBenchUrl: WorkBenchUrl,
      VehicleUrl,
      fetchApi: new CommonApi(),
      searchMode: {
        name: '按站点',
        nextMode: 'station'
      },
      vehicleList: []
    },
    computed: {
      stickyHeight() {
        return this.statusBarHeight + this.titleBarHeight;
      },
      ...mapStores(useLocationStore)
    },
    methods: {
      changeSort() {
        if (this.searchMode.nextMode === 'station') {
          this.searchMode = {
            name: '按距离',
            nextMode: 'distance'
          };
          this.formatStationVehicle();
        } else if (this.searchMode.nextMode === 'distance') {
          this.searchMode = {
            name: '按站点',
            nextMode: 'station'
          };
          this.getUserVehicleList();
        }
      },
      handleSelectItem(e: any) {
        if (!e.detail.val) {
          this.triggerEvent('handleSelect', { val: e.detail.val });
          return;
        }
        if (!this.checkContent || this.checkContent.length <= 0) {
          this.triggerEvent('handleSelect', { val: e.detail.val });
          return;
        }
        const check = new CheckVehicle(this.checkContent, e.detail.val);
        check.checkVehicleFunc().then(res => {
          if (res) {
            this.triggerEvent('handleSelect', { val: e.detail.val });
          }
        });
      },
      formatStationVehicle() {
        const obj: any = {};
        this.vehicleList.forEach((v: any) => {
          if (obj[v.stationName]) {
            obj[v.stationName].list.push(v);
          } else {
            obj[v.stationName] = { stationName: v.stationName, list: [v] };
          }
        });
        this.vehicleList = Object.values(obj);
      },
      async getUserVehicleList() {
        const userLocation = wx.getStorageSync('userLocation');
        const res = await this.fetchApi.getUserVehicleList(
          userLocation.longitude,
          userLocation.latitude
        );
        if (res.code === HTTPSTATUSCODE.Success) {
          this.vehicleList = res.data.map((v: any) => {
            return {
              ...v,
              description: v.takeoverStatus
                ? `${TakeOverTypeNameMap.get(
                    v.takeoverStatus
                  )}-${TakeOverSourceNameMap.get(v.takeoverSource)}-${
                    v.takeoverUserName
                  }`
                : null
            };
          });
        }
      },
      handleFocus() {
        wx.navigateTo({
          url: '/pages/selectVehiclePages/searchVehicle',
          events: {
            searchVehiclePageChannel: (data: any) => {
              this.triggerEvent('handleSelect', { val: data.vehicleName });
            }
          },
          success: (res: any) => {
            res.eventChannel.emit('searchVehiclePageChannel', {
              // 在搜车页面选车后去到哪个页面，比如开舱门功能在搜车页面选车后去到开舱门页面
              whichPageToGo: this.whichPageToGo,
              // 在搜车页面选车后的校验
              checkContent: this.checkContent
            });
          }
        });
      }
    },
    lifetimes: {
      created: function () {
        this.getUserVehicleList();
      },
      attached() {
        this.setData({
          statusBarHeight: app.globalData.statusBarHeight,
          titleBarHeight: app.globalData.titleBarHeight
        });
      },
      detached: function () {}
    }
  });
</script>
<style lang="scss">
  .select-vehcile-cmp {
    width: 100%;
    .fixed-bar {
      position: fixed;
      z-index: 999;
      background-color: white;
      width: 100%;
    }
    .search-bar {
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 16px 6px 11px;
      .search {
        height: 36px;
        line-height: 36px;
        width: 100%;
        margin-right: 16px;
        background: rgba(245, 245, 245, 1);
        border-radius: 4px;
        display: flex;
        padding-left: 14px;
        align-items: center;
        text {
          margin-left: 8px;
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: normal;
          color: rgba(204, 204, 204, 1);
        }
      }
      .change-search-mode {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 90px;
        text {
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: normal;
          color: rgba(128, 128, 128, 1);
        }
      }
    }

    .tip {
      height: 36px;
      line-height: 36px;
      background: rgba(255, 248, 235, 1);
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(204, 136, 0, 1);
      padding-left: 16px;
    }

    .station-list {
      .list-item {
        .station-name {
          image {
            margin-right: 5px;
            width: 14px;
            height: 16px;
          }
          text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          display: flex;
          align-items: center;
          height: 40px;
          line-height: 40px;
          padding-left: 17px;
          background: rgba(245, 245, 245, 1);
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 500;
          color: rgba(51, 51, 51, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
</style>
<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image",
      "t-search": "tdesign-miniprogram/search/search",
      "vehicle-item": "shared/ui/vehicleItem.mpx",
      "t-icon": "tdesign-miniprogram/icon/icon"
    }
  }
</script>
