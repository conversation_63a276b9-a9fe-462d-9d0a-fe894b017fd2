import { doRequest } from 'shared/api/fetch';

export class FreeDriveApi {
  getVehicleList = (longitude: any, latitude: any) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/freeDrive/getVehicleList',
      data: {
        longitude: longitude,
        latitude: latitude
      }
    };
    return doRequest(requestOptions);
  };

  getMapVehicle = (longitude: any, latitude: any) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/freeDrive/getMapVehicle',
      data: {
        longitude: longitude,
        latitude: latitude
      }
    };
    return doRequest(requestOptions);
  };

  getStopList(vehicleNameList: any[]) {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/freeDrive/getStopList',
      data: {
        vehicleNameList: vehicleNameList
      }
    };
    return doRequest(requestOptions);
  }

  dispatchDpature(stopId: any, stopName: any, vehicleNameList: any[]) {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/freeDrive/navigation',
      data: {
        vehicleNameList: vehicleNameList,
        stopId: stopId,
        stopName: stopName
      }
    };
    return doRequest(requestOptions);
  }

  async dispatchAnyDrive(anyDriveInfo: {
    stopName: string;
    longitude: number;
    latitude: number;
    heading: number;
    vehicleNameList: any[];
  }) {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/freeDrive/arbitrary-navigation',
      data: {
        ...anyDriveInfo
      }
    };
    return doRequest(requestOptions);
  }
}
