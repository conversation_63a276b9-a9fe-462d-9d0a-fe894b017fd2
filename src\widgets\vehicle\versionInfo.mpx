<template>
  <view class="version-info-card">
    <view class="info-item">
      <text class="info-label">车型名称</text>
      <text class="info-value">{{ versionInfo.vehicleTypeName || '-' }}</text>
    </view>
    <view class="info-item">
      <text class="info-label">软件版本号</text>
      <text class="info-value">{{ versionInfo.roverVersion || '-' }}</text>
    </view>
    <view class="info-item">
      <text class="info-label">安卓版本号</text>
      <text class="info-value">{{ versionInfo.androidVersion || '-' }}</text>
    </view>
    <view class="info-item">
      <text class="info-label">地图版本号</text>
      <text class="info-value">{{ versionInfo.mapVersion || '-' }}</text>
    </view>
    <view class="info-item">
      <text class="info-label">视频版本号</text>
      <text class="info-value">{{ versionInfo.videoVersion || '-' }}</text>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { SingleVehicleApi } from 'shared/api/singleVehicle';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createComponent({
    properties: {
      vehicleName: String
    },
    data: {
      fetchApi: new SingleVehicleApi(),
      versionInfo: {}
    },
    lifetimes: {
      created: function () {
        this.getVersionInfo();
      },
      detached: function () {
        // console.log('9999');
      }
    },
    methods: {
      async getVersionInfo() {
        const res: any = await this.fetchApi.getVehicleVersionInfo(
          this.vehicleName
        );
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({ versionInfo: res.data });
        }
      }
    }
  });
</script>

<script type="application/json">
  {
    "component": true
  }
</script>

<style lang="scss" scoped>
  .version-info-card {
    padding: 8px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 1);
  }

  .info-item {
    margin-bottom: 8px;
    display: flex;
  }

  .info-label {
    width: 70px;
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(153, 153, 153, 1);
  }

  .info-value {
    width: calc(100vw - 48px - 70px);
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(51, 51, 51, 1);
  }

  .info-item:last-child {
    margin-bottom: 0;
  }
</style>
