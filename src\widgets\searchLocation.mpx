<template>
  <t-popup
    visible="{{visible}}"
    bind:visible-change="closePopup"
    placement="bottom"
    style="width: 100%; height: {{height}}px; border-radius: 16px 16px 0 0"
    close-on-overlay-click="{{false}}"
  >
    <view class="top-bar">
      <view class="search-bar">
        <view class="icon-prefix-wrapper">
          <t-icon
            name="{{VehicleUrl.SearchIcon}}"
            size="16px"
            color="#868D9F"
          />
        </view>
        <view class="input-wrapper">
          <input
            placeholder="搜索地址"
            placeholderStyle="font-size: 14px"
            confirm-type="search"
            class="search-input"
            cursor-color="#FA2C19"
            cursor="{{searchValue.length}}"
            model:value="{{searchValue}}"
            maxlength="{{30}}"
            model:focus="{{isSearchFocused}}"
            always-embed="{{true}}"
            bindconfirm="onSearch"
            bindfocus="onFocus"
            bindinput="onInput"
          />
        </view>
        <view class="icon-suffix-wrapper" bindtap="onClear">
          <t-icon name="close-circle-filled" size="16px" color="#868D9F" />
        </view>
      </view>
      <view class="close-icon-wrapper" bindtap="closePopup">
        <view
          class="close-icon"
          style="background: url({{RemoteControlUrl.ClosePopup}}) no-repeat
                center/100%; width: 14px; height: 14px;"
        ></view>
      </view>
    </view>

    <view class="address-list-wrapper">
      <view class="search-status-image" wx:if="{{searchValue === ''}}">
        <t-image
          src="{{IndexPageUrl.SearchInit}}"
          mode="scaleToFill"
          width="223"
          height="148"
        />
      </view>
      <view
        class="search-status-image"
        wx:elif="{{searchValue !== '' && addressList.length === 0}}"
      >
        <t-image
          src="{{IndexPageUrl.SearchEmpty}}"
          mode="scaleToFill"
          width="223"
          height="148"
      /></view>
      <view
        class="address-list"
        wx:elif="{{searchValue !== '' && addressList.length > 0}}"
      >
        <view
          class="address-item"
          wx:for="{{addressList}}"
          wx:key="id"
          bindtap="updateUserLocation(item, $event)"
        >
          <view class="address-item-name"
            ><view
              wx:for="{{item.segments}}"
              wx:key="index"
              wx:for-item="segment"
              class="{{segment.highlighted ? 'highlight' : ''}}"
            >
              {{ segment.text }}
            </view></view
          >
        </view>
      </view>
    </view>
  </t-popup>
</template>
<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import {
    IndexPageUrl,
    RemoteControlUrl,
    VehicleUrl
  } from 'shared/assets/imageUrl';
  import { isEmpty } from 'shared/utils/utils';
  import tmap from 'shared/utils/tencentMap';
  import { mapStores } from '@mpxjs/pinia';
  import useLocationStore from 'shared/store/useLocationStore';
  const app = getApp();
  createComponent({
    data: {
      IndexPageUrl,
      RemoteControlUrl,
      VehicleUrl,
      searchValue: '',
      addressList: [],
      isEmpty,
      tmap,
      isSearchFocused: false,
      height: 690
    },
    properties: {
      visible: {
        type: Boolean,
        value: false
      }
    },
    watch: {
      visible(val) {
        if (!val) {
          this.setData({
            searchValue: '',
            isSearchFocused: false,
            filteredVehicleList: this.vehicleList
          });
        }
      }
    },
    computed: {
      ...mapStores(useLocationStore)
    },
    lifetimes: {
      ready() {
        const windowInfo = wx.getWindowInfo();
        this.setData({
          height: windowInfo.screenHeight - 128
        });
      }
    },
    methods: {
      updateUserLocation(locationInfo: any, e: any) {
        this.locationStore.setLocation({
          latitude: locationInfo.latitude,
          longitude: locationInfo.longitude
        });
        this.locationStore.setAddress(locationInfo.address);
        wx.setStorageSync('userLocation', {
          latitude: locationInfo.latitude,
          longitude: locationInfo.longitude
        });
        wx.setStorageSync('userAddress', locationInfo.address);
        this.closePopup();
      },
      closePopup() {
        this.triggerEvent('closepopup');
        this.setData({
          searchValue: '',
          isSearchFocused: false,
          addressList: []
        });
      },
      getAddressList(e: any) {
        const that = this;
        const keyword = e.detail.value;
        const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        this.tmap.getSuggestion({
          keyword: e.detail.value,
          success: function (res: any) {
            //搜索成功后的回调
            const sug: any = [];
            const regex = new RegExp(`(${escapedKeyword})`, 'i');
            for (var i = 0; i < res.data.length; i++) {
              const segments = res.data[i].title
                .split(regex)
                .map((segment: any, index: number) => {
                  // 检查当前片段是否为关键字
                  const isKeyword = regex.test(segment);
                  return {
                    text: segment,
                    highlighted: isKeyword
                  };
                });
              sug.push({
                // 获取返回结果，放到sug数组中
                title: res.data[i].title,
                id: res.data[i].id,
                address: res.data[i].address,
                city: res.data[i].city,
                district: res.data[i].district,
                latitude: res.data[i].location.lat,
                longitude: res.data[i].location.lng,
                segments: segments
              });
            }
            that.setData({
              //设置suggestion属性，将关键词搜索结果以列表形式展示
              addressList: sug
            });
          },
          fail: function (error: any) {
            console.error(error);
          },
          complete: function (res: any) {
            that.setData({
              searchValue: e.detail.value
            });
          }
        });
      },
      onInput(e: any) {
        this.getAddressList(e);
        this.setData({
          isSearchFocused: true
        });
      },
      onFocus(e: any) {
        this.setData({
          isSearchFocused: true
        });
      },
      onSearch(e: any) {
        this.getAddressList(e);
        this.setData({
          isSearchFocused: false
        });
        wx.hideKeyboard();
      },
      onClear(e: any) {
        this.setData({
          searchValue: '',
          isSearchFocused: false,
          addressList: []
        });
      }
    }
  });
</script>
<style lang="scss">
  .top-bar {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-evenly;
    -webkit-align-items: center;
    align-items: center;
    border-bottom: 1px solid rgb(245, 245, 245);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgb(26, 26, 26);
    padding: 16px 0 16px 1px;
    .close-icon-wrapper {
      position: absolute;
      right: 10px;
      width: 32px;
      height: 32px;
      display: -webkit-flex;
      display: flex;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-align-items: center;
      align-items: center;
    }
    .search-bar {
      width: 81%;
      height: 32px;
      background: rgb(245, 245, 246);
      border-radius: 16px;
      display: -webkit-flex;
      display: flex;
      -webkit-justify-content: space-between;
      justify-content: space-between;
      -webkit-align-items: center;
      align-items: center;
      padding-left: 12px;
      padding-right: 12px;
      margin-right: 16px;
      .input-wrapper {
        width: 100%;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        padding: 0 6px;
        input {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .address-list-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: calc(724 * 0.8px);
    .search-status-image {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .address-list {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-direction: column;
      width: 100%;
      height: 100%;
      .address-item {
        margin-left: 16px;
        margin-right: 16px;
        width: 100%;
        height: 46px;
        line-height: 46px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
        .address-item-name {
          font-size: 16px;
          display: flex;
          .highlight {
            color: #fa2c19;
          }
        }
      }
    }
  }
</style>
<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-icon': 'tdesign-miniprogram/icon/icon',
      't-popup': 'tdesign-miniprogram/popup/popup',
      't-image': 'tdesign-miniprogram/image/image'
    }
  };
</script>
