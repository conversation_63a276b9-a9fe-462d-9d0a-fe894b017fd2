<template>
  <view
    class="step-line-content {{index === 0 ? 'start' : 'other'}}"
    wx:for="{{stepList}}"
  >
    <view class="step-line">
      <image
        class="start-point"
        wx-if="{{index === 0}}"
        src="{{Notification.StartPoint}}"
      />
      <view
        wx-if="{{index > 0}}"
        class="point"
        style="background:{{item.color || '#FED6CF'}}"
      ></view>
      <view class="line"></view>
    </view>
    <view class="content-info">
      <view class="event-title">{{ item.title }}</view>
      <view class="event-desc" wx-if="{{item.desc}}">{{ item.desc }}</view>
      <view class="event-time" wx-if="{{item.time}}"
        >时间：{{ item.time }}</view
      >
    </view>
  </view>
</template>

<script>
  import { createComponent } from '@mpxjs/core';
  import { Notification } from 'shared/assets/imageUrl';
  createComponent({
    data: {
      Notification: Notification
    },
    properties: {
      stepList: {
        type: Array,
        value: []
      }
    }
  });
</script>

<style lang="scss">
  .step-line-content {
    display: flex;
    &.other {
      margin-left: 6px;
    }
    &:last-child {
      .line {
        display: none;
      }
    }
    .step-line {
      display: flex;
      flex-flow: column;
      align-items: center;
      margin-right: 8px;
      .start-point {
        width: 20px;
        height: 21px;
      }
      .point {
        width: 8px;
        height: 8px;
        border-radius: 8px;
      }
      .line {
        width: 1px;
        height: calc(100% - 12px);
        background: #fed6cf;
      }
    }
    .content-info {
      min-height: 60px;
    }
  }

  .event-title {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(50, 50, 50, 1);
    margin-bottom: 8px;
  }

  .event-desc,
  .event-time {
    font-size: 11px;
    font-family: JDZhengHT;
    font-weight: normal;
    color: rgba(102, 102, 102, 1);
  }
</style>