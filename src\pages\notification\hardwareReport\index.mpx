<template>
  <view class="hardware-report-container">
    <navi-bar
      show-address="{{false}}"
      title="硬件报修"
      showReturn="true"
    ></navi-bar>
    <view class="main-container">
      <view class="title-box">{{ vehicleName }}</view>
      <view class="form-group">
        <view class="field-item">
          <view class="form-label" style="flex: 1">
            问题影响 <text class="required">*</text>
          </view>
          <view class="form-options inline">
            <t-radio-group v-model="isInfluenceOperation" bindchange="onChange">
              <t-radio value="{{1}}" label="影响运营" />
              <t-radio
                style="margin-left: 16px"
                value="{{0}}"
                label="不影响运营"
              />
            </t-radio-group>
          </view>
        </view>
      </view>
      <view class="form-group">
        <view class="field-item flex-columns">
          <view class="form-label">
            问题描述 <text class="required">*</text>
            <t-textarea
              class="textarea"
              placeholder="请输入问题描述"
              maxlength="{{50}}"
              indicator="true"
              value="{{description}}"
              bindchange="handleInput"
            ></t-textarea>
          </view>
        </view>
        <view
          class="field-item inline"
          bindtap="handleSheetVisible"
          style="padding: 4px"
        >
          <view class="form-label">维修位置</view>
          <view class="form-value">
            <text>{{ selectHardwareTypeName }}</text>
          </view>
        </view>
        <t-divider style="margin-top: 8px; margin-bottom: 8px" />
        <view class="field-item flex-columns">
          <view class="attachment-wrapper">
            <view class="tips"> 上传图片或视频(最多上传1个视频，3张照片) </view>
            <upload-image maxNum="4" bindfileInfo="saveFiles" />
          </view>
        </view>
      </view>
    </view>
    <view class="footer">
      <view class="btn primry" bindtap="debounceReport">提报完成</view>
    </view>
    <action-sheet
      visible="{{sheetVisible}}"
      placement="bottom"
      title="维修位置"
      bindclose="handleClose"
    >
      <scroll-view
        class="btn-list"
        type="list"
        scroll-y
        style="width: 100%; height: 600px"
        show-scrollbar="{{false}}"
      >
        <view
          class="btn-item"
          wx:class="{{{active:item.isActive}}}"
          wx:for="{{hardwareTypeList}}"
          bindtap="handleSelect(item)"
        >
          {{ item.hardwareTypeName }}
        </view></scroll-view
      >

      <view class="footer action-footer">
        <view class="btn primry" bindtap="handleClose">
          选择完成({{ selectHardwareTypeIds?.length }})
        </view>
      </view>
    </action-sheet>
  </view>
</template>
<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import { doRequest, HTTPSTATUSCODE } from 'shared/api/fetch';
  import { HardWareReport } from 'shared/api/hardwareReport';
  import { showToast } from '../../../shared/utils/utils';
  const fetchApi = new HardWareReport();
  interface HardwareItem {
    hardwareTypeId: number;
    hardwareTypeName: string;
    isActive: boolean;
  }
  createPage<any>({
    data: {
      vehicleName: '',
      isInfluenceOperation: null,
      sheetVisible: false,
      description: '',
      pictureList: [],
      hardwareTypeList: [],
      timeout: null
    },
    computed: {
      selectHardwareTypeName() {
        return this.hardwareTypeList
          ?.filter((i: HardwareItem) => i.isActive)
          ?.map((i: HardwareItem) => i.hardwareTypeName)
          ?.join(',');
      },
      selectHardwareTypeIds() {
        return (
          this.hardwareTypeList
            ?.filter((i: HardwareItem) => i.isActive)
            ?.map((i: HardwareItem) => i.hardwareTypeId) || []
        );
      }
    },
    methods: {
      onChange(e: any) {
        this.setData({
          isInfluenceOperation: e?.detail?.value
        });
      },
      handleInput(e: any) {
        this.setData({
          description: e?.detail?.value
        });
      },
      handleClose() {
        this.sheetVisible = false;
      },
      handleSelect(value: HardwareItem) {
        const active = this.hardwareTypeList?.find(
          (i: HardwareItem) => i.hardwareTypeId === value.hardwareTypeId
        );
        active.isActive = !active.isActive;
        this.$forceUpdate();
      },
      handleSheetVisible() {
        this.sheetVisible = true;
      },
      showToast(message: string) {
        wx.showToast({
          icon: 'none',
          title: message
        });
      },
      validateFieldsValue() {
        if (this.isInfluenceOperation != 0 && this.isInfluenceOperation != 1) {
          this.showToast('请选择问题影响');
          return false;
        } else if (!this.description) {
          this.showToast('请输入问题描述');
          return false;
        }
        return true;
      },
      handleReport() {
        const {
          selectHardwareTypeIds,
          vehicleName,
          description,
          isInfluenceOperation,
          pictureList,
          video
        } = this.data;
        if (!this.validateFieldsValue()) {
          return;
        }
        fetchApi
          .addVehicleRequire({
            vehicleName,
            requireHardwareTypeIds: selectHardwareTypeIds?.join(';'),
            title: description,
            isInfluenceOperation,
            pictureList: pictureList,
            description,
            video: video
          })
          .then((res: any) => {
            if (res.code === HTTPSTATUSCODE.Success) {
              wx.showToast({
                icon: 'none',
                title: '提报成功',
                mask: true
              });
              setTimeout(() => {
                wx.navigateBack({
                  delta: 1
                });
              }, 1500);
            } else {
              wx.showToast({
                icon: 'none',
                title: res.message
              });
            }
          });
      },
      debounceReport() {
        if (this.timeout) {
          clearTimeout(this.timeout);
        }
        this.timeout = setTimeout(() => {
          this.handleReport();
        }, 500);
      },
      getHardwareTypeList() {
        fetchApi.getHardwareType().then((res: any) => {
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              hardwareTypeList: res.data?.map((item: any) => ({
                ...item,
                isActive: false
              }))
            });
          }
        });
      },
      saveFiles(e: any) {
        const pic: any[] = [];
        let v: any = null;
        e?.detail?.fileKeys?.forEach((i: any) => {
          if (i.type === 'image') {
            pic.push(i.fileKey);
          } else if (i.type === 'video') {
            v = i.fileKey;
          }
        });
        this.setData({
          pictureList: pic,
          video: v
        });
      }
    },
    onLoad(query: { vehicleName: string }) {
      this.vehicleName = query.vehicleName;
      this.getHardwareTypeList();
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>
<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    flex-flow: column;
  }
  .main-container {
    height: calc(100vh - 200px);
    overflow-y: auto;
    display: flex;
    flex-flow: column;
  }
  .title-box {
    margin: 16px 16px 8px;
    flex: 0 0 38px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    text-align: center;
    line-height: 38px;
  }
  .form-group {
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    margin: 0 16px 8px;
    padding: 12px;
    .t-icon-check-circle-filled:before {
      color: red;
    }
    .field-item {
      display: flex;
      &.flex-columns {
        flex-flow: column;
      }
    }
    .form-label {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
      margin-right: 6px;
      flex: 0 0 72px;
      .required {
        color: #fa2c19;
      }
    }
    .t-radio--block {
      padding: 0;
    }
    .t-radio__border {
      display: none;
    }
    .t-radio__title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
    }
    .textarea {
      width: 100%;
      height: 104px;
      background-color: #f6f6f6;
      border-radius: 4px;
      padding: 10px;
      font-size: 14px;
      color: #666;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
      margin-top: 8px;
      margin-bottom: 8px;
      padding: 4px !important;
    }
  }
  .form-options {
    position: relative;
    &.inline {
      .t-radio-group {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }
    }
  }
  .form-value {
    flex: 1;
    text-align: right;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: bold;
    color: rgba(26, 26, 26, 1);
    max-width: 77%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
    padding-right: 20px;
    &::after {
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/workbench/arrow-right.png?Expires=3868521457&AccessKey=n828WHAXD584pTvi&Signature=VHgyCxiYJ7xbPV0JaCcRIF90sDI%3D')
        no-repeat center;
      background-size: contain;
      color: rgba(204, 204, 204, 1);
      position: absolute;
      right: 0;
      top: 1px;
    }
  }
  .btn-list {
    display: flex;
    width: 100%;
    margin: 16px 0;
    flex-wrap: wrap;
    padding-bottom: 40px;
    justify-content: space-between;
    .btn-item {
      flex: 0 0 30%;
      // height: 32px;
      line-height: 32px;
      background: rgb(245, 245, 245);
      border: 1px solid rgb(204, 204, 204);
      border-radius: 4px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      margin-bottom: 12px;
      color: rgb(51, 51, 51);
      display: flex;
      align-items: center;
      justify-content: center;
      word-break: break-all;
      padding: 0 4px;
      box-sizing: border-box;
    }
    .active {
      background: rgba(255, 241, 240, 1);
      border: 1px solid rgba(250, 44, 25, 1);
      border-radius: 4px;
      color: #fa2c19;
    }
  }
  .footer {
    height: 150px;
    width: 100%;
    background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      width: 80%;
      height: 40px;
      border-radius: 40px;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      line-height: 40px;
      margin-bottom: 70px;
    }
    .normal {
      width: 106px;
      height: 40px;
      border: 1px solid rgba(204, 204, 204, 1);
      border-radius: 30px;
      background: #fff;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
      margin-right: 16px;
    }
  }
  .action-footer {
    position: fixed;
    left: 0;
    height: 40px;
  }
  .t-upload {
    width: 100% !important;
  }

  .t-textarea__placeholder {
    font-size: 14px !important;
    font-family: PingFang SC !important;
    font-weight: normal !important;
    color: rgba(128, 128, 128, 1) !important;
  }

  .t-textarea__indicator {
    color: #999 !important;
  }
  .tips {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(150, 150, 150, 1);
  }

  .t-upload__close-btn {
    background: rgba(0, 0, 0, 0.7) !important;
  }
  .t-icon-close {
    font-weight: bold !important;
    font-size: 24px !important;
  }
  .t-upload__wrapper {
    .t-icon-close {
      font-size: 14px !important;
      font-weight: normal !important;
    }
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "shared/ui/naviBar.mpx",
      "t-radio": "tdesign-miniprogram/radio/radio",
      "t-radio-group": "tdesign-miniprogram/radio-group/radio-group",
      "t-divider": "tdesign-miniprogram/divider/divider",
      "t-textarea": "tdesign-miniprogram/textarea/textarea",
      "action-sheet": "shared/ui/actionSheet.mpx",
      "upload-image": "shared/ui/uploadImage.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
