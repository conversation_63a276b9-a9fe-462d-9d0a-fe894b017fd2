import { doRequest } from 'shared/api/fetch';
import { getLOPDN, getLOPdomain } from 'shared/utils/config';
const token = wx.getStorageSync('JD_AUTH_TOKEN');
export class CallFrontApi {
  getFrontUserList = (vehicleName: any) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/call/getSiteUserList',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  getUserPhone = (userName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/call/getUserPhone',
      data: { userName }
    };
    return doRequest(requestOptions);
  };

  getErpPhone = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/call/getStationPersonPhone',
      data: {
        vehicleName
      }
    };
    return doRequest(requestOptions);
  };

  callCockpit(vehicleName: string, remark: any) {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/call/callCockpit',
      data: {
        vehicleName,
        remark
      }
    };
    return doRequest(requestOptions);
  }
}
