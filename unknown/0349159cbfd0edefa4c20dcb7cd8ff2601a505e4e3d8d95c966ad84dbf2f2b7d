
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="单手遥控"
  ></navi-bar>

  <vehicle-list
    selectedVehicleName="{{selectedVehicleName}}"
    whichPageToGo="/pages/singlehandControl/index"
    tip="选择车辆后，进入单手遥控"
    bind:handleSelect="getVehicleInfo"
  />
</template>
<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  import { CommonApi } from 'shared/api/common';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import {
    SystemStatus,
    TakeOverSourceType,
    TakeOverType
  } from 'shared/utils/constant';
  interface Data {
    fetchApi: any;
    selectedVehicleName: any;
  }
  createPage<Data>({
    data: {
      fetchApi: new CommonApi(),
      selectedVehicleName: null
    },
    methods: {
      async getVehicleInfo(e: any) {
        try {
          const res: any = await this.fetchApi.getVehicleInfo(e.detail.val);
          if (res.code === HTTPSTATUSCODE.Success) {
            this.selectVehicle(
              {
                ...res.data,
                vehicleName: e.detail.val
              },
              e
            );
          } else {
            wx.showToast({
              title: res.message,
              icon: 'none'
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      selectVehicle(vehicleInfo: any, e: any) {
        const {
          vehicleName,
          takeoverSource,
          takeoverUserName,
          takeoverStatus,
          systemStatus
        } = vehicleInfo;
        if (!systemStatus || systemStatus === SystemStatus.OFFLINE) {
          wx.showToast({
            title: `当前车辆离线，不能遥控！`,
            icon: 'none'
          });
          return;
        } else {
          if (takeoverStatus === TakeOverType.TAKEOVER) {
            if (takeoverSource !== TakeOverSourceType.MINI_MONITOR) {
              wx.showToast({
                title: `${takeoverUserName}正在接管中！`,
                icon: 'none'
              });
              return;
            } else {
              const userName = wx.getStorageSync('userName');
              if (userName === takeoverUserName) {
                if (this.selectedVehicleName === vehicleName) {
                  wx.navigateBack({
                    delta: 1
                  });
                }
              } else {
                wx.showToast({
                  title: `${takeoverUserName}正在接管中！`,
                  icon: 'none'
                });
                return;
              }
            }
          }
        }
        this.setData({
          selectedVehicleName: vehicleInfo.vehicleName
        });
        wx.redirectTo({
          url: `/pages/singlehandControl/index?vehicleName=${vehicleInfo.vehicleName}`
        });
      }
    },
    onLoad: function (options) {
      if (options.vehicleName) {
        this.setData({
          selectedVehicleName: options.vehicleName
        });
      }
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>
<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
