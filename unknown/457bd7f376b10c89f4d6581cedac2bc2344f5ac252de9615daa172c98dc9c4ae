<template>
  <view class="file-list-container">
    <view class="file-item" wx:for="{{fileList}}" wx:key="index">
      <view
        class="file-content-bg {{item.status === 'failed' ? 'error-bg' : ''}}"
      >
        <view class="file-info">
          <image src="{{TechMaintainUrl.UploadedFile}}" class="file-icon" />
          <view class="file-name">{{ item.name }}</view>

          <!-- 上传中遮罩层 -->
          <view
            wx:if="{{showUploadMask && item.status && item.status !== 'done'}}"
            class="upload-mask"
            data-index="{{index}}"
            data-item="{{item}}"
            bindtap="onFileClick"
          >
            <block wx:if="{{item.status === 'loading'}}">
              <view class="loading-spinner"></view>
              <view class="upload-text">
                {{ item.percent ? item.percent + '%' : '上传中...' }}
              </view>
            </block>

            <block wx:if="{{item.status === 'failed'}}">
              <view class="error-icon">!</view>
              <view class="upload-text error">上传失败</view>
            </block>
          </view>
        </view>

        <!-- 删除按钮 -->
        <view
          wx:if="{{showDelete}}"
          class="delete-btn"
          bindtap="handleDelete"
          data-index="{{index}}"
          data-item="{{item}}"
        >
          <image src="{{TechMaintainUrl.Delete}}" class="delete-icon" />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';

  createComponent({
    properties: {
      fileList: {
        type: Array,
        value: []
      },
      showDelete: {
        type: Boolean,
        value: true
      },
      showUploadMask: {
        type: Boolean,
        value: false
      }
    },
    data: {
      TechMaintainUrl
    },
    methods: {
      handleDelete(e) {
        const { index, item } = e.currentTarget.dataset;
        this.triggerEvent('delete', { file: item, index });
      }
    },
    watch: {
      fileList(newVal) {
        // 监听文件列表变化，处理上传状态等逻辑
        console.log('文件列表变化:', newVal);
      }
    }
  });
</script>

<style lang="scss">
  .file-list-container {
    display: flex;
    flex-wrap: wrap;
    margin: -8rpx;
  }

  .file-item {
    width: 50%;
    box-sizing: border-box;
    padding: 8rpx;
  }

  .file-content-bg {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8f8f8;
    border-radius: 12rpx;
    padding: 12rpx;
    min-height: 48rpx;
    overflow: hidden;

    &.error-bg {
      background: #fff0f0;
    }
  }

  .file-info {
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
  }

  .file-icon {
    flex-shrink: 0;
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }

  .file-name {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    min-width: 0; /* 关键修复：允许文本截断 */
  }

  .delete-btn {
    flex-shrink: 0;
    z-index: 10;
    margin-left: 8rpx;
    .delete-icon {
      width: 32rpx;
      height: 32rpx;
      object-fit: contain;
    }
  }

  .upload-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    z-index: 5;
  }

  .loading-spinner {
    flex-shrink: 0;
    width: 24rpx;
    height: 24rpx;
    border: 3rpx solid #007aff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    margin-right: 8rpx;
  }

  .error-icon {
    flex-shrink: 0;
    width: 24rpx;
    height: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8rpx;
    font-size: 24rpx;
    color: #ff3b30;
    font-weight: bold;
  }

  .upload-text {
    font-size: 24rpx;
    color: #007aff;

    &.error {
      color: #ff3b30;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true
  };
</script>