import { doRequest } from 'shared/api/fetch';
export class My {
  async getConfigStationCrashList() {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/user/getConfigStationCrashList'
    };
    return doRequest(requestOptions);
  }

  async configAccidentVoiceNotify(param: { key: number; value: boolean }) {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/user/updateConfig',
      data: { ...param, config: 'configAccidentVoiceNotify' }
    };
    return doRequest(requestOptions);
  }

  async getUserMaskPhone(userName: string) {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/user/getMaskPhone',
      data: { userName }
    };
    return doRequest(requestOptions);
  }
}
