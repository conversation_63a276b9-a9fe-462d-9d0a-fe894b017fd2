import { doRequest } from 'shared/api/fetch';

export class SingleVehicleApi {
  getVehicleRealTime = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/map/getVehicleRealTime',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  getVehicleVersionInfo = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/map/getVehicleVersion',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  getVehicleTicketInfo = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/map/getVehicleIssue',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  getVehiclePermission = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/map/getVehicleBasic',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  // 轮询OTA服务，获取地图下载/升级状态
  getMapUpdateStatus(vehicleName: string) {
    return doRequest({
      url: '/ota/server/get_vehicle_progress',
      method: 'POST',
      data: {
        vehicleName,
        appNameList: ['map']
      }
    });
  }

  // 推迟地图升级
  postponeMapUpdate(vehicleName: string) {
    return doRequest({
      url: '/monitor/web/ota/mapSilentDownload',
      method: 'POST',
      data: {
        vehicleName
      }
    });
  }
}
