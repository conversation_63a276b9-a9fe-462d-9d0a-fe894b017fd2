<index bindshowauthor="showLoad" bindsmsloginres='smsloginResListener' config='{{config}}' checkboxChecked='{{checkboxChecked}}' />
<view hidden="{{config.hiddenLoginType == 2}}">
  <button wx:if="{{!stopClick && checkboxChecked}}" class='phone-btn' bindtap="quickLoginHandler" data-lgid="btn" phone-number-no-quota-toast="{{false}}" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" style="{{config.loginConfig && config.loginConfig.wxBtnStyle}}">
    手机号快捷登录
  </button>
  <!-- 防止连续点击触发获取微信用户信息 -->
  <button wx:else class='phone-btn' data-lgid="fbd" style="{{config.loginConfig && config.loginConfig.wxBtnStyle}}" bindtap="quickLoginHandler">
    手机号快捷登录
  </button>
</view>
<!-- 弹窗类型展示协议授权 -->
<view class="dialog-tips" wx:if="{{selfTipsDialog}}">
  <view>
    <instruction class="no-fix" type='modal' config='{{config}}' bindchangecheck="changeCheckbox" />
    <button wx:if="{{smsBtn}}" class='phone-btn phone-modal' bindtap="quickLoginHandler" data-lgid="mdl" style="{{config.loginConfig && config.loginConfig.wxBtnStyle}}" phone-number-no-quota-toast="{{false}}" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
      确认
    </button>
    <index wx:if="{{userBtn}}" type="modal" bindshowauthor="showLoad" bindsmsloginres='smsloginResListener' config='{{config}}' checkboxChecked='{{true}}' />
    <view class='cancel' bindtap="reject">我再想想</view>
  </view>
</view>
<!-- 底部展示协议授权 -->
<instruction wx:else config='{{config}}' bindchangecheck="changeCheckbox" />
<view>
  <canvas class="********************************" canvas-id='fbfd3ec3c4ab4213b9a09b5c5e769ceb'></canvas>
</view>
<privacy-auth-modal />
