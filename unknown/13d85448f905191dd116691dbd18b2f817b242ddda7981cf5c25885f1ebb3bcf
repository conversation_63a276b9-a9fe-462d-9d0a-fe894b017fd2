import { doRequest } from './fetch';

class CarTrackApi {
  public async getBugLabel() {
    const requestOptions: RequestOptions = {
      url: '/shadow/web/jira/getBugLabel',
      method: 'POST'
    };
    return doRequest(requestOptions);
  }

  public async addShadow(shadowInfo: any) {
    const requestOptions: RequestOptions = {
      url: '/shadow/web/event/add',
      method: 'POST',
      data: shadowInfo
    };
    return doRequest(requestOptions);
  }

  public async reportJira(jiraInfo: {
    id: number;
    topic: string;
    description: string;
    reportUser: string;
  }) {
    const requestOptions: RequestOptions = {
      url: '/shadow/web/jira/report',
      method: 'POST',
      data: jiraInfo
    };
    return doRequest(requestOptions);
  }
}

export default CarTrackApi;
