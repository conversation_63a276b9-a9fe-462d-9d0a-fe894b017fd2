<template>
  <view class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner">
        <view
          class="spinner"
          wx:class="{{ { 'success': allDataLoaded } }}"
        ></view>
        <view wx:if="{{allDataLoaded}}" class="success-icon">✓</view>
      </view>
      <view class="loading-text">{{ loadingText }}</view>
      <view class="progress-container">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progress}}%"></view>
        </view>
        <view class="progress-text">{{ progress }}%</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import LoginApi from 'shared/api/login';
  import { CommonApi } from 'shared/api/common';
  import permissionManager from 'shared/utils/permissionManager';
  import { TAB_BAR_PAGES } from 'shared/utils/constant';
  import { toLogin } from 'shared/utils/utils';

  interface TaskStatus {
    userInfo: boolean;
    token: boolean;
    enum: boolean;
  }

  createPage({
    data: {
      loginApi: new LoginApi(),
      commonFetchApi: new CommonApi(),
      taskStatus: {
        userInfo: false,
        token: false,
        enum: false
      } as TaskStatus,
      allDataLoaded: false,
      loadingText: '加载中...',
      progress: 0,
      completedTasks: 0,
      totalTasks: 3,
      hasNavigated: false
    },
    lifetimes: {
      created() {
        console.log('Transition Page created');
        this.initializeApp();
      }
    },
    methods: {
      async initializeApp() {
        try {
          await Promise.all([
            this.getUserInfo(),
            this.getToken(),
            this.getEnum()
          ]);
          this.setData({
            allDataLoaded: true,
            loadingText: '加载完成！'
          });
          setTimeout(() => {
            this.navigateToMainPage();
          }, 200);
        } catch (error) {
          console.error('加载失败:', error);
          this.setData({
            loadingText: '加载失败，请重试'
          });
          setTimeout(() => {
            toLogin();
          }, 1000);
        }
      },
      updateProgress(taskName: keyof TaskStatus) {
        const newTaskStatus = { ...this.data.taskStatus };
        newTaskStatus[taskName] = true;
        const completedCount =
          Object.values(newTaskStatus).filter(Boolean).length;
        const progress = Math.round(
          (completedCount / this.data.totalTasks) * 100
        );
        this.setData({
          taskStatus: newTaskStatus,
          completedTasks: completedCount,
          progress: progress
        });
      },
      async getUserInfo() {
        try {
          const res = await this.loginApi.fetchUserInfoAfterLogin();
          if (res.code === HTTPSTATUSCODE.Success) {
            wx.setStorageSync('userName', res?.data?.userName);
            wx.setStorageSync('appResourceInfoList', res.data.resourceList);
            this.updateProgress('userInfo');
          } else {
            throw new Error(res.message || '获取用户信息失败');
          }
        } catch (err) {
          console.error('getUserInfo error:', err);
          throw err;
        }
      },
      async getToken() {
        try {
          const res = await this.loginApi.getToken();
          if (res.code === HTTPSTATUSCODE.Success) {
            wx.setStorageSync('JD_AUTH_TOKEN', res.data.accessToken);
            this.updateProgress('token');
          } else {
            throw new Error(res.message || '获取访问令牌失败');
          }
        } catch (err) {
          console.error('getToken error:', err);
          throw err;
        }
      },
      async getEnum() {
        try {
          await this.commonFetchApi.getEnum();
          this.updateProgress('enum');
        } catch (err) {
          console.error('getEnum error:', err);
          throw err;
        }
      },
      navigateToMainPage() {
        if (this.data.hasNavigated) return;
        this.setData({ hasNavigated: true });
        const authorizedTabs =
          permissionManager.filterByPermission(TAB_BAR_PAGES);
        wx.setStorageSync('authorizedTabs', authorizedTabs);
        const defaultTabBarPage = this.getDefaultPage(authorizedTabs);
        if (defaultTabBarPage) {
          wx.switchTab({
            url: defaultTabBarPage.path,
            success: () => {
              console.log('成功跳转到:', defaultTabBarPage.path);
            },
            fail: error => {
              console.error('跳转失败:', error);
              toLogin();
            }
          });
        } else {
          console.warn('没有找到授权页面，跳转到登录页');
          wx.showToast({
            title: '登录失败，请重新登录',
            icon: 'none',
            duration: 2000
          });
          toLogin();
        }
      },
      getDefaultPage(authorizedTabs: any) {
        if (authorizedTabs.length === 0) {
          return null;
        }
        if (authorizedTabs.length === TAB_BAR_PAGES.length) {
          // 如果所有页面都有权限，返回车辆页面，否则如果有工作台页面返回工作台页面
          const vehiclePage = authorizedTabs.find(
            (page: any) => page.key === 'workbench'
          );
          if (vehiclePage) {
            return vehiclePage;
          }
        } else {
          const workbenchPage = authorizedTabs.find(
            (page: any) => page.key === 'workbench'
          );
          if (workbenchPage) {
            return workbenchPage;
          }
          return authorizedTabs[0];
        }
      }
    }
  });
</script>

<style lang="scss" scoped>
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: #ffffff;
    color: #333333;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    padding: 40px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .loading-spinner {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-top: 4px solid #e53e3e;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transition: all 0.3s ease;

    &.success {
      border-color: transparent;
      animation: none;
      opacity: 0;
    }
  }

  .success-icon {
    position: absolute;
    font-size: 32px;
    color: #4caf50;
    font-weight: bold;
    animation: scaleIn 0.3s ease;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes scaleIn {
    0% {
      transform: scale(0);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .loading-text {
    font-size: 18px;
    color: #333333;
    font-weight: 500;
    text-align: center;
    min-height: 24px;
    transition: all 0.3s ease;
  }

  .progress-container {
    width: 200px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #e53e3e, #f56565, #fc8181);
    border-radius: 3px;
    transition: width 0.3s ease;
    box-shadow: 0 0 8px rgba(229, 62, 62, 0.4);
  }

  .progress-text {
    font-size: 14px;
    color: #666666;
    font-weight: 500;
  }
</style>
