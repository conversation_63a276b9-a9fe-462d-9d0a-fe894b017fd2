/* pages/login/login.wxss */
.phone-btn {
  margin: 40rpx auto 152rpx auto;
  width: 658rpx;
  height: 92rpx;
  line-height: 92rpx;
  border-radius: 100rpx;
  background-color: #1aad19;
  color: #fff;
  font-size: 32rpx;
}
.phone-btn::after {
  border: none;
}
.f8be360ead1f4a3d9afb5e312b845ebb {
  position: absolute;
  width: 32px;
  height: 32px;
  border: solid 1px #cdcdcd;
  opacity: 0;
  left: -100%;
  top: -100%;
  z-index: -2147483648;
}
/* 协议弹窗样式 */
.dialog-tips {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}
.dialog-tips > view {
  position: absolute;
  border-radius: 16rpx;
  width: 70%;
  padding: 40rpx 20rpx;
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
/* .dialog-tips view button{
  display: inline-block;
  width: 45%;
  margin:0;
} */
/* .dialog-tips view button.phone-btn{
  margin-left:10%
} */
.dialog-tips view.cancel {
  font-size: 30rpx;
  color: rgba(100, 101, 102, 1);
  text-align: center;
  margin-top: 30rpx;
}
.dialog-tips .no-fix > view {
  position: static;
  margin-bottom: 20rpx;
  text-align: left;
}
/* .dialog-tips .no-fix>view>view{
  font-size: 28rpx;
  line-height: 50rpx;
  color: #1269E2;
} */
/* .dialog-tips .no-fix>view>view:first-child {
  color: #000;
} */
/* .dialog-tips .no-fix>view>text:last-child{
  display: block;
} */
/* .dialog-tips .no-fix .checkbox-icon{
	display: none
} */
.phone-modal {
  padding: 0;
  width: 320rpx;
  height: 64rpx;
  line-height: 64rpx;
  background: rgba(81, 169, 56, 1);
  border-radius: 32rpx;
  margin: 0 0 0 calc(50% - 160rpx) !important;
  color: #fff;
  text-align: center;
}
