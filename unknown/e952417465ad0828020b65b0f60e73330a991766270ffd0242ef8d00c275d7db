<!--对应wxml文件-->
<template>
  <view class="workbench-container">
    <navi-bar
      show-address="{{true}}"
      title="京小鸽"
      backgroundColor="transparent"
      bindsearchlocation="openSearchLocation"
    ></navi-bar>

    <view
      class="technician-repair"
      bindtap="navigateToTechMaintenance"
      wx:if="{{techMaintenanceBtn}}"
    >
      <view class="repair-header">
        <view class="repair-icon">
          <image src="" class="icon-image" />
        </view>
        <view class="repair-title">技师维修</view>
        <view class="repair-badge">需维修({{ maintainingOrderCount }})</view>
      </view>
    </view>

    <view class="control-tools">
      <view class="title">控车工具</view>
      <view class="main-tools">
        <view wx:for="{{mainToolsButtons}}" wx:key="key" wx:if="{{item.show}}">
          <component is="{{item.component}}" sourceType="workBench" />
        </view>
      </view>
      <t-divider />
      <t-row class="other-tools-row">
        <t-col
          span="6"
          wx:for="{{otherToolsButtons}}"
          wx:key="key"
          wx:if="{{item.show}}"
        >
          <component is="{{item.component}}" sourceType="workBench" />
        </t-col>
      </t-row>
    </view>

    <view class="dignostic-container" wx:if="{{diagnosticToolPart}}">
      <view class="title">诊断工具</view>
      <view class="tool-list">
        <view
          wx:for="{{diagnosticToolsButtons}}"
          wx:key="key"
          wx:if="{{item.show}}"
        >
          <component is="{{item.component}}" sourceType="workBench" />
        </view>
      </view>
    </view>
  </view>
  <search-location
    visible="{{showSearchLocation}}"
    bindclosepopup="closeSearchLocation"
  ></search-location>
  <real-tab-bar />
</template>
<!--对应js文件-->
<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  import { ToolKey, PermissionKey } from '../../shared/utils/constant';
  import { HTTPSTATUSCODE } from '../../shared/api/fetch';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { IndexPageUrl } from 'shared/assets/imageUrl';
  import { sendGlobalEvent } from 'shared/utils/emit';
  import permissionManager from 'shared/utils/permissionManager';
  createPage({
    data: {
      PermissionKey,
      showSearchLocation: false,
      IndexPageUrl,
      selectedTabKey: 'workbench',
      techMaintenanceApi: new TechMaintenanceApi(),
      maintainingOrderCount: 0
    },
    computed: {
      techMaintenanceBtn() {
        return permissionManager.hasPermission(PermissionKey.techMaintenanceBtn);
      },
      mainToolsButtons() {
        return [
          {
            key: 'remoteControl',
            component: 'remote-control-btn',
            show: permissionManager.hasPermission(PermissionKey.remoteControlBtn)
          },
          {
            key: 'callDriver',
            component: 'call-driver',
            show: permissionManager.hasPermission(PermissionKey.callDriverBtn)
          },
          {
            key: 'openGrid',
            component: 'open-grid',
            show: permissionManager.hasPermission(PermissionKey.openGridBtn)
          }
        ];
      },
      otherToolsButtons() {
        return [
          {
            key: 'freeDriving',
            component: 'free-driving',
            show: permissionManager.hasPermission(PermissionKey.freeDrivingBtn)
          },
          {
            key: 'restart',
            component: 'restart-btn',
            show: permissionManager.hasPermission(PermissionKey.restartBtn)
          },
          {
            key: 'followVehicle',
            component: 'follow-vehicle',
            show: permissionManager.hasPermission(PermissionKey.followVehicleBtn)
          },
          {
            key: 'findCar',
            component: 'find-car',
            show: permissionManager.hasPermission(PermissionKey.findCarBtn)
          },
          {
            key: 'callFront',
            component: 'call-front',
            show: permissionManager.hasPermission(PermissionKey.callFrontBtn)
          },
          {
            key: 'loginVehicle',
            component: 'login-vehicle',
            show: permissionManager.hasPermission(PermissionKey.loginVehicleBtn)
          },
          {
            key: 'trackMap',
            component: 'track-map',
            show: permissionManager.hasPermission(PermissionKey.trackMapBtn)
          }
        ];
      },
      diagnosticToolsButtons() {
        return [
          {
            key: 'reportHardwareBtn',
            component: 'report-hardware-btn',
            show: permissionManager.hasPermission(PermissionKey.reportHardwareBtn)
          },
          {
            key: 'reportSoftwareBtn',
            component: 'report-software-btn',
            show: permissionManager.hasPermission(PermissionKey.reportSoftwareBtn)
          }
        ];
      },
      diagnosticToolPart() {
        return permissionManager.hasPermissions(this.diagnosticToolsButtons);
      }
    },
    onShow() {
      if (this.techMaintenanceBtn) {
        this.getRepairOrderList();
      }
    },
    onLoad() {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    methods: {
      navigateToTechMaintenance() {
        wx.navigateTo({
          url: '/pages/techMaintenance/index'
        });
      },
      clickDignosticTool(val: any) {
        console.log(val);
      },
      openSearchLocation(e: any) {
        this.setData({
          showSearchLocation: true
        });
      },
      closeSearchLocation(e: any) {
        this.setData({
          showSearchLocation: false
        });
      },
      handleClickTab(e: any) {
        wx.vibrateShort({
          type: 'heavy',
          success: () => {
            console.log('震动成功');
          },
          fail: () => {
            console.log('震动失败');
          }
        });
        const data = e.currentTarget.dataset;
        const url = data.path;
        this.setData({
          selectedTabKey: data.key
        });
        wx.switchTab({ url });
      },
      async getTodoList() {
        const res: any = await this.notificationApi.getUserTodoList();
        if (res.code === HTTPSTATUSCODE.Success) {
          sendGlobalEvent('update_notice_num', { val: res.data.todoSize });
        }
      },
      async getRepairOrderList() {
        try {
          const res =
            await this.techMaintenanceApi.getServiceStationRequireList();
          if (res.code === HTTPSTATUSCODE.Success) {
            wx.setStorageSync('repairOrderList', res.data);
            this.setData({
              maintainingOrderCount: res.data.maintainingList?.length || 0
            });
          }
        } catch (error) {
          console.error('获取维修单列表失败:', error);
        }
      }
    }
  });
</script>
<!--对应wxss文件-->
<style lang="scss">
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/workbench/work-bench-bg.png?Expires=3868527449&AccessKey=n828WHAXD584pTvi&Signature=q7QDp4B1AuDo1YYwaAHkwzJJvsc%3D')
      no-repeat center/100%;
  }
  .workbench-container {
    height: 100%;
    width: 100%;
  }
  .title {
    margin-bottom: 16px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
  }
  .technician-repair {
    padding: 12px;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.5) 0%,
      rgba(255, 255, 255, 1) 100%
    );
    border-radius: 8px;
    margin: 16px;

    .repair-header {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: relative;

      .repair-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;

        .icon-image {
          width: 100%;
          height: 100%;
        }
      }
      .repair-title {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
      }

      .repair-badge {
        background: #ff4444;
        color: #ffffff;
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
  .control-tools {
    padding: 12px;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.5) 0%,
      rgba(255, 255, 255, 1) 100%
    );
    border-radius: 8px;
    margin-left: 16px;
    margin-right: 16px;
    .main-tools {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      margin-bottom: 16px;
    }
    .other-tools-row {
      .t-col {
        margin-top: 16px;
      }
    }
  }

  .dignostic-container {
    margin-left: 16px;
    margin-right: 16px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    margin-top: 24px;
    padding: 12px 12px 6px 14px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    .tool-list {
      display: flex;
      flex-direction: column;
    }
  }
</style>
<!--对应json文件-->
<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "t-divider": "tdesign-miniprogram/divider/divider",
      "t-row": "tdesign-miniprogram/row/row",
      "t-col": "tdesign-miniprogram/col/col",
      "call-driver": "widgets/commandBtn/callDriverBtn",
      "call-front": "widgets/commandBtn/callFrontBtn",
      "find-car": "widgets/commandBtn/findCarBtn",
      "follow-vehicle": "widgets/commandBtn/followVehicleBtn",
      "free-driving": "widgets/commandBtn/freeDrivingBtn",
      "login-vehicle": "widgets/commandBtn/loginVehicleBtn",
      "track-map": "widgets/commandBtn/trackMapBtn",
      "open-grid": "widgets/commandBtn/openGridBtn",
      "remote-control-btn": "widgets/commandBtn/remoteControlBtn",
      "restart-btn": "widgets/commandBtn/restartBtn",
      "report-software-btn": "widgets/commandBtn/reportSoftwareBtn",
      "report-hardware-btn": "widgets/commandBtn/reportHardwareBtn",
      "search-location": "widgets/searchLocation.mpx",
      "real-tab-bar": "shared/ui/realTabBar.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
