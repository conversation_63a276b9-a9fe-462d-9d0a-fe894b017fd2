import { doRequest, HTTPSTATUSCODE } from 'shared/api/fetch';

export class CommonApi {
  getUserVehicleList = (longitude?: any, latitude?: any) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/common/getSelectVehicleList',
      data: {
        longitude,
        latitude
      }
    };
    return doRequest(requestOptions);
  };

  getHistoryVehicle = () => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/user/getVehicleSearchRecord'
    };
    return doRequest(requestOptions);
  };

  addHistoryVehicle = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/user/addVehicleSearchRecord',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  clearHistoryVehicle = () => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/user/clearVehicleSearchRecord'
    };
    return doRequest(requestOptions);
  };

  checkCanRepair = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/common/require_check',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  checkVehicleTaskRepairOrder = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/freeDrive/checkVehicle',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  getVehicleInfo = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/common/getVehicleInfo',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  loginVehicle = (vehicleName: string, phone: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/login/remote',
      data: {
        vehicleName: vehicleName,
        contact: phone
      }
    };
    return doRequest(requestOptions);

    // B031308：车辆离线，暂不支持远程登录
    // A071412：操作太频繁请稍后再试
    // A070701：该车已有其他人登录
    // A071203：获取用户信息异常
    // A070505：该用户未有该站点权限
  };

  getEnum = () => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/common/enum_list',
      data: {
        keyList: [
          'VEHICLE_STATE_ENUM',
          'ALARM_EVENT_ENUM',
          'SYSTEM_STATE_ENUM',
          'SCHEDULE_STATE_ENUM',
          'DELIVERY_STATUS_ENUM',
          'ISSUE_STATUS_ENUM',
          'VEHICLE_ONLINE_ENUM',
          'VEHICLE_BUSINESS_TYPE_ENUM',
          'ISSUE_OPERATE_RESULT_ENUM',
          'ORDER_SOURCE_TYPE_ENUM',
          'STOP_TYPE_ENUM',
          'SCHEDULE_TASK_ENUM',
          'MINI_MONITOR_REALTIME_STATE_ENUM'
        ]
      }
    };
    doRequest(requestOptions).then((res: any) => {
      if (res.code === HTTPSTATUSCODE.Success) {
        wx.setStorageSync('enumMap', res.data);
      }
    });
  };
}
