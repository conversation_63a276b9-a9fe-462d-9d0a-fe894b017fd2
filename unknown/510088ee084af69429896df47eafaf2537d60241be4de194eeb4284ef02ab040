<template>
  <view class="vehicle-info-card">
    <messageIcon
      wx:if="{{vehicleInfo.iconInfo}}"
      iconInfo="{{vehicleInfo.iconInfo}}"
    />
    <view class="vehicleName">{{ vehicleInfo.vehicleName }}</view>
    <view class="details">
      <text class="time">{{ vehicleInfo.time }}</text>
      <text class="separator">|</text>
      <text class="stationName">{{ vehicleInfo.stationName }}</text>
    </view>
    <slot name="extra-base-info"></slot>
    <view class="divider"></view>
    <slot name="extend-info"></slot>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';

  createComponent({
    options: {
      multipleSlots: true
    },
    properties: {
      vehicleInfo: {
        type: Object,
        value: {
          vehicleName: '',
          time: '',
          stationName: '',
          iconInfo: null
        }
      }
    }
  });
</script>

<style lang="scss" scoped>
  .vehicle-info-card {
    position: relative;
    padding: 16px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    margin-bottom: 8px;

    messageIcon {
      position: absolute;
      top: 0px;
      right: 0px;
    }
    .vehicleName {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
    }
    .details {
      display: flex;
      align-items: center;
      margin-top: 4px;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
      .separator {
        margin: 0 4px;
      }
    }

    .divider {
      height: 1px;
      background-color: rgba(245, 245, 245, 1);
      margin-top: 8px;
      margin-bottom: 6px;
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      messageIcon: 'shared/ui/messageIcon.mpx'
    }
  };
</script>