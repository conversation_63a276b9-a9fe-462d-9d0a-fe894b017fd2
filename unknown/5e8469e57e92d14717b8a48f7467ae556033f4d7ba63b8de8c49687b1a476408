import useLoginStore from 'shared/store/useLoginStore';
/**
 * 时间戳转日期(年-月-日)
 */
const timeToData = (unixtime: any, auto = false, secondFlag = false) => {
  if (unixtime) {
    const d = new Date(unixtime);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    const hour = d.getHours().toString().padStart(2, '0');
    const minute = d.getMinutes().toString().padStart(2, '0');
    const second = d.getSeconds().toString().padStart(2, '0');
    let date = null;
    if (auto) {
      if (secondFlag) {
        date = `${hour}:${minute}:${second}`;
      } else {
        date = `${hour}:${minute}`;
      }
    } else {
      date = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
    return date;
  } else {
    return null;
  }
};

/**
 * 只显示姓氏
 * @param {data} 传入数据
 * 格式：张**
 */
const showFirstName = (name: string) => {
  let newStr;
  if (name?.length === 2) {
    newStr = name.substr(0, 1) + '*';
  } else if (name?.length > 2) {
    let char = '';
    for (let i = 0, len = name.length - 1; i < len; i++) {
      char += '*';
    }
    newStr = name.substr(0, 1) + char;
  } else {
    newStr = name;
  }
  return newStr;
};

/**
 * 加密地址
 */
const secretAddress = (address: string) => {
  if (address?.length <= 5) {
    return address;
  }
  const char = address.substr(0, address.length - 6) + '******';
  return char;
};

/**
 * 从缓存中取出指定枚举列表
 * @param {string} enumKey
 */
const findEnumList = (enumKey: string) => {
  if (!enumKey) {
    return [];
  }
  const enums = wx.getStorageSync('enumMap') || {};
  return (
    enums[enumKey] || [
      {
        code: enumKey,
        name: enumKey
      }
    ]
  );
};

/**
 * get请求拼接参数
 * @param {Object} queryObj
 * @returns
 */
const serializeQueryParis = (queryObj: any) => {
  const paramsQuery = Object.entries(queryObj || {})
    .map((item: any) => {
      const [k, v] = item;
      if (v) {
        return `${encodeURIComponent(k)}=${encodeURIComponent(v)}`;
      }
    })
    .join('&');
  return paramsQuery;
};

/**
 *
 * @param {string} param0 枚举字段
 * @param {string} param1 时间类型
 * @returns
 */
const getEnumEventName = (enumKey: string, code: string) => {
  const enums = wx.getStorageSync('enumMap');
  if (enums && enums[enumKey]) {
    const enumList = enums[enumKey];
    const filteredList = enumList.filter((item: any) => item.code === code);
    if (filteredList?.length > 0) {
      return filteredList[0]?.name;
    }
  }
  return null;
};

const dateNum = (date: any) => {
  const [lastHour, lastmMin] = date.split(':');
  return 60 * lastHour + lastmMin * 1;
};

const formatTime = (time: any, format: any) => {
  const date = new Date(time);
  const year = '0' + date.getFullYear();
  const month = '0' + (date.getMonth() + 1);
  const day = '0' + date.getDate();
  const hour = '0' + date.getHours();
  const minute = '0' + date.getMinutes();
  const second = '0' + date.getSeconds();
  if (format.includes('y')) {
    format = format.replace(/y+/, year.slice(-format.match(/y+/)[0].length));
  }
  if (format.includes('M')) {
    format = format.replace(/M+/, month.slice(-format.match(/M+/)[0].length));
  }
  if (format.includes('d')) {
    format = format.replace(/d+/, day.slice(-format.match(/d+/)[0].length));
  }
  if (format.includes('h')) {
    format = format.replace(/h+/, hour.slice(-format.match(/h+/)[0].length));
  }
  if (format.includes('m')) {
    format = format.replace(/m+/, minute.slice(-format.match(/m+/)[0].length));
  }
  if (format.includes('s')) {
    format = format.replace(/s+/, second.slice(-format.match(/s+/)[0].length));
  }
  return format;
};

const formatTimestamp = (timestamp: any, type: 'xxhxxmin' | 'hh:mm:ss') => {
  // 将时间戳转换为秒
  const totalSeconds = Math.floor(timestamp / 1000);
  // 计算小时、分钟和秒
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = (totalSeconds % 3600) % 60;
  // 格式化为两位数
  const formattedHours = String(hours).padStart(2, '0');
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(seconds).padStart(2, '0');
  if (type === 'xxhxxmin') {
    if (hours >= 24) {
      return '24h+';
    } else if (hours < 24 && hours > 0) {
      return `${formattedHours}h${formattedMinutes}min`;
    } else if (minutes > 0) {
      return `${formattedMinutes}min`;
    } else {
      return '<1min';
    }
  } else if (type === 'hh:mm:ss') {
    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
  }
};

const urlObject = (url: string) => {
  const params = url.slice(url.indexOf('?') + 1, url.length);
  const newParams = params.split('&');
  const urlObj: any = new Object();
  for (let i = 0; i < newParams.length; i++) {
    const newArray = newParams[i].split('=');
    urlObj[newArray[0]] = newArray[1];
  }
  return urlObj;
};

const showToast = (message: string) => {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  });
};

/**
 * 生成追踪ID
 * @returns string
 */
const formatTraceId = () => {
  let num = '';
  for (let i = 0; i < 6; i++) {
    num += Math.floor(Math.random() * 10);
  }
  return new Date().getTime() + num;
};

const isNullObject = (obj: any) => {
  if (!obj) {
    return true;
  }
  return Object.keys(obj).length === 0;
};

const isEmpty = (param: any) => {
  if (Array.isArray(param) || typeof param === 'string') {
    return param.length === 0;
  }
  return true;
};

const checkUpdate = () => {
  const updateManager = wx.getUpdateManager();
  updateManager.onCheckForUpdate(function (res) {
    console.log('versionBack==>', res.hasUpdate);
  });
  updateManager.onUpdateReady(function () {
    wx.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      success: function (res) {
        if (res.confirm) {
          updateManager.applyUpdate();
        }
      }
    });
  });
  updateManager.onUpdateFailed(function () {
    wx.showToast({
      title: '新版本下载失败,请检查网络!',
      icon: 'none'
    });
  });
};
const checkLogin = () => {
  const plugin = requirePlugin('loginPlugin');
  const k = plugin.getPtKey();
  if (!k) {
    wx.showModal({
      title: '获取登录态信息失败',
      content: '登录态信息获取失败，请重新登录',
      confirmText: '重新登录',
      success: res => {
        if (res.confirm) {
          toLogin();
        }
      }
    });
  } else {
    wx.switchTab({ url: '/pages/my/index' });
  }
};
const toLogin = () => {
  const returnPage = encodeURIComponent('/pages/transitionPage/index');
  const pageType = 'redirectTo';
  wx.redirectTo({
    url: `/pages/login/index/index?returnPage=${returnPage}&pageType=${pageType}`
  });
};
const getCurrentRoute = () => {
  try {
    const pages = getCurrentPages();
    console.log('另一个方法获取');
    console.log('当前页面列表:', pages);

    // skyline模式下可能页面栈为空，添加容错处理
    if (!pages || pages.length === 0) {
      console.warn(
        'getCurrentPages() 返回空数组，可能在skyline模式下页面还未完全初始化'
      );
      return undefined;
    }

    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage?.route;

    if (!currentRoute) {
      console.warn('当前页面route为空，可能在skyline模式下页面还未完全初始化');
    }

    return currentRoute;
  } catch (error) {
    console.error('获取当前路由失败:', error);
    return undefined;
  }
};
const getFormattedDate = () => {
  const date = new Date();

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const translateSceneSignal = (data: number) => {
  let mapStr = '';
  const SCENE_SIGNAL: any = new Map([
    ['0,2.0', '偏移'],
    ['2.0,3.0', '准确性差'],
    ['3.0,3.5', '准确性较差'],
    ['3.5,4.5', '较准确'],
    ['4.5,5.0', '准确']
  ]);

  if (data === 0) {
    return '定位偏移';
  }
  for (const [key, value] of SCENE_SIGNAL) {
    const limit = key?.split(',');
    const [left, right] = limit;
    if (data > Number(left) && data <= Number(right)) {
      mapStr = value;
      break;
    }
  }

  return mapStr;
};

const getPermissionById = (id: any) => {
  const appResourceInfoList = wx.getStorageSync('appResourceInfoList');
  if (appResourceInfoList) {
    const findIndex = appResourceInfoList.findIndex(
      (item: any) => item.code === id
    );
    return findIndex > -1;
  }
  return false;
};

const isNumber = (value: any): boolean => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'number') return !isNaN(value);
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return !isNaN(parsed) && isFinite(parsed);
  }
  return false;
};

const convertSpeedMsToKmh = (
  speedMs: any,
  numberOfDecimalPlaces?: number
): string => {
  if (isNumber(speedMs)) {
    if (Number(speedMs) === 0) {
      return '0 km/h';
    }
    if (numberOfDecimalPlaces !== undefined) {
      return `${(speedMs * 3.6).toFixed(numberOfDecimalPlaces)} km/h`;
    }
    return `${speedMs * 3.6} km/h`;
  } else {
    return '--';
  }
};

export {
  dateNum,
  timeToData,
  secretAddress,
  findEnumList,
  serializeQueryParis,
  getEnumEventName,
  formatTime,
  showFirstName,
  urlObject,
  showToast,
  formatTraceId,
  isNullObject,
  isEmpty,
  checkUpdate,
  checkLogin,
  formatTimestamp,
  getFormattedDate,
  translateSceneSignal,
  getPermissionById,
  toLogin,
  isNumber,
  convertSpeedMsToKmh,
  getCurrentRoute
};
