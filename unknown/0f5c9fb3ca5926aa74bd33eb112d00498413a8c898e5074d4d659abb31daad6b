import { getAppCode } from 'shared/utils/config';
import { doRequest } from './fetch';

class LoginApi {
  public async fetchUserInfoAfterLogin(): Promise<any> {
    const appCode = getAppCode();
    const requestOptions: RequestOptions = {
      url: '/authentication/getLoginUserInfo',
      method: 'POST',
      data: {
        appCode
      }
    };
    return doRequest(requestOptions);
  }

  public async getToken(): Promise<any> {
    const requestOptions: RequestOptions = {
      url: '/authentication/h5/getRemoteControlToken',
      method: 'POST'
    };
    return doRequest(requestOptions);
  }
}

export default LoginApi;
