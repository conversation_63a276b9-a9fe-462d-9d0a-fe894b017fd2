<template>
  <!-- 占位组件，用于 skyline 模式下的底部占位 -->
  <view style="height: {{tabBarHeight + safeAreaBottom}}px"></view>

  <!-- 真实的 tab-bar -->
  <view
    class="real-tab-bar"
    style="bottom: {{safeAreaBottom}}px; height: {{tabBarHeight}}px;"
  >
    <view
      wx:for="{{authorizedTabs}}"
      wx:key="key"
      class="tab-btn {{item.showBadge ? 'message' : ''}}"
      bindtap="handleClickTab"
      data-path="{{item.path}}"
      data-key="{{item.key}}"
    >
      <view class="notice-num" wx:if="{{item.showBadge && noticeNum}}">{{
        noticeNum
      }}</view>
      <image
        style="width: 25px; height: 25px; margin-bottom: 7px"
        src="{{selectedTabKey === item.key ? IndexPageUrl[item.iconSelected] : IndexPageUrl[item.iconUnselected]}}"
      ></image>
      <view
        class="{{selectedTabKey === item.key ? 'tab-text-selected' : 'tab-text-unselected'}}"
        >{{ item.text }}</view
      >
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { IndexPageUrl } from 'shared/assets/imageUrl';
  import { NotificationApi } from 'shared/api/notification';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { addGlobalEventListener } from '../utils/emit';
  import { getCurrentRoute } from '../utils/utils';
  import { TAB_BAR_PAGES } from '../utils/constant';

  let allAuthorizedTabs: any = [];

  createComponent({
    data: {
      IndexPageUrl: IndexPageUrl,
      selectedTabKey: '',
      fetchApi: new NotificationApi(),
      noticeNum: null,
      tabBarHeight: 90,
      safeAreaBottom: 0
    },
    computed: {
      authorizedTabs() {
        console.log('执行 authorizedTabs 计算属性');
        return this.getAuthorizedTabs();
      }
    },
    lifetimes: {
      created() {
        addGlobalEventListener('update_notice_num', val => {
          const newNoticeNum = val?.val;
          console.log('更新消息数量:', newNoticeNum);
          if (newNoticeNum !== undefined && newNoticeNum !== null) {
            if (newNoticeNum > 0) {
              wx.setStorageSync('cached_notice_num', newNoticeNum);
            } else {
              wx.removeStorageSync('cached_notice_num');
            }
            this.setData({
              noticeNum: newNoticeNum
            });
          }
        });
      },
      attached() {
        console.log('======TabBar组件的attached======', this.data);
        //  this.initSafeArea();
        this.restoreNoticeNum();
      }
    },
    pageLifetimes: {
      show() {
        this.updateSelectedTabKey();
      }
    },
    methods: {
      // initSafeArea() {
      //   const systemInfo = wx.getSystemInfoSync();
      //   console.log('获取系统信息:', systemInfo);
      //   this.setData({
      //     safeAreaBottom: systemInfo.safeArea?.bottom
      //       ? systemInfo.screenHeight - systemInfo.safeArea.bottom
      //       : 0
      //   });
      // },
      updateSelectedTabKey() {
        console.log('======父页面的show======');
        console.log('此时的selectedTabKey:', this.selectedTabKey);
        const currentRoute = getCurrentRoute();
        console.log('当前页面路由:', currentRoute);
        const routeParts = currentRoute?.split('/') || [];
        const routeKey =
          routeParts.length >= 3 ? routeParts[routeParts.length - 2] : '';
        console.log('提取的路由关键字:', routeKey);
        if (routeKey === this.data.selectedTabKey) {
          return;
        }
        this.setData({
          selectedTabKey: routeKey
        });
      },
      restoreNoticeNum() {
        try {
          const cachedNoticeNum = wx.getStorageSync('cached_notice_num');
          console.log('尝试从缓存中恢复消息数量:', cachedNoticeNum);
          if (
            cachedNoticeNum !== undefined &&
            cachedNoticeNum !== null &&
            cachedNoticeNum !== ''
          ) {
            this.setData({
              noticeNum: cachedNoticeNum
            });
          }
        } catch (error) {
          console.error('恢复消息数量失败:', error);
        }
      },
      getAuthorizedTabs() {
        console.log('执行 getAuthorizedTabs 方法');
        if (allAuthorizedTabs?.length > 0) {
          console.log('使用全局变量 allAuthorizedTabs');
          return allAuthorizedTabs;
        }
        // 尝试从缓存获取
        const cachedTabs = wx.getStorageSync('authorizedTabs') || [];
        if (cachedTabs.length > 0) {
          console.log('从缓存中获取 authorizedTabs');
          console.log('给全局状态赋值');
          allAuthorizedTabs = cachedTabs;
          return cachedTabs;
        }
        console.warn('没有找到任何授权的 TabBar 页面配置，返回空数组');
        return [];
      },
      handleClickTab(e: any) {
        wx.vibrateShort({
          type: 'heavy',
          success: () => {
            console.log('震动成功');
          },
          fail: () => {
            console.log('震动失败');
          }
        });
        const data = e.currentTarget.dataset;
        const url = data.path;
        console.log('点击TabBar，数据:', data, '跳转路径:', url);
        this.setData({
          selectedTabKey: data.key
        });
        getApp().globalData.singleVehicle = null;
        wx.switchTab({ url });
      }
    }
  });
</script>

<style lang="scss">
  .real-tab-bar {
    bottom: 0;
    width: 100%;
    pointer-events: auto;
    position: absolute;
    z-index: 9999;
    height: 90px;
    border-radius: 15px 15px 0 0;
    background-color: #ffff;
    box-shadow: 0px -1px 8px rgb(165 165 165 / 55%);
    display: flex;
    justify-content: space-around;
    align-items: center;
    .tab-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      width: 80px;
      height: 80px;

      &.message {
        position: relative;
        .notice-num {
          top: 10px;
          position: absolute;
          min-width: 20px;
          min-height: 20px;
          border-radius: 10px;
          background-color: red;
          color: white;
          font-size: 12px;
          line-height: 20px;
          text-align: center;
          z-index: 99;
          left: 42px;
        }
      }
    }
    .tab-text-unselected,
    .tab-text-selected {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
    }
    .tab-text-selected {
      color: rgba(26, 26, 26, 1);
    }
  }
</style>
<script name="json">
  module.exports = {
    component: true
  };
</script>
