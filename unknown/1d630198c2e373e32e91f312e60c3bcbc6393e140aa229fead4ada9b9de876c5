import { doRequest } from 'shared/api/fetch';
import {
  AccidentJudge,
  AccidentType,
  HandleMethod
} from 'shared/utils/constant';
export class AccidentDetial {
  getAccidentDetail = async (messageId: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/message/getAccidentDetail',
      data: { messageId }
    };
    return doRequest(requestOptions);
  };

  getRepairDetail = async (messageId: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/message/getRepairDetail',
      data: { messageId }
    };
    return doRequest(requestOptions);
  };

  accidentOperate = async (params: {
    messageId: string;
    accidentNo: string;
    operateType: 'REPORT' | 'REJECT';
  }) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/message/accidentOperate',
      data: params
    };
    return doRequest(requestOptions);
  };

  accidentGenerate = async (params: {
    messageId: string;
    accidentNo: string;
    operateType: 'SAVE' | 'SUBMIT';
    handleMethod: HandleMethod | null;
    compensated: boolean | null;
    amount: string | null;
    accidentType: AccidentType | null;
    accidentJudge: AccidentJudge | null;
    reason: string;
    attachmentList: any[];
    isReportVehicleNet: boolean | null;
  }) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/message/accidentGenerate',
      data: params
    };
    return doRequest(requestOptions);
  };

  repairOperate = async (params: {
    messageId: string;
    repairNumber: string;
    operateType: 'CONFIRM' | 'KNOW';
  }) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/message/repairOperate',
      data: params
    };
    return doRequest(requestOptions);
  };

  getVehicleInfo = async (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/common/getVehicleInfo',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  getAccidentReportDetail = async (accidentNo: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/message/getAccidentReportDetail',
      data: { accidentNo }
    };
    return doRequest(requestOptions);
  };
}
