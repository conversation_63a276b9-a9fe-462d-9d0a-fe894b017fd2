<template>
  <view class="label-container" style="width: {{iconInfo.imageWidth}}">
    <image
      class="image"
      src="{{imageUrl}}"
      style="width: {{iconInfo.imageWidth}}"
    />
    <view
      class="label"
      style="color: {{iconInfo.labelColor === 'red' ? 'rgba(255,70,70,1)' : iconInfo.labelColor === 'green' ? 'rgba(18,179,93,1)' : iconInfo.labelColor}}"
    >
      {{ iconInfo.label }}
    </view>
  </view>
</template>

<script>
  import { createComponent } from '@mpxjs/core';
  import { Notification } from 'shared/assets/imageUrl';

  createComponent({
    properties: {
      iconInfo: {
        label: String,
        imageUrl: {
          type: String,
          value: ''
        },
        labelColor: 'red' | 'green' | String,
        imageWidth: String
      }
    },
    data: { Notification: Notification },
    computed: {
      imageUrl() {
        if (this.iconInfo.imageUrl) {
          return this.iconInfo.imageUrl;
        }
        if (this.iconInfo.labelColor === 'red') {
          return this.Notification.CollisionMsgIcon;
        } else if (this.iconInfo.labelColor === 'green') {
          return this.Notification.RepairMsgIcon;
        }
        return '';
      }
    }
  });
</script>

<style lang="scss">
  .label-container {
    position: relative;
    .image {
      height: 25px;
    }
    .label {
      position: absolute;
      top: 4px;
      right: 8px;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true
  };
</script>