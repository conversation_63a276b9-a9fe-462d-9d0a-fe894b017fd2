<template>
  <web-view
    id="webview"
    wx:if="{{webviewUrl}}"
    src="{{webviewUrl}}"
    bindmessage="onWebViewMessage"
  ></web-view>
</template>

<style lang="scss">
  .container {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>

<script>
  import { createPage } from '@mpxjs/core';
  import { getWebviewHost } from 'shared/utils/config';
  import { doRequest } from 'shared/api/fetch';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { getAppCode } from 'shared/utils/config';

  createPage({
    data() {
      return {
        webviewUrl: ''
      };
    },
    methods: {
      onWebViewMessage(event) {
        const message = event.detail.data;
        console.log('event', event);
        console.log(message);
        getApp().globalData.anyDriveInfo = message[0];
      }
    },
    onLoad(query) {
      const plugin = requirePlugin('loginPlugin');
      const k = plugin.getPtKey();
      const name = wx.getStorageSync('userName');
      const userLocation = wx.getStorageSync('userLocation');
      this.webviewUrl = `${getWebviewHost()}/anyDrive?k=${k}&userName=${name}&latitude=${
        userLocation.latitude
      }&longitude=${userLocation.longitude}`;
      console.log(this.webviewUrl);
    }
  });
</script>

<script name="json">
  module.exports = {
    navigationBarTitleText: '选择任意点',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black'
  };
</script>
