/* eslint-disable @typescript-eslint/no-non-null-assertion */
/* eslint-disable camelcase */
/* eslint-disable prefer-promise-reject-errors */
import mpx from '@mpxjs/core';
import mpxFetch from '@mpxjs/fetch';
import { getLOPDN, getLOPdomain } from 'shared/utils/config';
import { toLogin } from 'shared/utils/utils';

mpx.use(mpxFetch);

const request = (params: any, autoTip: any) => {
  return new Promise((resolve, reject) => {
    const { url, method, data, headers } = params;
    const trueHeader = Object.assign(
      {},
      {
        'Content-Type': 'application/json',
        'LOP-DN': getLOPDN(),
        authType: 2,
        client: 'm',
        appid: 2435,
        ticket_type: 'mix',
        ...headers
      }
    );
    const interval = setInterval(() => {
      clearInterval(interval);
      const plugin = requirePlugin('loginPlugin');
      const k = plugin.getPtKey();
      trueHeader.Cookie = `pt_key=${k}`;
      const requestData = {
        url: `${getLOPdomain()}${url}`,
        method,
        header: trueHeader,
        data: data && JSON.stringify(data)
      };
      mpx.xfetch
        .fetch(requestData)
        .then(async (res: any) => {
          if (res.statusCode < 200 || res.statusCode >= 300) {
            // token过期
            if (res.statusCode === 401) {
              // 跳转登录页
              toLogin();
            } else if (res.statusCode === 403) {
              // TODO: 跳转无权限页面
            } else {
              toLogin();
              if (autoTip) {
                wx.showToast({
                  title:
                    (res.data && res.data.message) ||
                    '数据加载出现错误，请刷新页面重试！',
                  icon: 'none'
                });
              }
              reject({
                code: res.statusCode,
                data: res.data,
                message: res.message
              });
            }
          } else {
            resolve({
              code: res.data.code,
              data: res.data.data,
              errorCode: res.data.code,
              message: res.data.message
            });
          }
        })
        .catch(e => {
          if (autoTip) {
            wx.showToast({
              title: '请求数据失败!',
              icon: 'none'
            });
          }
          reject({
            code: 500,
            status: 500,
            message: '网络错误',
            requestId: ''
          });
        });
    }, 100);
  });
};

/**
 * @param 参数格式形如
 * {
 *   url: any;
 *   method: any;
 *   data?: any;
 * }
 */
export const doRequest = async (
  params: {
    url: any;
    method: any;
    data?: any;
    headers?: any;
    useLOP?: boolean;
  },
  autoTip = true
) => {
  return new Promise((resolve, reject) => {
    request(params, autoTip)
      .then(values => {
        resolve(values);
      })
      .catch(err => {
        console.error(err);
        reject({
          httpCode: 500,
          status: 500,
          message: '网络错误'
        });
      });
  });
};

export const HTTPSTATUSCODE = {
  Success: '0000',
  Timeout: '3001',
  ServiceException: '5001',
  ServiceError: '4001'
};

export const scheduleCode = {
  Success: '0'
};
