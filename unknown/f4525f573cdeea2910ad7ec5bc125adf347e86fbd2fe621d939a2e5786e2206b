<template>
  <web-view
    id="webview"
    wx:if="{{webviewUrl}}"
    src="{{webviewUrl}}"
    bindmessage="onWebViewMessage"
    allow="autoplay; playsInline"
  ></web-view>
</template>

<style lang="scss">
  .container {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>

<script>
  import { createPage } from '@mpxjs/core';
  import { getWebviewHost } from 'shared/utils/config';
  createPage({
    data() {
      return {
        vehicleName: '',
        webviewUrl: ''
      };
    },
    methods: {
      onWebViewMessage(event) {
        const message = event.detail.data;
        if (message && message.action === 'goBack') {
          wx.navigateBack({
            delta: 1
          });
        }
      }
    },
    onLoad(query) {
      const plugin = requirePlugin('loginPlugin');
      const k = plugin.getPtKey();
      this.vehicleName = query.vehicleName;
      const name = wx.getStorageSync('userName');
      this.webviewUrl = `${getWebviewHost()}/realtime?vehicleName=${
        query.vehicleName
      }&userName=${name}&k=${k}`;
      console.log(this.webviewUrl);
    }
  });
</script>

<script name="json">
  module.exports = {
    navigationStyle: 'custom',
    pageOrientation: 'landscape'
  };
</script>
