/**
 *
 * WARNING: 请谨慎修改，若导致登录业务不可用，业务自行承担风险
 *
 */
import util from '../util.js';
const fm = require('../fm.min.js');
const config = util.getLoginConfig();
const plugin = requirePlugin('loginPlugin');

Page({
  data: {
    config,
    stopClick: false,
    checkboxChecked: !config.author,
  },
  smsloginResListener(res = {}) {
    if (this.data.checkboxChecked || this.data.userBtn) {
      util.handleJump(res.detail);
    } else {
      this.showLoad('userBtn');
    }
  },
  showLoad(type) {
    const {
      config = {},
      config: { appid: LoginAppid },
    } = this.data;
    const showType = type.detail || type || 'smsBtn';
    // 显示弹窗提示勾选协议
    this.setData({ selfTipsDialog: true, [showType]: true });
    // 登录注册按钮类型
    plugin.expoLog({
      eid: 'WXLogin_AgreementToast',
      pageId: 'WXLogin_LoginPage',
      eparam: {
        LoginAppid,
        AuthType: showType == 'userBtn' ? 'Login' : 'QuickLogin',
      },
    });
  },
  changeCheckbox(e) {
    this.setData({ checkboxChecked: e.detail });
  },
  frequentClick() {
    if (!this.data.checkboxChecked) {
      this.showLoad('smsBtn');
    } else {
      wx.showToast({
        icon: 'none',
        duration: 3000,
        title: '点击频繁，请选择其他登录方式',
      });
    }
  },
  quickLoginHandler(event) {
    const {
      target = {},
      target: {
        dataset = {},
        dataset: { lgid = '' },
      },
    } = event || {};
    const {
      config: { appid: LoginAppid },
    } = this.data;
    if (lgid == 'fbd') {
      this.frequentClick();
    } else {
      plugin.baseClickLog({
        event,
        eid: 'WXLogin_QuickLogin',
        pageId: 'WXLogin_LoginPage',
        eparam: {
          LoginAppid,
          LoginButton: lgid == 'mdl' ? 'Agreement' : 'LoginPage',
        },
      });
    }
  },
  getPhoneNumber(event = {}) {
    const { stopClick } = this.data;
    if (stopClick) {
      wx.showToast({
        icon: 'none',
        duration: 3000,
        title: '重复点击，请稍后再试或选择其他登录方式',
      });
      return;
    }
    this.setData({ stopClick: true });
    const {
      detail = {},
      detail: { iv, encryptedData, errno = -1 },
    } = event;
    if ('1400001' === `${errno}`) {
      /**
       * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html
       *
       * 小程序快捷登录已欠费，默认跳转到短信验证码/账号密码登录页
       */
      plugin
        .loginRequest({})
        .then(() =>
          util.handleJump({ pluginUrl: plugin.formatPluginPage('main') })
        )
        .catch(() =>
          wx.showToast({ icon: 'none', title: '网络异常，请稍后再试' })
        );
      return;
    }
    plugin.clickLog({
      event,
      eid: 'WLogin_Diversion_Wechat',
    });
    if (!iv || !encryptedData) {
      this.setData({ stopClick: false });
      return;
    }
    wx.showLoading({
      title: '加载中',
    });
    this.setData({
      detail,
    });
    this.mobileLogin();
    plugin.clickLog({
      event,
      eid: 'WLogin_DiversionWechat_Allow',
    });
  },
  mobileLogin() {
    let { code, detail } = this.data;
    let { iv, encryptedData } = detail;
    if (!code || !iv || !encryptedData) return;
    const startClick = () => {
      wx.hideLoading();
      this.setData({
        stopClick: false,
      });
    };
    plugin
      .WXMobileLogin({
        iv,
        encryptedData,
        code,
      })
      .then(res => {
        if ([32, 33].indexOf(res.err_code) >= 0) return plugin.loginRequest({});
        // 风控提示用户去浏览器解除 重新获取code
        if (res.err_code == 124) return this.getWxcode();
        return res;
      })
      .then(res => {
        let { pt_key, rsa_modulus, guid } = res;
        if (!pt_key && rsa_modulus && guid) {
          // login 返回
          // 联合登录失败跳转普通登录
          res.pluginUrl = plugin.formatPluginPage('main', 'wxl=1');
        }
        // startClick()
        util.handleJump(res);
      })
      .catch(res => {
        startClick();
        console.jdLoginLog(res);
      });
  },
  getWxcode() {
    wx.login({
      success: (res = {}) => {
        this.setData({
          code: res.code,
        });
      },
    });
  },
  onLoad(options) {
    // 登录成功回调
    plugin.onLoginSuccess(data => {
      console.info(data, 'login/index 登录成功');
    }, 'IndexPage');
    let { riskFail } = options;
    this.setData({
      config: util.getLoginConfig(options),
    });
    //风控失败不重置缓存
    if (!riskFail) {
      util.setLoginParamsStorage(options);
    }
    plugin.setLog();
    util.setCustomNavigation();
    this.getWxcode();
    this.setFingerData();
  },
  // onUnload() {
  // TODO: 业务根据实际情况决定是否需要卸载当前页面登录成功监听器
  // plugin.offLoginSuccess('IndexPage');
  // },
  /**
   * 设备指纹具体文档说明：https://derisks.jdfmgt.com/home/<USER>
   */
  setFingerData() {
    fm.config(this, {
      // 固定值，请勿修改！
      bizKey: plugin.bizKey,
      // 是否关闭 canvas, 默认 false
      //  true：不启用canvas；false：启用canvas（即添加的<canvas>生效），建议使用时添加canvas
      canvasOff: false,
    });
    fm.init({
      // TODO: 业务按需传入参数，建议传入 openid 提升设备指纹准确度
      openid: '',
      // 设置为true，方法将不再调用 wx.login 方法，增加运行速度200ms左右
      codeOff: true,
    });
    fm.getEid((res = {}) => {
      plugin.setJdStorageSync('finger_tk', res.tk);
    });
  },
  // 拒绝协议
  reject() {
    this.setData({ selfTipsDialog: false, userBtn: false, smsBtn: false });
  },
  onShow() {
    const { appid: LoginAppid, hiddenLoginType } = util.getLoginConfig();
    plugin.pvLog({
      ext: { LoginAppid },
      pageId: 'WXLogin_LoginPage',
    });
    if (hiddenLoginType != 2) {
      // 未隐藏快捷登录
      plugin.expoLog({
        eparam: { LoginAppid },
        pageId: 'WXLogin_LoginPage',
        eid: 'WXLogin_QuickLoginButton',
      });
    }
  },
});
