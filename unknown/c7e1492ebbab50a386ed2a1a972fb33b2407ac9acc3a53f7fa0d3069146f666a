<template>
  <view class="contact-info-card">
    <view class="contact-content">
      <view class="user-avatar">
        <image src="{{TechMaintainUrl.JdAvatar}}" class="avatar-image" />
      </view>

      <view class="user-info">
        <text class="username">{{ reporterInfo.name }}</text>

        <view class="contact-details">
          <view class="contact-item">
            <view class="contact-icon">
              <image src="{{TechMaintainUrl.Message}}" class="icon-image" />
            </view>
            <text class="contact-text email">{{ reporterInfo.email }}</text>
          </view>

          <view class="contact-item">
            <view class="contact-icon" bindtap="handleMakeCall">
              <image
                src="{{TechMaintainUrl.Phone}}"
                class="icon-image phone-icon"
              />
            </view>
            <text class="contact-text phone">{{ currentPhoneNumber }}</text>
            <view class="chart-icon" bindtap="handleEyeToggle">
              <image
                src="{{ isPhoneVisible ? TechMaintainUrl.EyeClose : TechMaintainUrl.EyeOpen }}"
                class="icon-image"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  interface ReporterInfo {
    name: string;
    email: string;
    phone: string;
    avatar: string;
  }

  createComponent({
    properties: {
      reporterInfo: {
        type: Object,
        value: {
          name: '',
          email: '',
          phone: '',
          avatar: ''
        }
      },
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      TechMaintainUrl,
      fetchApi: new TechMaintenanceApi(),
      isPhoneVisible: false, // 默认电话号码是隐藏的（脱敏的）
      realPhoneNumber: '', // 存储真实的电话号码
      loading: false
    },
    computed: {
      currentPhoneNumber() {
        return this.isPhoneVisible
          ? this.realPhoneNumber
          : this.reporterInfo.phone;
      }
    },
    methods: {
      async handleEyeToggle() {
        if (this.loading) return;

        // 如果已经显示真实号码，直接切换到隐藏状态
        if (this.isPhoneVisible) {
          this.setData({
            isPhoneVisible: false
          });
          return;
        }

        // 如果还没有获取过真实号码，调用接口
        if (!this.realPhoneNumber) {
          try {
            this.setData({ loading: true });

            const res = await this.fetchApi.getRequireReportPhone(
              this.orderNumber
            );

            if (res.code === HTTPSTATUSCODE.Success) {
              this.setData({
                realPhoneNumber: res.data.reportPhone,
                isPhoneVisible: true
              });
            } else {
              wx.showToast({
                title: res.message || '获取电话号码失败',
                icon: 'none',
                duration: 2000
              });
            }
          } catch (error) {
            console.error('获取真实电话号码失败:', error);
            wx.showToast({
              title: '获取电话号码失败',
              icon: 'none',
              duration: 2000
            });
          } finally {
            this.setData({ loading: false });
          }
        } else {
          // 如果已经有真实号码，直接显示
          this.setData({
            isPhoneVisible: true
          });
        }
      },
      handleMakeCall() {
        const phoneNumber = this.currentPhoneNumber;
        if (phoneNumber && this.isPhoneVisible) {
          wx.makePhoneCall({
            phoneNumber,
            success() {
              console.log('拨打电话成功');
            },
            fail() {
              wx.showToast({
                title: '拨打电话失败',
                icon: 'none',
                duration: 2000
              });
            }
          });
        } else {
          wx.showToast({
            title: '无法拨打电话',
            icon: 'none',
            duration: 2000
          });
        }
      }
    }
  });
</script>

<style lang="scss">
  .contact-info-card {
    background: #ffffff;
    border-radius: 8px;

    .contact-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .user-avatar {
        width: 48px;
        height: 48px;
        overflow: hidden;
        flex-shrink: 0;

        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .user-info {
        flex: 1;
        min-width: 0;

        .username {
          font-size: 14px;
          font-weight: 600;
          color: #333333;
          display: block;
          margin-bottom: 8px;
        }

        .contact-details {
          display: flex;
          align-items: center;
          gap: 6px;

          .contact-item {
            display: flex;
            align-items: center;
            gap: 4px;

            .contact-icon {
              width: 16px;
              height: 16px;
              flex-shrink: 0;

              .icon-image {
                width: 100%;
                height: 100%;
                object-fit: contain;

                &.phone-icon {
                  filter: hue-rotate(10deg) saturate(1.2);
                }
              }
            }

            .contact-text {
              font-size: 12px;
              color: #666666;
              white-space: nowrap;

              &.email {
                color: #666666;
              }

              &.phone {
                color: #fa2c19;
                font-weight: 500;
              }
            }
            .chart-icon {
              width: 16px;
              height: 16px;
              flex-shrink: 0;

              .icon-image {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }
            }
          }
        }
      }
    }
  }

  // 响应式适配
  @media (max-width: 480px) {
    .contact-info-card {
      .contact-content {
        .user-info {
          .contact-details {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .chart-icon {
              margin-left: 0;
              align-self: flex-end;
            }
          }
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true
  }
</script>