.privacy-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 10002;
  animation: fadeIn 0.3s forwards;
}
.privacy-popup-screen-dialog {
  width: 750rpx;
  border-radius: 24rpx 24rpx 0 0;
  height: auto;
  position: absolute;
  left: 0;
  bottom: 0;
  background: #fff;
  padding: 60rpx 52rpx 20rpx 52rpx;
  box-sizing: border-box;
  transform: translateY(100%);
  animation: moveOut 0.3s linear 0.3s 1 forwards;
}
.privacy-popup-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
}
.privacy-popup-screen-dialog__hd {
  display: flex;
  align-items: center;
}
.privacy-popup-screen-dialog__title {
  font-size: 28rpx;
}
.privacy-popup-screen-dialog__bd {
  margin-top: 52rpx;
}
.privacy-popup-screen-dialog__tip1 {
  font-size: 32rpx;
  color: #000;
  margin-bottom: 12rpx;
}
.privacy-popup-screen-dialog__tip2 {
  font-size: 28rpx;
  color: #888;
  margin-bottom: 12rpx;
}
.privacy-popup-text-bule {
  color: #576b95;
}
.privacy-popup-screen-dialog__tip3 {
  font-size: 28rpx;
  color: #888;
}
.privacy-popup-screen-dialog__ft {
  margin-top: 60rpx;
  padding-bottom: 68rpx;
}
.privacy-popup-screen-dialog__btn-area {
  display: flex;
  justify-content: center;
}
.privacy-popup-ui-btn {
  width: 230rpx;
  height: 92rpx;
  margin: 0 16rpx;
  text-align: center;
  line-height: 92rpx;
  font-size: 32rpx;
  border-radius: 16rpx;
  border: none !important;
}
.disagree-btn {
  background: #f2f2f2 !important;
  color: #07c160 !important;
}
.disagree-btn-hover::after {
  content: '';
  background: rgba(0, 0, 0, 0.1);
}
.agree-btn-hover::after {
  content: '';
  background: rgba(0, 0, 0, 0.1);
}
.agree-btn {
  background: #07c160 !important;
  color: #fff !important;
}
@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes moveOut {
  0% {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
@keyframes moveOut {
  0% {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
