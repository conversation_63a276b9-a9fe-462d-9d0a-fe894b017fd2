<template>
  <view class="bump-detail-container">
    <navi-bar
      show-address="{{false}}"
      title="事故消息"
      show-return="true"
      back-url="{{'/pages/notification/index'}}"
    ></navi-bar>
    <scroll-view class="main-container" type="list" scroll-y enable-flex>
      <view class="title-box" bindtap="goSingle"
        >{{ vehicleName }}
        <image
          style="background: url({{IndexPageUrl.ArrowLeft}}) no-repeat center/100%; width: 15px; height: 15px;transform:rotate(180deg)"
        ></image
      ></view>

      <view class="issue-info" wx:if="{{accidentDetail}}">
        <view class="desc">{{ accidentDetail?.description }}</view>
        <view class="fields-content">
          <field-info
            labelName="工单信息"
            value="{{accidentDetail?.issueNumber || '暂无'}}"
            statusText="{{accidentDetail?.issueStatusName}}"
            status="{{accidentDetail?.issueStatus}}"
          />
          <field-info
            labelName="车辆位置"
            value="{{accidentDetail?.address || '暂无'}}"
          />
          <field-info-media
            labelName="事故快照"
            wx:if="{{accidentDetail?.attachmentList?.length > 0}}"
            value="{{accidentDetail?.attachmentList || []}}"
          />
          <field-info
            labelName="远驾跟进人"
            value="{{accidentDetail?.remoteUser || '暂无'}}"
          />
          <field-info
            labelName="一线跟进人"
            value="{{accidentDetail?.operationUser || '暂无'}}"
          />
        </view>
        <t-divider />
        <view class="grid">
          <view
            class="grid-item"
            wx:for="{{items}}"
            wx:if="{{item.show}}"
            wx:key="index"
            bindtap="clickControlBtn(item)"
          >
            <view
              class="grid-item-icon"
              style="background: url({{item.icon}}) no-repeat center/100%"
            />
            <text class="label">{{ item.label }}</text>
          </view>
        </view>
      </view>
      <view class="operate-record">
        <view class="title">处理记录</view>
        <view class="time-line-wrapper"
          ><step-line stepList="{{recordList}}"
        /></view>
      </view>
    </scroll-view>
    <view class="footer" wx:if="{{btnGroup.length>0}}">
      <view
        wx:for="{{btnGroup}}"
        class="btn {{item.type}}"
        wx:key="type"
        bindtap="btnClick(item.operateType)"
      >
        {{ item.text }}
      </view>
    </view>
    <t-dialog
      visible="{{showConfirm}}"
      content="{{confirmContent}}"
      confirm-btn="{{okBtnText}}"
      cancel-btn="取消"
      bind:confirm="handleConfirm"
      bind:close="handleClose"
    />
    <call-front-operate
      visible="{{callFrontVisible}}"
      vehicleName="{{vehicleName}}"
      bind:onVisibleChange="onCallFrontVisibleChange"
    />
    <call-driver-operate
      visible="{{callDriverVisible}}"
      vehicleName="{{vehicleName}}"
      bind:onVisibleChange="onCallDriverVisibleChange"
    ></call-driver-operate>
    <map style="width: 200px; height: 200px; display: none" id="map"></map>
  </view>
</template>
<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import { Notification, IndexPageUrl } from 'shared/assets/imageUrl';
  import { doRequest, HTTPSTATUSCODE } from 'shared/api/fetch';
  import { AccidentDetial } from 'shared/api/accidentDetail';
  import { FindCar } from 'widgets/commandOperate/findcarOperate';
  import { getPermissionById } from 'shared/utils/utils';
  const accidentApi = new AccidentDetial();
  createPage<any>({
    data: {
      showConfirm: false,
      callFrontVisible: false,
      confirmContent: '',
      okBtnText: '确认跟进',
      okBtnType: 'ACCEPT',
      Notification: Notification,
      IndexPageUrl,
      vehicleName: '',
      messageId: '',
      accidentDetail: null,
      btnGroup: [],
      callDriverVisible: false,
      items: [
        {
          icon: Notification.ShadowPlay,
          label: '车辆影子',
          color: '#1989fa',
          type: 'ShadowPlay',
          show: true
        },
        {
          icon: Notification.VideoPlay,
          label: '四路视频',
          color: '#1989fa',
          type: 'VideoPlay',
          show: getPermissionById('realtime')
        },
        {
          icon: Notification.RemoteControl,
          label: '车辆遥控',
          color: '#ff9900',
          type: 'RemoteControl',
          show: getPermissionById('remoteControlBtn')
        },
        {
          icon: Notification.Navigator,
          label: '导航找车',
          color: '#52c41a',
          type: 'Navigator',
          show: getPermissionById('findCarBtn')
        },
        {
          icon: Notification.CallRemote,
          label: '呼叫远驾',
          color: '#1989fa',
          type: 'CallRemote',
          show: getPermissionById('callDriverBtn')
        },
        {
          icon: Notification.Call,
          label: '呼叫一线',
          color: '#ff4d4f',
          type: 'Call',
          show: getPermissionById('callFrontBtn')
        }
      ]
    },
    computed: {
      recordList() {
        return this.accidentDetail?.recordList?.map((item: any) => ({
          title: item.title,
          desc: item.content
        }));
      }
    },
    methods: {
      onCallFrontVisibleChange() {
        this.setData({
          callFrontVisible: false
        });
      },
      onCallDriverVisibleChange() {
        this.setData({
          callDriverVisible: false
        });
      },
      clickControlBtn(item: any) {
        switch (item.type) {
          case 'ShadowPlay':
            wx.navigateTo({
              url:
                '/pages/notification/shadow/index?vehicleName=' +
                this.vehicleName +
                '&eventId=' +
                this.accidentDetail?.shadowEventId
            });
            break;
          case 'VideoPlay':
            wx.navigateTo({
              url:
                '/pages/notification/realtime/index?vehicleName=' +
                this.vehicleName +
                '&eventId=' +
                this.accidentDetail?.shadowEventId
            });
            break;
          case 'RemoteControl':
            const vehicleName = this.vehicleName;
            const messageId = this.messageId;
            getApp().globalData.controlVehicleName = this.vehicleName;
            getApp().globalData.isNavigateBump = true;
            getApp().globalData.eventType = 'navigate';
            getApp().globalData.backUrl = `/pages/notification/bumpDetail/index?vehicleName=${vehicleName}&messageId=${messageId}`;
            wx.navigateTo({
              url: '/pages/remoteControl/index?vehicleName=' + this.vehicleName
            });
            break;
          case 'Navigator':
            const mapContext = wx.createMapContext('map', this);
            new FindCar(mapContext).handleNavigation(this.vehicleName);
            break;
          case 'CallRemote':
            this.setData({
              callDriverVisible: true
            });
            break;
          case 'Call':
            this.setData({
              callFrontVisible: true
            });
            break;
          default:
            return;
        }
      },
      btnClick(operateType: 'REJECT' | 'REPORT') {
        operateType === 'REJECT' && this.rejectOperate();
        operateType === 'REPORT' && this.reportOperate();
      },
      rejectOperate() {
        this.showConfirm = true;
        this.okBtnText = '无需处理';
        this.okBtnType = 'REJECT';
        this.confirmContent = '确认该事故无需处理吗？';
      },
      reportOperate() {
        this.showConfirm = true;
        this.okBtnText = '生成事故单';
        this.okBtnType = 'REPORT';
        this.confirmContent = '确认跟进事故，生成事故单吗？';
      },
      handleConfirm() {
        this.showConfirm = false;
        this.operateAccident(this.okBtnType);
      },
      operateAccident(operateType: 'REJECT' | 'REPORT') {
        accidentApi
          .accidentOperate({
            messageId: this.messageId,
            accidentNo: this.accidentDetail?.accidentNo,
            operateType: operateType
          })
          .then((res: any) => {
            if (res.code === HTTPSTATUSCODE.Success) {
              if (operateType == 'REJECT') {
                wx.navigateBack({
                  delta: 1
                });
              } else if (operateType == 'REPORT') {
                wx.navigateTo({
                  url:
                    '/pages/notification/bumpForm/index?vehicleName=' +
                    this.vehicleName +
                    '&messageId=' +
                    this.messageId +
                    '&accidentNo=' +
                    this?.accidentDetail?.accidentNo
                });
              }
            } else {
              wx.showToast({
                icon: 'none',
                title: res.message
              });
            }
          });
      },
      handleClose() {
        this.showConfirm = false;
      },
      goToAccidentFormPage() {},
      getAccidentDetail(messageId: string) {
        const self = this;
        accidentApi.getAccidentDetail(messageId).then((res: any) => {
          if (res?.code === HTTPSTATUSCODE.Success) {
            const userName = wx.getStorageSync('userName');
            this.setData({
              btnGroup:
                res?.data?.operationUser && res?.data?.operationUser != userName
                  ? []
                  : [
                      {
                        text: '无需处理',
                        type: 'normal',
                        operateType: 'REJECT'
                      },
                      {
                        text: '生成事故单',
                        type: 'primary',
                        operateType: 'REPORT'
                      }
                    ],
              accidentDetail: res?.data
            });
          }
        });
      },
      goSingle() {
        getApp().globalData.singleVehicle = this.vehicleName;
        wx.switchTab({
          url: '/pages/vehicle/index'
        });
      }
    },
    onLoad(options: { vehicleName: string; messageId: string }) {
      this.setData({
        vehicleName: options.vehicleName,
        messageId: options.messageId
      });
      this.getAccidentDetail(options.messageId);
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>
<style lang="scss" >
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    flex-flow: column;
  }
  .main-container {
    height: calc(100vh - 200px);
    overflow-y: auto;
    display: flex;
    flex-flow: column;
    padding-bottom: 4px;
  }
  .title-box {
    margin: 16px 16px 8px;
    flex: 0 0 38px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    text-align: center;
    line-height: 38px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .issue-info {
    height: auto;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    margin: 0 16px 8px;
    padding: 12px;
    .desc {
      display: block;
      background: #fff1f0;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(250, 44, 25, 1);
      padding: 4px 8px;
    }
    .fields-content {
      margin-top: 12px;
    }
    .grid {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      .grid-item {
        width: 21%;
        text-align: center;
        display: flex;
        flex-flow: column;
        align-items: center;
      }
      .grid-item-icon {
        width: 24px;
        height: 24px;
      }
      .label {
        display: block;
        margin-top: 6px;
        font-size: 12px;
        color: #333;
      }
    }
  }
  .operate-record {
    flex: 1;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    margin: 0 16px;
    padding: 12px;
    height: 100%;
    .title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
    }
    .time-line-wrapper {
      background: rgba(248, 248, 248, 1);
      border: 1px solid rgba(245, 245, 245, 1);
      border-radius: 4px 4px 0 0;
      padding: 12px 4px 0;
      overflow-y: auto;
      height: calc(100% - 40px);
      margin-top: 8px;
    }
  }
  .footer {
    background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 16px 50px;
    .btn {
      width: 80%;
      height: 40px;
      border-radius: 40px;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      line-height: 40px;
    }
    .normal {
      width: 106px;
      height: 40px;
      border: 1px solid rgba(204, 204, 204, 1);
      border-radius: 30px;
      background: #fff;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
      margin-right: 16px;
    }
  }
  .t-dialog__footer .t-button--default {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9) !important;
  }
  .t-dialog__footer .t-button--primary {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: bold;
    color: rgba(250, 44, 25, 1) !important;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "shared/ui/naviBar.mpx",
      "field-info": "shared/ui/fieldInfo.mpx",
      "field-info-media": "shared/ui/fieldInfoMedia.mpx",
      "step-line": "shared/ui/stepLine.mpx",
      "t-divider": "tdesign-miniprogram/divider/divider",
      "t-icon": "tdesign-miniprogram/icon/icon",
      "t-dialog": "tdesign-miniprogram/dialog/dialog",
      "call-front-operate": "widgets/commandOperate/callFrontOperate.mpx",
      "call-driver-operate": "widgets/commandOperate/callDriverOperate"
    },
    "navigationStyle": "custom"
  }
</script>
