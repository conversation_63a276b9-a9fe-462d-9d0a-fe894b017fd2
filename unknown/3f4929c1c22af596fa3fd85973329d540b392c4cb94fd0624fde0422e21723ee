<template>
  <view class="index">
    <view class="address-wrapper" bindtap="openSearchLocation">
      <image class="icon" src="{{IndexPageUrl.Position}}"></image>
      <text class="address">{{ address }}</text>
    </view>
    <map
      id="vehicleMap"
      scale="{{scale}}"
      latitude="{{mapCenter.latitude}}"
      longitude="{{mapCenter.longitude}}"
      bindregionchange="onMoveMapRegion"
      show-location="{{true}}"
      markers="{{markers}}"
      polyline="{{polyline}}"
      bindmarkertap="handleClickMarker"
      bindlabeltap="handleClickMarker"
    ></map>

    <view
      class="vehicle-container"
      id="sgm-vehicle-container"
      style="height: {{height}}px; background: url({{VehicleUrl.PanSheetBg}}) no-repeat top/100%"
    >
      <!-- 顶部不参与手势协商，单独控制 -->
      <pan-gesture-handler worklet:ongesture="handlePan" style="flex-shrink: 0">
        <view class="sheet-header">
          <view
            class="position"
            bindtap="moveToUser"
            style="background: url({{SingleVehicle.PositionIcon}}) no-repeat center/100%; width: 40px; height: 40px;"
          ></view>
          <view class="sheet-handler"> </view>
          <view
            class="search-vehicle-btn-wrapper"
            wx:if="{{!showSingleVehicle}}"
          >
            <view class="search-label"
              >车辆列表
              <view
                class="search-btn"
                bindtap="openSearchVehicle"
                style="background: url({{VehicleUrl.SearchIconRed}}) no-repeat center/100%"
              ></view
            ></view>
          </view>
          <view class="vehicle-info-title" wx:else>
            <view class="vehicle-name-info" bindtap="back">
              <image src="{{RemoteControlUrl.NaviBackArrow}}"> </image>
              <view class="vehicle-name">{{ selectedVehicleName }}</view>
            </view>
            <image
              class="vehicle-status"
              wx:if="{{vehicleInfo.statusImgUrl}}"
              src="{{vehicleInfo.statusImgUrl}}"
            >
            </image>
          </view>
        </view>
      </pan-gesture-handler>
      <!-- 滚动区要与 pan 手势协商 -->
      <pan-gesture-handler
        id="pan"
        worklet:should-response-on-move="shouldPanResponse"
        simultaneousHandlers="{{['scroll']}}"
        worklet:ongesture="handlePan"
      >
        <vertical-drag-gesture-handler
          id="scroll"
          native-view="scroll-view"
          worklet:should-response-on-move="shouldScrollViewResponse"
          simultaneousHandlers="{{['pan']}}"
        >
          <scroll-view
            class="vehicle-list"
            scroll-y
            worklet:adjust-deceleration-velocity="adjustDecelerationVelocity"
            worklet:onscrollupdate="handleScroll"
            type="list"
            show-scrollbar="{{false}}"
            wx:if="{{!showSingleVehicle}}"
          >
            <vehicle-list
              vehicleList="{{userVehicleList}}"
              bindclickvehicleitem="jump"
            >
            </vehicle-list>
            <view class="safe-area-inset-bottom"></view>
          </scroll-view>
          <scroll-view
            class="single-vehicle-container"
            scroll-y
            worklet:adjust-deceleration-velocity="adjustDecelerationVelocity"
            worklet:onscrollupdate="handleScroll"
            type="list"
            show-scrollbar="{{false}}"
            wx:else
          >
            <vehicle-info
              selectedVehicleName="{{selectedVehicleName}}"
              vehicleInfo="{{vehicleInfo}}"
            />

            <view class="safe-area-inset-bottom"></view>
          </scroll-view>
        </vertical-drag-gesture-handler>
      </pan-gesture-handler>
    </view>
  </view>
  <real-tab-bar />
  <search-vehicle
    visible="{{showSearchVehicle}}"
    bindclosepopup="closeSearchVehicle"
    bindselectvehicle="jump"
  >
  </search-vehicle>
  <search-location
    visible="{{showSearchLocation}}"
    bindclosepopup="closeSearchLocation"
  ></search-location>
</template>

<script>
  import { createPage } from '@mpxjs/core';
  import {
    IndexPageUrl,
    SingleVehicle,
    RemoteControlUrl,
    VehicleUrl,
    FreeDriveUrl
  } from 'shared/assets/imageUrl';
  import { SingleVehicleApi } from 'shared/api/singleVehicle';
  import VehicleMapApi from 'shared/api/vehicleMap';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { GestureState, SystemStatus } from 'shared/utils/constant';
  import useLocationStore from 'shared/store/useLocationStore';
  import { mapStores } from '@mpxjs/pinia';
  import { isEmpty } from '../../shared/utils/utils';
  import { NotificationApi } from 'shared/api/notification';
  import { sendGlobalEvent, addGlobalEventListener } from 'shared/utils/emit';
  const { screenHeight, statusBarHeight, safeArea } = wx.getSystemInfoSync();
  const deviceInfo = wx.getDeviceInfo();
  const isIOS = deviceInfo.system.includes('iOS');
  const colorMap = {
    [SystemStatus.NORMAL]: '#52B066',
    [SystemStatus.ABNORMAL]: '#E6432E',
    [SystemStatus.OFFLINE]: '#808080'
  };
  const VehicleCluster = require('shared/assets/vehicle-cluster.png');
  createPage({
    data: {
      fetchApi: new SingleVehicleApi(),
      vehicleMapApi: new VehicleMapApi(),
      notificationApi: new NotificationApi(),
      IndexPageUrl,
      VehicleUrl,
      FreeDriveUrl,
      GestureState,
      SingleVehicle,
      RemoteControlUrl,
      selectedTabKey: 'vehicle',
      scale: 16,
      timer: null,
      userVehicleList: [],
      mapVehicleList: [],
      showSingleVehicle: false,
      selectedVehicleName: null,
      vehicleInfo: {},
      showSearchVehicle: false,
      showSearchLocation: false,
      vehicleInfoTimer: null,
      markers: [],
      polyline: [],
      noticeNum: null
    },
    lifetimes: {
      created() {
        this.transY = wx.worklet.shared(1000);
        this.scrollTop = wx.worklet.shared(0);
        this.startPan = wx.worklet.shared(true);
        this.initTransY = wx.worklet.shared(0); // 留言半屏的初始位置
        this.upward = wx.worklet.shared(false);
      },
      attached() {
        this.setData({
          height: screenHeight - statusBarHeight
        });
      },
      ready() {
        const query = this.createSelectorQuery();
        const deviceInfo = wx.getDeviceInfo();
        // ready 生命周期里才能获取到首屏的布局信息
        query.select('.sheet-header').boundingClientRect();
        query.exec(res => {
          if (deviceInfo.system.includes('iOS')) {
            this.transY.value = this.initTransY.value =
              screenHeight -
              res[0].height -
              (screenHeight - safeArea.bottom) -
              100;
          } else {
            this.transY.value = this.initTransY.value =
              screenHeight -
              res[0].height -
              (screenHeight - safeArea.bottom) -
              150;
          }
        });
        // 通过 transY 一个 SharedValue 控制半屏的位置
        this.applyAnimatedStyle('.vehicle-container', () => {
          'worklet';
          return { transform: `translateY(${this.transY.value}px)` };
        });
      }
    },
    computed: {
      ...mapStores(useLocationStore),
      mapCenter() {
        if (this.locationStore.getLocation) {
          return this.locationStore.getLocation;
        } else {
          const initLocation = wx.getStorageSync('userLocation');
          return initLocation;
        }
      },
      address() {
        if (this.locationStore.getAddress) {
          return this.locationStore.getAddress;
        } else {
          const initAddress = wx.getStorageSync('userAddress');
          return initAddress;
        }
      }
    },
    onShow() {
      addGlobalEventListener('update_notice_num', val => {
        this.setData({
          noticeNum: val.val
        });
      });
      if (typeof this.getTabBar === 'function') {
        this.getTabBar(tabBar => {
          tabBar.setData({
            selectedTabKey: 'vehicle'
          });
        });
      }
      this.getTodoList();
      this.setData({
        selectedTabKey: 'vehicle'
      });
      this.getUserVehicleList();
      const that = this;
      if (that.mapCtx) {
        that.mapCtx.getCenterLocation({
          success: function (res) {
            clearInterval(that.timer);
            that.getMapVehicleList(res.longitude, res.latitude);
            that.timer = setInterval(() => {
              that.getMapVehicleList(res.longitude, res.latitude);
            }, 5000);
          }
        });
      } else {
        clearInterval(this.timer);
        this.getMapVehicleList(this.mapCenter.longitude, this.mapCenter.latitude);
        this.timer = setInterval(() => {
          this.getMapVehicleList(
            this.mapCenter.longitude,
            this.mapCenter.latitude
          );
        }, 5000);
      }

      const vehicleName = getApp().globalData.singleVehicle;
      if (!vehicleName) {
        this.setData({
          showSingleVehicle: false,
          selectedVehicleName: vehicleName
        });
        return;
      }
      this.setData({
        showSingleVehicle: true,
        showSearchVehicle: false,
        selectedVehicleName: vehicleName
      });
      this.getVehicleRealTime();
      this.getVehicleRealTimer = setInterval(() => {
        this.getVehicleRealTime();
      }, 5000);
    },
    onLoad() {
      this.mapCtx = wx.createMapContext('vehicleMap');
      this.bindEvent();
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    onHide() {
      clearInterval(this.timer);
      clearInterval(this.getVehicleRealTimer);
      this.setData({
        selectedVehicleName: null,
        markers: [],
        polyline: []
      });
    },
    onUnload() {
      clearInterval(this.timer);
      clearInterval(this.getVehicleRealTimer);
      getApp().globalData.singleVehicle = null;
      this.setData({
        selectedVehicleName: null,
        markers: [],
        polyline: []
      });
    },
    watch: {
      selectedVehicleName(val) {
        this.getVehiclePath(val);
      },
      showSingleVehicle(val, oldVal) {
        if (!val) {
          this.setData({
            vehicleInfo: {}
          });
        }
      }
    },
    methods: {
      bindEvent() {
        this.mapCtx.initMarkerCluster({
          enableDefaultStyle: false,
          zoomOnClick: true,
          gridSize: 30
        });

        // enableDefaultStyle 为 true 时不会触发改事件
        this.mapCtx.on('markerClusterCreate', res => {
          console.log('clusterCreate', res);
          const clusters = res.clusters;
          const markers = clusters.map(cluster => {
            const { center, clusterId, markerIds } = cluster;
            return {
              ...center,
              width: 60,
              height: 60,
              clusterId, // 必须
              iconPath: VehicleCluster,
              alpha: 1,
              joinCluster: true,
              label: {
                content: markerIds.length + '',
                fontSize: 15,
                width: 20,
                height: 20,
                color: '#FA2C19FF',
                bgColor: '#fff',
                borderRadius: 10,
                textAlign: 'center',
                anchorX: isIOS ? 0 : -10,
                anchorY: -40
              }
            };
          });
          this.mapCtx.addMarkers({
            markers,
            clear: false
          });
        });
      },
      async getTodoList() {
        const res = await this.notificationApi.getUserTodoList();
        if (res.code === HTTPSTATUSCODE.Success) {
          sendGlobalEvent('update_notice_num', { val: res.data.todoSize });
          this.setData({
            noticeNum: res.data.todoSize
          });
        }
      },
      moveToUser() {
        this.mapCtx.moveToLocation();
      },
      openSearchVehicle(e) {
        this.setData({
          showSearchVehicle: true
        });
      },
      closeSearchVehicle(e) {
        this.setData({
          showSearchVehicle: false
        });
      },
      openSearchLocation(e) {
        this.setData({
          showSearchLocation: true
        });
      },
      closeSearchLocation(e) {
        this.setData({
          showSearchLocation: false
        });
      },
      async getVehiclePath(vehicleName) {
        try {
          if (vehicleName) {
            const res = await this.vehicleMapApi.getVehiclePath(vehicleName);
            if (res.code === HTTPSTATUSCODE.Success) {
              let startPoint, endPoint;
              let stopPointList = [];
              let pathArr = [];
              const routingPoint = res.data.routingPointList;
              if (!isEmpty(routingPoint)) {
                routingPoint.forEach(point => {
                  point.forEach(i => {
                    pathArr.push({ latitude: i.lat, longitude: i.lon });
                  });
                });
                const firstPath = routingPoint[0];
                startPoint = {
                  id: `start_point_${vehicleName}`,
                  latitude: firstPath[0].lat,
                  longitude: firstPath[0].lon,
                  iconPath: this.SingleVehicle.StartPoint,
                  width: '38px',
                  height: '38px',
                  alpha: 1
                };
              }
              if (!isEmpty(res.data.stopPointList)) {
                res.data.stopPointList.forEach((item, index) => {
                  if (index < res.data.stopPointList.length - 1) {
                    stopPointList.push({
                      id: `stop_point_${index}`,
                      latitude: item.latitude,
                      longitude: item.longitude,
                      iconPath: this.SingleVehicle.StopPoint,
                      width: '38px',
                      height: '38px',
                      alpha: 1
                    });
                  } else if (index === res.data.stopPointList.length - 1) {
                    // 取最后一个停靠点作为终点
                    endPoint = {
                      id: `end_point_${vehicleName}`,
                      latitude: item.latitude,
                      longitude: item.longitude,
                      iconPath: this.SingleVehicle.EndPoint,
                      width: '38px',
                      height: '38px',
                      alpha: 1
                    };
                  }
                });
              }
              this.setData({
                markers: [
                  ...this.markers,
                  startPoint,
                  endPoint,
                  ...stopPointList
                ],
                polyline: [
                  {
                    points: pathArr,
                    color: '#4DB672',
                    width: 10,
                    arrowLine: true,
                    borderColor: '#007C00',
                    borderWidth: 1
                  }
                ]
              });
            }
          } else {
            this.setData({
              polyline: []
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      getVehicleIcon(vehicleInfo, clickVehicle) {
        if (vehicleInfo.systemStatus === SystemStatus.OFFLINE) {
          if (vehicleInfo.vehicleName === clickVehicle) {
            return this.FreeDriveUrl.OfflineSelectedVehicleIcon;
          } else {
            return this.FreeDriveUrl.OfflineNotSelect;
          }
        } else {
          if (vehicleInfo.vehicleName === clickVehicle) {
            return this.FreeDriveUrl.SelectVehicleIcon;
          } else {
            return this.FreeDriveUrl.OnlineNotSelect;
          }
        }
      },
      formatMarkers(list, clickVehicle) {
        this.setData({
          markers: list.map(v => {
            return {
              id: v.vehicleName,
              label: {
                content: v.vehicleName,
                padding: 4,
                borderRadius: 4,
                display: 'ALWAYS',
                color: colorMap[v.systemStatus],
                fontSize: 14,
                borderColor: colorMap[v.systemStatus],
                borderWidth: 2,
                textAlign: 'center',
                anchorX: isIOS ? 0 : -30,
                anchorY: v.vehicleName === clickVehicle ? -75 : -52,
                bgColor: '#FFFFFF'
              },
              joinCluster: true,
              latitude: v.latitude,
              longitude: v.longitude,
              iconPath: this.getVehicleIcon(v, clickVehicle),
              width: v.vehicleName === clickVehicle ? '52px' : '38px',
              height: v.vehicleName === clickVehicle ? '56px' : '30px',
              alpha: 1
            };
          })
        });
      },
      handleClickMarker(e) {
        const clickVehicle = e.detail.markerId;
        if (
          clickVehicle.includes('start_point') ||
          clickVehicle.includes('end_point') ||
          clickVehicle.includes('stop_point')
        ) {
          return;
        } else {
          this.changeSelectVehicle(
            clickVehicle,
            this.formatMarkers.bind(this, this.mapVehicleList, clickVehicle)
          );
        }
      },
      changeSelectVehicle(vehicleName, selectCB) {
        if (this.selectedVehicleName !== vehicleName) {
          this.setData({
            selectedVehicleName: vehicleName
          });
          selectCB && selectCB();
          this.jump({
            detail: {
              vehicleName
            }
          });
        } else {
          this.formatMarkers(this.mapVehicleList);
          this.back();
        }
      },
      jump(e) {
        getApp().globalData.singleVehicle = e.detail.vehicleName;
        this.setData({
          showSingleVehicle: true,
          showSearchVehicle: false,
          selectedVehicleName: e.detail.vehicleName
        });
        this.getVehicleRealTime();
        this.getVehicleRealTimer = setInterval(() => {
          this.getVehicleRealTime();
        }, 5000);
      },
      back() {
        getApp().globalData.singleVehicle = null;
        this.setData({
          showSingleVehicle: false,
          selectedVehicleName: null
        });
        clearInterval(this.getVehicleRealTimer);
        this.getUserVehicleList();
      },
      async getVehicleRealTime() {
        const res = await this.fetchApi.getVehicleRealTime(
          this.selectedVehicleName
        );
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            vehicleInfo: {
              ...res.data,
              statusImgUrl: this.SingleVehicle[res.data.systemStatus]
            }
          });
        }
      },
      async getUserVehicleList() {
        try {
          const res = await this.vehicleMapApi.getVehicleList();
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              userVehicleList: res.data
            });
          } else {
            wx.showToast({
              title: res.message,
              icon: 'none'
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      async getMapVehicleList(longitude, latitude) {
        try {
          const res = await this.vehicleMapApi.getMapVehicleList(
            longitude,
            latitude
          );
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              mapVehicleList: res.data
            });
            if (this.selectedVehicleName) {
              this.formatMarkers(res.data, this.selectedVehicleName);
              this.getVehiclePath(this.selectedVehicleName);
            } else {
              this.formatMarkers(res.data);
            }
          }
        } catch (err) {
          console.error(err);
        }
      },
      handleClickTab(e) {
        wx.vibrateShort({
          type: 'heavy',
          success: () => {
            console.log('震动成功');
          },
          fail: () => {
            console.log('震动失败');
          }
        });
        const data = e.currentTarget.dataset;
        const url = data.path;
        getApp().globalData.singleVehicle = null;
        this.setData({
          selectedTabKey: data.key
        });
        wx.switchTab({ url });
      },
      onMoveMapRegion(e) {
        if (e.type === 'end') {
          clearInterval(this.timer);
          this.getMapVehicleList(
            e.detail.centerLocation.longitude,
            e.detail.centerLocation.latitude
          );
          this.timer = setInterval(() => {
            this.getMapVehicleList(
              e.detail.centerLocation.longitude,
              e.detail.centerLocation.latitude
            );
          }, 5000);
        }
      },
      clamp(val, min, max) {
        'worklet';
        return Math.min(Math.max(val, min), max);
      },
      scrollTo(toValue) {
        'worklet';

        this.transY.value = wx.worklet.timing(toValue, { duration: 200 });
      },
      // shouldPanResponse 和 shouldScrollViewResponse 用于 pan 手势和 scroll-view 滚动手势的协商
      shouldPanResponse() {
        'worklet';
        return this.startPan.value;
      },
      shouldScrollViewResponse(pointerEvent) {
        'worklet';
        // transY > 0 说明 pan 手势在移动半屏，此时滚动不应生效
        if (this.transY.value > statusBarHeight) return false;

        const scrollTop = this.scrollTop.value;
        const { deltaY } = pointerEvent;
        // deltaY > 0 是往上滚动，scrollTop <= 0 是滚动到顶部边界，此时 pan 开始生效，滚动不生效
        const result = scrollTop <= 0 && deltaY > 0;
        this.startPan.value = result;
        return !result;
      },
      // 处理拖动半屏的手势
      handlePan(gestureEvent) {
        'worklet';
        // 滚动半屏的位置
        if (gestureEvent.state === this.GestureState.ACTIVE) {
          // deltaY < 0，往上滑动
          this.upward.value = gestureEvent.deltaY < 0;
          // 当前半屏位置
          const curPosition = this.transY.value;
          // 只能在 [statusBarHeight, screenHeight] 之间移动
          const destination = this.clamp(
            curPosition + gestureEvent.deltaY,
            statusBarHeight,
            screenHeight
          );
          if (curPosition === destination) return;
          // 改变 transY，来改变半屏的位置
          this.transY.value = destination;
        }

        if (
          gestureEvent.state === this.GestureState.END ||
          gestureEvent.state === this.GestureState.CANCELLED
        ) {
          if (this.transY.value <= screenHeight / 2) {
            // 在上面的位置
            if (this.upward.value) {
              this.scrollTo(statusBarHeight + 50);
            } else {
              this.scrollTo(screenHeight / 2);
            }
          } else if (
            this.transY.value > screenHeight / 2 &&
            this.transY.value <= this.initTransY.value
          ) {
            // 在中间位置的时候
            if (this.upward.value) {
              this.scrollTo(screenHeight / 2);
            } else {
              this.scrollTo(this.initTransY.value);
            }
          } else {
            // 在最下面的位置
            this.scrollTo(this.initTransY.value);
          }
        }
      },
      adjustDecelerationVelocity(velocity) {
        'worklet';
        const scrollTop = this.scrollTop.value;
        return scrollTop <= 0 ? 0 : velocity;
      },
      handleScroll(evt) {
        'worklet';
        this.scrollTop.value = evt.detail.scrollTop;
      }
    }
  });
</script>

<script name="json">
  module.exports = {
    usingComponents: {
      'vehicle-info': 'widgets/vehicle/vehicleInfo.mpx',
      'search-vehicle': 'widgets/vehicle/searchVehicle.mpx',
      'vehicle-list': 'widgets/vehicle/vehicleList.mpx',
      'search-location': 'widgets/searchLocation.mpx',
      'real-tab-bar': 'shared/ui/realTabBar.mpx'
    },

    renderer: 'skyline',
    componentFramework: 'glass-easel',
    navigationStyle: 'custom'
  };
</script>

<style lang="scss">
  .index {
    height: 100%;
    width: 100%;
    .address-wrapper {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 120px;
      font-size: 14px;
      margin-right: 27px;
      position: fixed;
      top: 60px;
      left: 16px;
      .icon {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
      .address {
        width: 100px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  page {
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100vh;
    color: #1a191e;
  }
  page,
  view {
    box-sizing: border-box;
  }
  pan-gesture-handler,
  vertical-drag-gesture-handler {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .container {
    flex: 1;
    width: 100vw;
    overflow: hidden;
  }
  .container image {
    width: 100vw;
  }

  #vehicleMap {
    width: 100vw;
    height: 100vh;
  }

  .open-comment {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;
    background-color: white;
  }
  .open-comment-wording {
    height: 66px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .safe-area-inset-bottom {
    height: env(safe-area-inset-bottom);
  }

  .vehicle-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    z-index: 999;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  }
  .sheet-header {
    width: 100%;
    height: 52px;
    position: relative;
    .sheet-handler {
      width: 40px;
      height: 4px;
      background: rgba(186, 190, 199, 1);
      border-radius: 4px;
      margin: 0 auto 10px;
      position: relative;
      top: 10px;
    }
    .position {
      position: absolute;
      top: -50px;
      right: 10px;
    }
    .search-vehicle-btn-wrapper {
      line-height: 52px;
      height: 52px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      top: 0px;
      padding: 18px 16px;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      color: #1a1a1a;
      .search-label {
        display: flex;
        justify-content: center;
        align-items: center;
        .search-btn {
          width: 18px;
          height: 18px;
          margin-left: 8px;
        }
      }
    }
    .vehicle-info-title {
      line-height: 52px;
      height: 52px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      top: 0px;
      padding: 0px 16px;
      .vehicle-name-info {
        display: flex;
        align-items: center;
        justify-content: center;
        image {
          width: 18px;
          height: 18px;
          margin-right: 8px;
        }
        .vehicle-name {
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 500;
          color: rgba(26, 26, 26, 1);
        }
      }
      .vehicle-status {
        width: 57px;
        height: 23px;
      }
    }
  }
  .vehicle-list {
    flex: 1;
    height: 100%;
  }
  .vehicle-item {
    padding: 0 20px 20px;
    font-size: 13px;
    line-height: 1.4;
  }
  .single-vehicle-container {
    flex: 1;
    .title {
      height: 52px;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0) 100%
      );
      border: 1px solid rgba(255, 255, 255, 1);
      border-radius: 16px 16px 0 0;
    }
  }

  .tab-bar {
    bottom: 0;
    width: 100%;
    pointer-events: auto;
    position: absolute;
    height: 90px;
    border-radius: 15px 15px 0 0;
    background-color: #ffff;
    box-shadow: 0px -1px 8px rgb(165 165 165 / 55%);
    display: flex;
    justify-content: space-around;
    align-items: center;
    .tab-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      width: 80px;
      height: 80px;
      &.message {
        position: relative;
        .notice-num {
          top: 10px;
          position: absolute;
          min-width: 20px;
          min-height: 20px;
          border-radius: 10px;
          background-color: red;
          color: white;
          font-size: 12px;
          line-height: 20px;
          text-align: center;
          z-index: 99;
          left: 42px;
        }
      }
    }
    .tab-text-unselected,
    .tab-text-selected {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
    }
    .tab-text-selected {
      color: rgba(26, 26, 26, 1);
    }
  }
</style>
