<template>
  <view
    class="control-panel-container"
    wx:class="{{{ 'control-pannel-collapse': isCollapsed, 'control-pannel-expand': !isCollapsed }}}"
  >
    <view class="control-btn-list" wx:if="{{vehiclePermission}}">
      <view class="row">
        <view class="col" wx:if="{{realtime}}">
          <view class="play-video" bindtap="handlePlayVideo">
            <t-image
              src="{{SingleVehicle.PlayVideo}}"
              mode="aspectFill"
              width="28px"
              height="28px"
            />
            <text class="label">四路视频</text>
          </view>
        </view>
        <view class="col" wx:if="{{remoteControl}}"
          ><remote-control-btn
            sourceType="singleVehicle"
            vehicleName="{{vehicleName}}"
        /></view>
        <view class="col" wx:if="{{callDriverBtn}}"
          ><call-driver
            sourceType="singleVehicle"
            vehicleName="{{vehicleName}}"
        /></view>
        <view class="col" wx:if="{{findCarBtn}}"
          ><find-car sourceType="singleVehicle" vehicleName="{{vehicleName}}"
        /></view>
        <view class="col" wx:if="{{openGridBtn}}"
          ><open-grid sourceType="singleVehicle" vehicleName="{{vehicleName}}"
        /></view>
        <view class="col" wx:if="{{restartBtn}}"
          ><restart sourceType="singleVehicle" vehicleName="{{vehicleName}}"
        /></view>
        <!-- </view> -->

        <!-- <view class="row" wx:if="{{!isCollapsed}}"> -->
        <view class="col" wx:if="{{freeDrivingBtn}}"
          ><free-driving
            sourceType="singleVehicle"
            vehicleName="{{vehicleName}}"
        /></view>
        <view class="col" wx:if="{{callFrontBtn}}"
          ><call-front sourceType="singleVehicle" vehicleName="{{vehicleName}}"
        /></view>
        <view class="col" wx:if="{{followVehicleBtn}}">
          <follow-vehicle
            sourceType="singleVehicle"
            vehicleName="{{vehicleName}}"
          />
        </view>
        <view class="col" wx:if="{{loginVehicleBtn}}">
          <login-vehicle
            sourceType="singleVehicle"
            vehicleName="{{vehicleName}}"
        /></view>
        <view class="col" wx:if="{{reportSoftwareBtn}}">
          <report-software-btn
            sourceType="singleVehicle"
            vehicleName="{{vehicleName}}"
          />
        </view>
        <!-- </view> -->

        <!-- <view class="row" wx:if="{{!isCollapsed}}"> -->
        <view class="col" wx:if="{{reportHardwareBtn}}">
          <report-hardware-btn
            sourceType="singleVehicle"
            vehicleName="{{vehicleName}}"
          />
        </view>
      </view>
    </view>

    <view class="nopermission-btn-list" wx:else>
      <view class="row">
        <view class="col" wx:if="{{reportSoftwareBtn}}"
          ><call-driver
            sourceType="singleVehicle"
            vehicleName="{{vehicleName}}"
        /></view>
        <view class="col" wx:if="{{reportSoftwareBtn}}"
          ><call-front sourceType="singleVehicle" vehicleName="{{vehicleName}}"
        /></view>
      </view>
    </view>
    <view
      wx:if="{{isCollapsed &&vehiclePermission }}"
      class="expand-btn"
      style="background: url({{SingleVehicle.Expand}}) no-repeat center/100%"
      bindtap="changeCollapse"
    ></view>
    <view
      wx:if="{{!isCollapsed  && vehiclePermission}}"
      class="collapse-btn"
      style="background: url({{SingleVehicle.Collapse}}) no-repeat center/100%"
      bindtap="changeCollapse"
    ></view>
  </view>
</template>
<script>
  import { createComponent } from '@mpxjs/core';
  import { SingleVehicle } from 'shared/assets/imageUrl';
  import { SingleVehicleApi } from 'shared/api/singleVehicle';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { getPermissionById } from 'shared/utils/utils';
  import { PermissionKey } from 'shared/utils/constant';
  createComponent({
    properties: {
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      fetchApi: new SingleVehicleApi(),
      isCollapsed: true,
      SingleVehicle,
      vehiclePermission: true
    },
    computed: {
      remoteControl() {
        return getPermissionById(PermissionKey.remoteControlBtn);
      },
      callDriverBtn() {
        return getPermissionById(PermissionKey.callDriverBtn);
      },
      reportHardwareBtn() {
        return getPermissionById(PermissionKey.reportHardwareBtn);
      },
      openGridBtn() {
        return getPermissionById(PermissionKey.openGridBtn);
      },
      freeDrivingBtn() {
        return getPermissionById(PermissionKey.freeDrivingBtn);
      },
      restartBtn() {
        return getPermissionById(PermissionKey.restartBtn);
      },
      followVehicleBtn() {
        return getPermissionById(PermissionKey.followVehicleBtn);
      },
      findCarBtn() {
        return getPermissionById(PermissionKey.findCarBtn);
      },
      callFrontBtn() {
        return getPermissionById(PermissionKey.callFrontBtn);
      },
      loginVehicleBtn() {
        return getPermissionById(PermissionKey.loginVehicleBtn);
      },
      reportSoftwareBtn() {
        return getPermissionById(PermissionKey.reportSoftwareBtn);
      },
      trackMapBtn() {
        return getPermissionById(PermissionKey.trackMapBtn);
      },
      // 车辆控制面板工具按钮配置
      vehicleControlButtons() {
        return [
          {
            key: 'remoteControl',
            component: 'remote-control-btn',
            show: this.remoteControl
          },
          {
            key: 'callDriver',
            component: 'call-driver',
            show: this.callDriverBtn
          },
          { key: 'findCar', component: 'find-car', show: this.findCarBtn },
          { key: 'openGrid', component: 'open-grid', show: this.openGridBtn },
          { key: 'restart', component: 'restart', show: this.restartBtn },
          {
            key: 'freeDriving',
            component: 'free-driving',
            show: this.freeDrivingBtn
          },
          { key: 'callFront', component: 'call-front', show: this.callFrontBtn },
          {
            key: 'followVehicle',
            component: 'follow-vehicle',
            show: this.followVehicleBtn
          },
          {
            key: 'loginVehicle',
            component: 'login-vehicle',
            show: this.loginVehicleBtn
          },
          {
            key: 'reportSoftware',
            component: 'report-software-btn',
            show: this.reportSoftwareBtn
          },
          {
            key: 'reportHardware',
            component: 'report-hardware-btn',
            show: this.reportHardwareBtn
          }
        ];
      }
    },
    lifetimes: {
      created: function () {
        this.vehicleName && this.getVehiclePermission(this.vehicleName);
      },
      detached: function () {
        // console.log('9999');
      }
    },
    watch: {
      vehicleName(newVal, oldVal) {
        if (newVal) {
          this.getVehiclePermission(newVal);
        }
      }
    },
    methods: {
      changeCollapse() {
        this.setData({
          isCollapsed: !this.isCollapsed
        });
      },
      async getVehiclePermission(vehicleName) {
        const res = await this.fetchApi.getVehiclePermission(vehicleName);
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            vehiclePermission: res.data.permission
          });
        }
      },
      handlePlayVideo() {
        wx.navigateTo({
          url:
            '/pages/notification/realtime/index?vehicleName=' + this.vehicleName
        });
      }
    }
  });
</script>
<style lang="scss">
  .control-panel-container {
    margin: 8px 16px 0px 16px;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.5) 0%,
      rgba(255, 255, 255, 1) 100%
    );
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.22, 0.61, 0.36, 1);
    .row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      .col {
        height: 59px;
        width: calc((100vw - 32px) / 5);
      }
    }
  }
  .control-pannel-collapse {
    .row {
      height: 59px;
      overflow: hidden;
    }
    .control-btn-list {
      width: 100%;
      margin: 8px 8px 0px 8px;
    }
    .expand-btn {
      width: 52px;
      height: 18px;
    }
  }
  .control-pannel-expand {
    .control-btn-list {
      width: 100%;
      margin: 8px 8px 0px 8px;
    }
    .collapse-btn {
      width: 52px;
      height: 18px;
    }
  }
  .play-video {
    display: flex;
    flex-direction: column;
    align-items: center;
    .label {
      text-align: center;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(51, 51, 51, 1);
      font-size: 11px;
    }
  }
  .nopermission-btn-list {
    width: 100%;
    margin: 8px;
  }
</style>
<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      'find-car': 'widgets/commandBtn/findCarBtn',
      restart: 'widgets/commandBtn/restartBtn',
      'call-driver': 'widgets/commandBtn/callDriverBtn',
      'call-front': 'widgets/commandBtn/callFrontBtn',
      'find-car': 'widgets/commandBtn/findCarBtn',
      'follow-vehicle': 'widgets/commandBtn/followVehicleBtn',
      'free-driving': 'widgets/commandBtn/freeDrivingBtn',
      'login-vehicle': 'widgets/commandBtn/loginVehicleBtn',
      'open-grid': 'widgets/commandBtn/openGridBtn',
      'remote-control-btn': '../../widgets/commandBtn/remoteControlBtn.mpx',
      'report-software-btn': 'widgets/commandBtn/reportSoftwareBtn',
      'report-hardware-btn': 'widgets/commandBtn/reportHardwareBtn',
      't-image': 'tdesign-miniprogram/image/image'
    }
  };
</script>
