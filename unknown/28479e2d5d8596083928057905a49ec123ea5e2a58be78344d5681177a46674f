<template>
  <web-view
    id="webview"
    wx:if="{{webviewUrl}}"
    src="{{webviewUrl}}"
    bindmessage="onWebViewMessage"
    allow="autoplay; playsInline"
  ></web-view>
</template>

<style lang="scss">
  .container {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>

<script>
  import { createPage } from '@mpxjs/core';
  import { getWebviewHost } from 'shared/utils/config';
  import { doRequest } from 'shared/api/fetch';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { getAppCode } from 'shared/utils/config';

  createPage({
    data() {
      return {
        vehicleName: '',
        webviewUrl: ''
      };
    },
    methods: {
      onWebViewMessage(event) {
        const message = event.detail.data;
        if (message && message.action === 'goBack') {
          wx.navigateBack({
            delta: 1
          });
        }
      }
    },
    onLoad(query) {
      this.vehicleName = query.vehicleName;
      this.eventId = query.eventId;
      const plugin = requirePlugin('loginPlugin');
      const k = plugin.getPtKey();
      const name = wx.getStorageSync('userName');
      this.webviewUrl = `${getWebviewHost()}/shadow?vehicleName=${
        query.vehicleName
      }&eventId=${query.eventId}&userName=${name}&k=${k}`;
      console.log(this.webviewUrl);
    }
  });
</script>

<script name="json">
  module.exports = {
    navigationStyle: 'custom',
    pageOrientation: 'landscape'
  };
</script>
