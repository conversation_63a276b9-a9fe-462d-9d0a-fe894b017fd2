<template>
  <vehicleInfoCard vehicleInfo="{{vehicleInfo}}">
    <view slot="extend-info">
      <view class="accident-info" wx:if="{{showAccident}}">
        <view class="description">
          <image class="icon" src="{{iconUrl.collision}}" />
          <text class="text">{{ props.description }}</text>
        </view>
        <view class="info-subitem">
          <text>
            远驾跟进人：{{
              props.attributes?.remoteUser
                ? props.attributes.remoteUser
                : '暂无'
            }}
          </text>
          <text>
            一线跟进人：{{
              props.attributes?.siteUser ? props.attributes.siteUser : '暂无'
            }}
          </text>
        </view>
        <view class="address-item">
          <image class="icon" src="{{iconUrl.location}}" />
          <text class="address">{{ props.attributes.address || '-' }}</text>
        </view>
      </view>
      <view class="repair-info" wx:else>{{ repairDescription }}</view>
    </view>
  </vehicleInfoCard>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TodoListType, RepairType } from 'shared/utils/constant';
  import { Notification } from 'shared/assets/imageUrl';
  createComponent({
    properties: {
      props: {
        type: Object
      }
    },
    data: {
      Notification: Notification
    },
    computed: {
      vehicleInfo() {
        let iconInfo = {};
        switch (this.props.type) {
          case 'ACCIDENT':
            iconInfo = {
              label: '碰撞',
              imageUrl: this.Notification.CollisionMsgIcon,
              labelColor: 'red',
              imageWidth: '50px'
            };
            break;
          case 'REPAIR_CONFIRM':
            iconInfo = {
              label: '维修待确认',
              imageUrl: this.Notification.RepairMsgIcon,
              labelColor: 'green',
              imageWidth: '86px'
            };
            break;
          case 'REPAIR_REJECT':
            iconInfo = {
              label: '维修驳回',
              imageUrl: this.Notification.RepairMsgIcon,
              labelColor: 'green',
              imageWidth: '74px'
            };
            break;
        }
        return {
          vehicleName: this.props.vehicleName,
          time: this.props.pushTime,
          stationName: this.props.stationName,
          iconInfo
        };
      },
      showAccident() {
        return this.props.type === TodoListType.ACCIDENT;
      },
      iconUrl() {
        return {
          collision: this.Notification.CollisionIcon,
          location: this.Notification.LocationIcon
        };
      },
      repairDescription() {
        return this.props.type === RepairType.REPAIR_REJECT
          ? '车辆维修单被驳回，请查看原因'
          : '车辆维修完成，请确认车辆可用';
      }
    }
  });
</script>

<style lang="scss">
  .accident-info {
    .description {
      display: flex;
      align-items: center;
      margin-bottom: 5px;

      .icon {
        width: 14px;
        height: 12px;
        margin-right: 5px;
      }
      .text {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(250, 44, 25, 1);
      }
    }
    .info-subitem {
      display: flex;
      margin-bottom: 6px;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
      text {
        margin-right: 10px;
      }
    }
    .address-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px;

      .icon {
        width: 12px;
        height: 12px;
        margin-right: 3px;
      }
      .address {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(128, 128, 128, 1);
      }
    }
  }

  .repair-info {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(128, 128, 128, 1);
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "vehicleInfoCard": "shared/ui/vehicleInfoCard.mpx"
    }
  }
</script>
