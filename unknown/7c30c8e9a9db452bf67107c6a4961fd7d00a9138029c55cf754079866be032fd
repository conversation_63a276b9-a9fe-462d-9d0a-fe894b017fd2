import type {
  FileInfo,
  ProcessResult,
  CompleteResult,
  UploadFunction
} from '../types/fileUpload';

class ParallelUploader {
  private queue: FileInfo[];
  private activeCount: number;
  private readonly maxConcurrent: number;
  private results: ProcessResult[];
  private readonly uploadFileFunction: UploadFunction;

  constructor(maxConcurrent = 3, uploadFunction: UploadFunction) {
    this.queue = [];
    this.activeCount = 0;
    this.maxConcurrent = maxConcurrent;
    this.results = [];
    this.uploadFileFunction = uploadFunction;
  }

  add(file: FileInfo): void {
    this.queue.push(file);
    this.process();
  }

  private async process(): Promise<void> {
    if (this.activeCount >= this.maxConcurrent || !this.queue.length) return;

    this.activeCount++;
    const file = this.queue.shift()!;

    try {
      const result = await this.uploadFileFunction(file);
      this.results.push({ file, finalStatus: 'success', result });
    } catch (error) {
      this.results.push({ file, finalStatus: 'error', error: error as Error });
    } finally {
      this.activeCount--;
      this.process();
    }
  }

  async complete(): Promise<CompleteResult> {
    while (this.activeCount > 0 || this.queue.length > 0) {
      await new Promise<void>(resolve => setTimeout(resolve, 100));
    }

    const successFiles = this.results.filter(r => r.finalStatus === 'success');
    const failedFiles = this.results.filter(r => r.finalStatus === 'error');

    this.results = [];

    return {
      successFiles,
      failedFiles,
      successCount: successFiles.length,
      failCount: failedFiles.length
    };
  }
}

export { ParallelUploader };
export type { UploadFunction };
