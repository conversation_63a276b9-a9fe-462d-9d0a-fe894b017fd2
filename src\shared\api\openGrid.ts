import { doRequest } from 'shared/api/fetch';

export class OpenGridApi {
  getBoxGrid = (vehicleName: string) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/box/getBoxGrid',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  };

  openGrid = ({
    vehicleName,
    gridNoList
  }: {
    vehicleName: string;
    gridNoList: any[];
  }) => {
    console.log(vehicleName, gridNoList);
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/box/openBox',
      data: {
        vehicleName,
        gridNoList
      }
    };
    return doRequest(requestOptions);
  };
}
