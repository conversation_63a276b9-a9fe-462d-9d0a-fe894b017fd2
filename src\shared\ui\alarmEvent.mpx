<template>
  <view class="alarm-item">
    <text class="alarm-event" wx:if="{{alarmName}}">{{ alarmName }}</text>
    <text class="report-time" wx:if="{{props.duration}}">{{
      props.duration
    }}</text>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { getEnumEventName } from 'shared/utils/utils';

  interface Data {
    duration: any;
  }
  createComponent<Data>({
    properties: {
      props: {
        type: Object
      }
    },
    computed: {
      alarmName() {
        return getEnumEventName('ALARM_EVENT_ENUM', this.props.alarmEvent);
      }
    }
  });
</script>

<style lang="scss">
  .alarm-item {
    text-align: center;
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(250, 44, 25, 1);
    height: 20px;
    background: rgba(255, 241, 240, 1);
    border-radius: 4px;
    padding: 2px 4px;
    margin-bottom: 4px;
    margin-right: 8px;
  }

  .report-time {
    margin-left: 4px;
  }
</style>

<script type="application/json">
  { "component": true }
</script>
