declare module '*?fallback=true' {
  const URL: string;
  export default URL;
}
declare type AnyFunc = (...args: any[]) => any;
declare module 'wx';
declare interface RequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | string;
  url: string;
  data?: any;
  absoluteURL?: string;
  contentType?:
    | 'application/json'
    | 'application/x-www-form-urlencoded'
    | 'text/plain'
    | 'multipart/form-data'
    | ''
    | 'formData';
  urlParams?: {
    [key: string]: string | number | null;
  };
  headers?: {
    [key: string]: string;
  };
  timeout?: number;
  useMock?: boolean;
  mockData?: any;
}
