<template>
  <image
    src="{{IndexPageUrl.Copy}}"
    class="copy-icon"
    mode="aspectFit"
    catchtap="handleCopy"
    data-text="{{code}}"
  />
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { IndexPageUrl } from 'shared/assets/imageUrl';

  createComponent({
    data: {
      IndexPageUrl: IndexPageUrl
    },
    properties: {
      code: {
        type: String,
        value: ''
      }
    },

    methods: {
      handleCopy(e: any) {
        const text = this.properties.code;
        if (!text) {
          wx.showToast({
            title: '没有可复制的内容',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        wx.setClipboardData({
          data: text,
          success: () => {
            wx.showToast({
              title: '复制成功',
              icon: 'success',
              duration: 1000
            });
          },
          fail: error => {
            console.error('Copy failed:', error);
            wx.showToast({
              title: '复制失败',
              icon: 'none',
              duration: 1000
            });
          }
        });
      }
    }
  });
</script>

<style lang="scss">
  .copy-icon {
    width: 15px;
    height: 15px;
    margin-left: 8px;
  }

  .copy-icon:active {
    opacity: 0.7;
  }
</style>

<script type="application/json">
  {
    "component": true
  }
</script>