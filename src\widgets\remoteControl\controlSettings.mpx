<template>
  <t-popup
    visible="{{visible}}"
    bind:visible-change="closePopup"
    placement="{{placement}}"
    style="{{placement === 'right' ? 'width: 284px; height: 100%;border-radius: 16px 0 0 16px' : 'width: 100%; height: 198px;border-radius: 16px 16px 0 0'}}"
    close-on-overlay-click="{{true}}"
  >
    <view class="top-bar">
      <view class="popup-title">设置</view>
      <view
        wx:class="{{{ 'close-icon-wrapper': placement === 'right', 'close-icon-wrapper-bottom': placement === 'bottom' }}}"
        bindtap="closePopup"
      >
        <view
          class="close-icon"
          style="background: url({{placement === 'right' ? RemoteControlUrl.ClosePopupWhite : RemoteControlUrl.ClosePopup}}) no-repeat
                center/100%; width: {{ placement === 'right' ? '20px' : '14px'}}; height: {{ placement === 'right' ? '20px' : '14px'}};"
        ></view>
      </view>
    </view>
    <view
      wx:class="{{{ 'settings-wrapper': placement === 'right',  'settings-wrapper-bottom': placement === 'bottom'}}}"
    >
      <view class="setting-item">
        <view class="label">震动反馈</view>
        <view class="operation">已开启</view>
      </view>
      <view class="setting-item">
        <view class="label">{{
          placement === 'right' ? '单手操作' : '双手操作'
        }}</view>
        <view class="operation" bindtap="goControl"
          >进入模式<view
            style="background: url({{RemoteControlUrl.RightArrow}}) no-repeat
                center/100%; width: 16px; height: 16px;"
          ></view
        ></view>
      </view>
    </view>
  </t-popup>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { RemoteControlUrl } from 'shared/assets/imageUrl';
  createComponent({
    data: {
      RemoteControlUrl: RemoteControlUrl
    },
    properties: {
      placement: {
        type: String,
        value: 'right'
      },
      visible: {
        type: Boolean,
        value: false
      },
      vehicleName: {
        type: String,
        value: ''
      },
      backUrl: {
        type: String,
        value: ''
      }
    },
    methods: {
      closePopup() {
        this.triggerEvent('closepopup');
      },
      goControl() {
        const vehicleName = this.vehicleName;
        const placement = this.placement;
        const backUrl = this.backUrl;
        getApp().globalData.eventType = 'redirect';
        getApp().globalData.redirectUrl =
          placement === 'right'
            ? `/pages/singlehandControl/index?vehicleName=${vehicleName}`
            : `/pages/remoteControl/index?vehicleName=${vehicleName}`;
        getApp().globalData.backUrl = backUrl;
        wx.redirectTo({
          url: `/pages/redirectPage/index`
        });
      }
    }
  });
</script>

<style lang="scss">
  .top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(245, 245, 245, 1);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(26, 26, 26, 1);
    padding: 16px;
    .close-icon-wrapper {
      position: absolute;
      left: -60px;
      top: 180px;
      width: 60px;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-left: 20px;
    }
    .close-icon-wrapper-bottom {
      right: 0px;
      position: absolute;
      width: 60px;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-left: 20px;
    }
  }
  .settings-wrapper,
  .settings-wrapper-bottom {
    width: 100%;
    overflow: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    font-size: 14px;
    .setting-item {
      border-bottom: 1px solid rgba(245, 245, 245, 1);
      height: 52px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .operation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fa2c19;
        height: 100%;
      }
    }
  }
  .settings-wrapper-bottom {
    padding: 0px 16px 16px 16px;
  }
</style>
<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-popup': 'tdesign-miniprogram/popup/popup'
    }
  };
</script>
