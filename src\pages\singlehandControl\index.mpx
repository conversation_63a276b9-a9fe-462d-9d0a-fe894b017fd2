<template>
  <view class="singlehand-page">
    <navi-bar
      show-address="{{false}}"
      show-return="{{true}}"
      title="单手遥控"
      back-url="{{backUrl}}"
      is-navigate-bump="{{isNavigateBump}}"
    ></navi-bar>
    <view class="vehiclename-wrapper" bindtap="goSelectVehicle">
      <view class="vehicle-name">{{ vehicleName }}</view>
      <view
        class="select-vehicle"
        style="background: url({{RemoteControlUrl.SelectVehicleIcon}}) no-repeat center/100%"
      ></view>
    </view>
    <view class="command-area" id="sgm-single-control">
      <delay-info
        wx:if="{{isControlStart}}"
        delay-info="{{responseData}}"
      ></delay-info>
      <button
        plain="{{true}}"
        class="custom-btn"
        hover-class="custom-btn-hover"
        wx:if="{{!isControlStart}}"
        bindtap="startControl"
        disabled="{{startBtnDisabled}}"
      >
        <view
          style="
              background: url({{RemoteControlUrl.ControlStart}}) no-repeat
                center/100%;
            "
          class="custom-icon"
        ></view>
        开启遥控器
      </button>
      <button
        plain="{{true}}"
        class="custom-btn"
        hover-class="custom-btn-hover"
        wx:if="{{isControlStart}}"
        bindtap="endControl"
        disabled="{{endBtnDisabled}}"
      >
        <view
          style="
              background: url({{RemoteControlUrl.ControlEnd}}) no-repeat
                center/100%;
            "
          class="custom-icon"
        ></view>
        关闭遥控器
      </button>
      <button
        plain="{{true}}"
        class="custom-btn"
        hover-class="custom-btn-hover"
        wx:if="{{!isControlStart}}"
        bindtap="openSettingModal"
      >
        <view
          style="
              background: url({{RemoteControlUrl.ControlSetting}}) no-repeat
                center/100%;
            "
          class="custom-icon"
        ></view
        >遥控设置
      </button>
      <view class="command-btn-area" wx:show="{{isControlStart}}">
        <view
          wx:class="{{{ 'force-control-btn-wrapper': !isForceControl, 'force-control-end-btn-wrapper': isForceControl }}}"
          bindtap="onChangeForceControl"
        >
          <view
            class="force-control-icon"
            style="background: url({{isForceControl ? RemoteControlUrl.ForceControlEnd : RemoteControlUrl.ForceControlStart}}) no-repeat center/100%"
          ></view>
          <view
            wx:class="{{{'force-control-label': !isForceControl, 'force-control-end-label': isForceControl}}}"
            >{{ isForceControl ? '关强遥' : '开强遥' }}</view
          >
        </view>
        <command-btn
          wx:for="{{remoteCommandList}}"
          wx:key="key"
          openCommand="{{item.openCommand}}"
          openLabel="{{item.openLabel}}"
          closeCommand="{{item.closeCommand}}"
          closeLabel="{{item.closeLabel}}"
          iconSrc="{{item.iconSrc}}"
          closeIconSrc="{{item.closeIconSrc}}"
          bindsendcommand="sendCommand"
          size="medium"
        ></command-btn>
      </view>
    </view>
    <single-joystick
      angle="{{angle}}"
      speed="{{speed}}"
      isControlStart="{{isControlStart}}"
      bindgetangleandspeed="getAngleAndSpeed"
      bindstartwslistener="startWsListener"
      bindremovewslistener="removeWsListener"
      bindbrake="brake"
    >
    </single-joystick>
  </view>
  <control-settings
    visible="{{showSettings}}"
    bindclosepopup="closeSettingModal"
    placement="bottom"
    vehicleName="{{vehicleName}}"
    backUrl="{{backUrl}}"
  ></control-settings>
</template>

<script lang='ts'>
  import { createPage } from '@mpxjs/core';
  import { RemoteControlUrl } from 'shared/assets/imageUrl';
  import * as dayjs from 'dayjs';
  import {
    REMOTE_COMMAND,
    REMOTE_CONTROL,
    WSReadyState,
    COMMAND_TYPE
  } from 'shared/utils/constant';
  import WebSocketClient from 'shared/api/websocket';
  import useLoginStore from 'shared/store/useLoginStore';
  import { mapStores } from '@mpxjs/pinia';
  import RemoteControlApi, {
    getDistanceInMeters
  } from 'shared/api/remoteControl';
  import { CommandApi } from 'shared/api/command';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { getRemoteControlUrl } from 'shared/utils/config';
  const globalEvent = require('shared/utils/event');
  let requestId = 0;
  const coefficient = 0.04;
  createPage({
    data: {
      RemoteControlUrl,
      fetchApi: new RemoteControlApi(),
      commanApi: new CommandApi(),
      isControlStart: false,
      showSettings: false,
      isForceControl: false,
      vehicleName: '',
      WSReadyState,
      wsClient: null as any,
      wsTimer: null as any,
      wsListener: null as any,
      responseData: null as any,
      commandType: COMMAND_TYPE.MOBILE,
      getDistanceInMeters,
      angle: 0,
      speed: 0,
      cv: 0,
      backUrl: '/pages/workbench/index',
      remoteCommandList: [
        {
          key: 'flashLight',
          openCommand: REMOTE_COMMAND.FLASH_LIGHT_OPEN,
          closeCommand: REMOTE_COMMAND.FLASH_LIGHT_CLOSE,
          openLabel: '开双闪',
          closeLabel: '关双闪',
          iconSrc: RemoteControlUrl.FlashLight,
          closeIconSrc: RemoteControlUrl.FlashLightClose
        },
        {
          key: 'lowLight',
          openCommand: REMOTE_COMMAND.LOW_LIGHT_OPEN,
          closeCommand: REMOTE_COMMAND.LOW_LIGHT_CLOSE,
          openLabel: '开大灯',
          closeLabel: '关大灯',
          iconSrc: RemoteControlUrl.LowLight,
          closeIconSrc: RemoteControlUrl.LowLightClose
        },
        {
          key: 'leftTurnLight',
          openCommand: REMOTE_COMMAND.LEFT_TURN_LIGHT_OPEN,
          closeCommand: REMOTE_COMMAND.LEFT_TURN_LIGHT_CLOSE,
          openLabel: '开左转灯',
          closeLabel: '关左转灯',
          iconSrc: RemoteControlUrl.LeftTurnLight,
          closeIconSrc: RemoteControlUrl.LeftTurnLightClose
        },
        {
          key: 'rightTurnLight',
          openCommand: REMOTE_COMMAND.RIGHT_TURN_LIGHT_OPEN,
          closeCommand: REMOTE_COMMAND.RIGHT_TURN_LIGHT_CLOSE,
          openLabel: '开右转灯',
          closeLabel: '关右转灯',
          iconSrc: RemoteControlUrl.RightTurnLight,
          closeIconSrc: RemoteControlUrl.RightTurnLightClose
        }
      ],
      startBtnDisabled: false,
      endBtnDisabled: false
    },
    computed: {
      ...mapStores(useLoginStore)
    },
    onLoad(query: any) {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
      if (query?.vehicleName) {
        this.setData({
          vehicleName: query.vehicleName
        });
        this.fetchApi.whistle(query.vehicleName);
      }
      const backUrl = getApp().globalData.backUrl;
      const eventType = getApp().globalData.eventType;
      const isNavigateBump = getApp().globalData.isNavigateBump;
      if (isNavigateBump) {
        this.setData({
          isNavigateBump: isNavigateBump
        });
      }
      if (eventType === 'navigate') {
        this.setData({
          backUrl: backUrl
        });
      }
    },
    onHide() {
      this.removeWsListener({});
      if (this.vehicleName && this.wsClient) {
        this.brake();
        this.recovery(this.vehicleName);
        this.removeWsListener({});
      }
    },
    onUnload() {
      this.removeWsListener({});
      if (this.vehicleName && this.wsClient) {
        this.brake();
        this.recovery(this.vehicleName);
        this.removeWsListener({});
      }
    },
    methods: {
      // ws 60s无操作监听
      startWsListener(e: any) {
        this.wsListener && clearTimeout(this.wsListener);
        if (this.wsClient) {
          this.wsListener = setTimeout(() => {
            this.endControl();
          }, 60000);
        }
      },
      removeWsListener(e: any) {
        this.wsListener && clearTimeout(this.wsListener);
      },
      getAngleAndSpeed(e: any) {
        this.setData({
          angle: e.detail.angle,
          speed: e.detail.speed
        });
      },
      // main方法：下发角度、速度
      formatSendMessage() {
        requestId += 1;
        const tv = this.speed;
        if (tv === 0 || (tv > 0 && this.cv < 0) || (tv < 0 && this.cv > 0)) {
          this.setData({
            cv: 0
          });
          const data = {
            eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
            data: {
              id: requestId,
              vehicleName: this.vehicleName,
              commandType: this.commandType,
              moduleName: 'supervisor',
              timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              targetVelocity: 0,
              targetAngle: this.angle
            }
          };
          this.wsClient.sendMessage(data);
        } else {
          if (tv - this.cv > 0) {
            const speed = this.cv + coefficient;

            if (speed <= tv) {
              this.setData({
                cv: speed
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: speed,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            } else {
              this.setData({
                cv: tv
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: tv,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            }
          } else if (tv - this.cv < 0) {
            const speed = this.cv - coefficient;
            if (speed >= tv) {
              this.setData({
                cv: speed
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: speed,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            } else {
              this.setData({
                cv: tv
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: tv,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            }
          } else {
            this.setData({
              cv: tv
            });
            const data = {
              eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
              data: {
                id: requestId,
                vehicleName: this.vehicleName,
                commandType: this.commandType,
                moduleName: 'supervisor',
                timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                targetVelocity: tv,
                targetAngle: this.angle
              }
            };
            this.wsClient.sendMessage(data);
          }
        }
      },
      // 监听ws创建成功
      handleOnOpen() {
        this.handleBroadcast('进入移动端摇车', this.vehicleName);
        this.wsTimer = setInterval(() => {
          this.formatSendMessage();
        }, 100);
      },
      // 远程语音播报
      handleBroadcast(value: string, vehicleName: string) {
        this.fetchApi.broadcast(value, this.vehicleName);
      },
      // 遥控超时，下发一次RESET
      handleRestControl() {
        requestId += 1;
        const data = {
          eventType: REMOTE_CONTROL.REMOTE_REQUEST_RESET_ABNORMAL_CONTROL,
          data: {
            id: requestId,
            vehicleName: this.vehicleName,
            commandType: 'MOBILE',
            moduleName: 'supervisor',
            timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
        };
        this.wsClient.sendMessage(data);
      },
      // 急停
      async stop(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        try {
          const res: any = await this.fetchApi.openControl(vehicleName);
          if (res.code === HTTPSTATUSCODE.Success) {
            const userName = wx.getStorageSync('userName');
            // 建立长链
            if (!this.wsClient && userName) {
              const wsUrl = `${getRemoteControlUrl()}/minimonitor/client/ws/mobile/control?userName=${userName}`;
              const token = wx.getStorageSync('JD_AUTH_TOKEN');
              this.wsClient = new WebSocketClient(
                wsUrl,
                {
                  onOpen: (data: any) => {
                    this.handleOnOpen();
                    console.log('ws opened');
                  },
                  onClose: this.onClose,
                  onError: this.onError,
                  onMessage: (data: any) => {
                    this.onMessage(data);
                  },
                  initMessage: {
                    eventType: 'REMOTE_REQUEST_ENTER_CONTROL',
                    data: {
                      vehicleName: this.vehicleName,
                      timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
                    }
                  }
                },
                token
              );
            }
            this.setData({
              isControlStart: true,
              startBtnDisabled: false
            });
            this.startWsListener({});
            wx.hideLoading();
          } else {
            wx.hideLoading();
            wx.showToast({
              title: res.message,
              icon: 'none',
              duration: 3000
            });
            this.setData({
              startBtnDisabled: false
            });
          }
        } catch (err) {
          console.error(err);
          this.setData({
            startBtnDisabled: false
          });
        }
      },
      // 恢复
      async recovery(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        this.setData({
          endBtnDisabled: true
        });
        try {
          this.wsTimer && clearInterval(this.wsTimer);
          const res: any = await this.fetchApi.closeControl(vehicleName);
          // 断开长链
          this.wsClient &&
            this.wsClient.sendMessage({
              eventType: 'REMOTE_REQUEST_QUIT_CONTROL',
              data: {
                vehicleName: this.vehicleName,
                timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
              }
            });
          this.wsClient && this.wsClient.closeSocket();
          this.wsClient = null;
          this.wsTimer = null;
          this.setData({
            isControlStart: false,
            commandType: COMMAND_TYPE.MOBILE,
            isForceControl: false,
            endBtnDisabled: false
          });
          if (res.code === HTTPSTATUSCODE.Success) {
            this.handleBroadcast('退出移动端摇车', this.vehicleName);
            wx.hideLoading();
          } else {
            wx.hideLoading();
            wx.showToast({
              title: res.message,
              icon: 'none',
              duration: 3000
            });
          }
        } catch (err) {
          this.setData({
            endBtnDisabled: false
          });
          console.error(err);
        }
      },
      // 急刹
      async brake() {
        try {
          if (this.wsClient) {
            const res = await this.fetchApi.brake(this.vehicleName);
            console.log(res);
          }
        } catch (err) {
          console.error(err);
        }
      },
      // 强制遥控
      openForceControl() {
        this.setData({
          isForceControl: true,
          commandType: COMMAND_TYPE.SUPER
        });
      },
      startControl() {
        this.vehicleName && this.getVehiclePosition(this.vehicleName);
      },
      endControl() {
        this.removeWsListener({});
        this.vehicleName && this.recovery(this.vehicleName);
      },
      onMessage(data: any) {
        const res = JSON.parse(data.data);
        if (res) {
          // console.log(JSON.parse(data.data));
          const { eventType } = res;
          if (eventType === 'REMOTE_REQUEST_VEHICLE_RESPONSE') {
            if (res.data.state === 'TIME_EXCEPTION') {
              this.handleRestControl();
            }
            const responseData = res.data;
            const { delayInfo } = res.data;
            if (delayInfo) {
              const supervisorModule = delayInfo.find(
                (item: any) => item.moduleName === 'supervisor'
              );
              const responseTimeStamp = Date.now();
              // 除2取单行通路的延时
              const commandDelay =
                (responseTimeStamp - supervisorModule?.transitTime) / 2 / 1000;
              responseData.commandDelay = commandDelay;
              responseData.responseTimeStamp = responseTimeStamp;
              this.setData({
                responseData
              });
            } else {
              this.setData({
                responseData: null
              });
            }
          }
        }
      },
      onError(data: any) {
        console.log(data);
        console.log('ws error');
      },
      onClose(data: any) {
        console.log(data);
        console.log('ws closed');
      },
      // 用户位置与无人车 距离弹窗
      showPositionModal(vehicleName: string) {
        const that = this;
        wx.showModal({
          content: `当前您与${vehicleName}的距离超过100m。请核对车号，避免接管错车辆。`,
          cancelText: '取消',
          confirmText: '核对无误',
          confirmColor: '#FA4219',
          success: function (res) {
            if (res.confirm) {
              // 下发急停
              that.stop(vehicleName);
            } else if (res.cancel) {
              console.log('用户点击取消');
              that.setData({
                startBtnDisabled: false
              });
            }
          },
          fail(res) {
            console.log(res);
            that.setData({
              startBtnDisabled: false
            });
          }
        });
      },
      // 校验用户位置
      async getVehiclePosition(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        this.setData({
          startBtnDisabled: true
        });
        try {
          const res: any = await this.fetchApi.getVehiclePosition(vehicleName);
          const userLocation = wx.getStorageSync('userLocation');
          if (res.code === HTTPSTATUSCODE.Success) {
            const distance = this.getDistanceInMeters(
              userLocation.latitude,
              userLocation.longitude,
              res.data?.latitude,
              res.data?.longitude
            );
            wx.hideLoading();
            if (distance <= 100 && distance >= 0) {
              // 下发急停
              this.stop(vehicleName);
            } else {
              this.showPositionModal(vehicleName);
            }
          } else {
            wx.hideLoading();
            wx.showToast({
              title: '车辆位置获取失败',
              icon: 'none'
            });
            this.setData({
              startBtnDisabled: false
            });
          }
        } catch (err) {
          this.setData({
            startBtnDisabled: false
          });
          console.error(err);
        }
      },
      openSettingModal() {
        this.setData({
          showSettings: true
        });
      },
      closeSettingModal() {
        this.setData({
          showSettings: false
        });
      },
      onChangeForceControl() {
        if (this.isControlStart && this.isForceControl) {
          this.setData({
            isForceControl: !this.isForceControl,
            commandType: COMMAND_TYPE.MOBILE
          });
          wx.vibrateShort({
            type: 'medium'
          });
        } else if (this.isControlStart && !this.isForceControl) {
          this.openDialog();
        }
      },
      confirmForceControl() {
        this.openForceControl();
        wx.vibrateShort({
          type: 'medium'
        });
      },
      openDialog() {
        wx.showModal({
          content: '确认开启强制遥控，用于窄路通行吗？',
          showCancel: true,
          confirmColor: '#fa2c19',
          success: res => {
            if (res.confirm) {
              this.confirmForceControl();
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      sendCommand(e: any) {
        this.commanApi.sendCommand({
          vehicleName: this.vehicleName,
          commandType: e.detail.command,
          operateTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
        });
      },
      goSelectVehicle(e: any) {
        if (this.isControlStart) {
          wx.showToast({
            title: `请确认关闭${this.vehicleName}的车辆遥控，才可换车`,
            icon: 'none'
          });
          return;
        }
        wx.redirectTo({
          url: '/pages/selectVehiclePages/remoteControlPage'
        });
      }
    }
  });
</script>

<style lang="scss">
  .singlehand-page {
    width: 100%;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(159, 215, 255, 0.5) 0%,
      rgba(255, 226, 226, 0.5) 84.2514518123494%,
      rgba(255, 255, 255, 0.5) 100%
    );
    display: flex;
    flex-direction: column;
    align-items: center;
    .vehiclename-wrapper {
      width: 343px;
      height: 38px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 16px;
      .vehicle-name {
        color: #333333;
        font-size: 14px;
        font-weight: 500;
      }
      .select-vehicle {
        width: 16px;
        height: 16px;
      }
    }
    .command-area {
      height: 40%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding-top: 57px;
      .custom-btn {
        border: 1.5px solid #3c6ef0;
        border-radius: 125px;
        color: #3c6ef0;
        font-size: 14px;
        width: 154px;
        height: 44px;
        margin-top: 16px;
        transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
        .custom-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          opacity: 1;
          transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
        }
      }
      .custom-btn-hover {
        border: 1.5px solid #8ca8f0;
        border-radius: 125px;
        color: #8ca8f0;
        font-size: 14px;
        width: 154px;
        height: 44px;
        margin-top: 16px;
        transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
        .custom-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          opacity: 0.5;
          transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
        }
      }
      .command-btn-area {
        display: flex;
        justify-content: center;
        align-items: center;
        .force-control-btn-wrapper,
        .force-control-end-btn-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 64px;
          height: 60px;
          background: #ffffff;
          color: rgba(100, 100, 100, 1);
          transition: all 0.5s ease-in-out;
          border: 1px solid #00000026;
          border-radius: 4px;
          .force-control-icon {
            width: 20px;
            height: 20px;
            margin-bottom: 5px;
          }
          .force-control-label,
          .force-control-end-label {
            font-size: 10px;
            font-family: PingFang SC;
            font-weight: 500;
            margin-bottom: 8px;
          }
          .force-control-end-label {
            color: #ffffff;
          }
          margin-top: 24px;
          margin-right: 4px;
        }
        .force-control-end-btn-wrapper {
          background: linear-gradient(180deg, #7797f0 0%, #84a4fdb5 100%);
        }
      }
    }
  }
</style>

<script name="json">
  module.exports = {
    usingComponents: {
      'navi-bar': '../../shared/ui/naviBar.mpx',
      'control-settings': '../../widgets/remoteControl/controlSettings.mpx',
      'common-switch': '../../shared/ui/commonSwitch.mpx',
      'command-btn': '../../widgets/remoteControl/commandBtn.mpx',
      'delay-info': '../../widgets/remoteControl/delayInfo.mpx',
      'single-joystick': '../../widgets/remoteControl/singlehandJoystick.mpx'
    },

    renderer: 'skyline',
    componentFramework: 'glass-easel',
    navigationStyle: 'custom',
    pageOrientation: 'portrait',
    disableScroll: true
  };
</script>
