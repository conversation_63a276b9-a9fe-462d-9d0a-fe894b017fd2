import { defineStore } from '@mpxjs/pinia';
import WebSocketClient from 'shared/api/websocket';
import { ref } from '@mpxjs/core';
const useSocketStore = defineStore('socket', () => {
  const socket = ref<any>(null);
  const message = ref<string>('');

  const wsSubscribe = (type: any) => {
    socket.value.subscribe(type, (data: any) => {
      console.log('接受服务端消息', data);
    });
  };
  const sendScoket = (data: any) => {
    socket.value.send(JSON.stringify(data));
  };
  const destroyScoket = () => {
    if (socket.value) {
      // 销毁socket
      socket.value.destroy();
      socket.value = null;
    }
  };
  const wsInit = async (url: string, config: any, token: string) => {
    if (!socket.value) {
      const ws = new WebSocketClient(url, config, token);
      socket.value = ws;
    }
    return socket.value;
  };
  return {
    socket,
    message,
    wsInit,
    wsSubscribe,
    sendScoket,
    destroyScoket
  };
});

export default useSocketStore;
