<template>
  <view class="repair-issue-container">
    <navi-bar
      show-address="{{false}}"
      title="{{title}}"
      show-return="true"
    ></navi-bar>
    <view class="main-container">
      <view class="title-box">{{ vehicleName }}</view>
      <view class="fields-group">
        <view class="title">维修结果</view>
        <view wx:if="{{repairDetail?.type == 'REPAIR_CONFIRM'}}">
          <label-field
            labelText="维修完成时间"
            value="{{repairDetail?.finishTime || '--'}}"
            type="inline"
          />
          <t-divider />
          <label-field
            labelText="备注信息"
            value="{{repairDetail?.completeRequireDesc}}"
            type="vertical"
          />
        </view>
        <view wx:elif="{{repairDetail?.type == 'REPAIR_REJECT'}}">
          <label-field
            labelText="驳回时间"
            value="{{repairDetail?.rejectTime || '--'}}"
            type="inline"
          />
          <t-divider />
          <label-field
            labelText="驳回原因"
            value="{{repairDetail?.rejectDesc}}"
            type="vertical"
          />
        </view>
      </view>
      <view class="fields-group">
        <view class="title">提报信息</view>
        <label-field
          labelText="提报时间"
          value="{{repairDetail?.reportTime}}"
          type="inline"
        />
        <t-divider />
        <label-field
          labelText="问题影响"
          value="{{repairDetail?.isInfluenceOperationName || '--'}}"
          type="inline"
        />
        <t-divider />
        <label-field
          labelText="问题描述"
          value="{{repairDetail?.reportDesc}}"
          type="vertical"
        />
        <t-divider />
        <label-field
          labelText="维修位置"
          value="{{repairDetail?.requireHardwareTypeNames || '--'}}"
          type="vertical"
        />
        <preview-media mediaList="{{repairDetail?.attachmentList || []}}" />
      </view>
      <view class="fields-group">
        <view class="title">维修记录</view>
        <view class="record-list"><step-line stepList="{{recordList}}" /></view>
      </view>
    </view>
    <view class="footer">
      <view class="btn" bindtap="handleReport">{{ btnText }}</view>
    </view>
  </view>
</template>
<script lang='ts'>
  import { createPage } from '@mpxjs/core';
  import { AccidentDetial } from 'shared/api/accidentDetail';
  import { HTTPSTATUSCODE } from '../../../shared/api/fetch';
  createPage<any>({
    data: {
      accidentApi: new AccidentDetial(),
      vehicleName: '',
      messageId: '',
      repairDetail: {}
    },

    methods: {
      handleReport() {
        this.accidentApi
          .repairOperate({
            repairNumber: this.repairDetail?.repairNumber,
            messageId: this.messageId,
            operateType:
              this.repairDetail?.type == 'REPAIR_CONFIRM' ? 'CONFIRM' : 'KNOW'
          })
          .then((res: any) => {
            if (res.code === HTTPSTATUSCODE.Success) {
              wx.showToast({
                icon: 'none',
                title: '操作成功'
              });
              setTimeout(() => {
                wx.navigateBack({
                  delta: 1
                });
              }, 1500);
            }
          });
      },
      getRepairDetail(messageId: string) {
        this.accidentApi.getRepairDetail(messageId).then((res: any) => {
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              repairDetail: res?.data
            });
          }
          console.log(res);
        });
      }
    },
    computed: {
      recordList() {
        return this.repairDetail?.recordList?.map((item: any) => ({
          title: item.title,
          desc: item.content
        }));
      },
      btnText() {
        return this.repairDetail?.type == 'REPAIR_CONFIRM'
          ? '确认可用'
          : '我已知晓';
      },
      title() {
        return this.repairDetail?.type == 'REPAIR_CONFIRM'
          ? '维修单确认'
          : '维修单驳回';
      }
    },
    onLoad(options: { vehicleName: string; messageId: string }) {
      this.vehicleName = options.vehicleName;
      this.messageId = options.messageId;
      this.getRepairDetail(options.messageId);
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>
<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    flex-flow: column;
  }
  .main-container {
    height: calc(100vh - 174px);
    overflow-y: auto;
    display: flex;
    flex-flow: column;
  }
  .title-box {
    margin: 16px 16px 8px;
    flex: 0 0 38px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    text-align: center;
    line-height: 38px;
  }
  .fields-group {
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    margin: 0 16px 8px;
    padding: 12px;
    .title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: bold;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 12px;
    }
  }
  .record-list {
    background: rgba(248, 248, 248, 1);
    border: 1px solid rgba(245, 245, 245, 1);
    border-radius: 4px 4px 0 0;
    padding: 12px;
    overflow-y: auto;
    margin-top: 8px;
  }
  .footer {
    height: 90px;
    background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      width: 80%;
      height: 40px;
      border-radius: 40px;
      background: linear-gradient(
        90deg,
        rgba(242, 0, 0, 1) 0%,
        rgba(255, 79, 24, 1) 100%
      );
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      line-height: 40px;
    }
  }
</style>
<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../../shared/ui/naviBar.mpx",
      "label-field": "../../../shared/ui/labelField.mpx",
      "t-divider": "tdesign-miniprogram/divider/divider",
      "step-line": "../../../shared/ui/stepLine.mpx",
      "preview-media": "../../../shared/ui/previewMedia.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
