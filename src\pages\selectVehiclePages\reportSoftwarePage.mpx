
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="问题提报"
    backUrl="/pages/workbench/index"
  ></navi-bar>

  <vehicle-list
    selectedVehicleName="{{selectedVehicleName}}"
    tip="选择车辆后，进入问题提报页面"
    whichPageToGo="/pages/notification/softwareReport/index"
    bind:handleSelect="handleSelect"
  />
</template>

<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  interface Data {
    selectedVehicleName: any;
  }
  createPage<Data>({
    data: {
      selectedVehicleName: null
    },
    onLoad() {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    methods: {
      handleSelect(e: any) {
        this.selectedVehicleName = e.detail.val;
        if (e.detail.val) {
          wx.navigateTo({
            url: `/pages/notification/softwareReport/index?vehicleName=${e.detail.val}`
          });
        }
      }
    },
    onShow: function () {
      if (getApp().globalData.operateSelectVehicle) {
        this.selectedVehicleName = getApp().globalData.operateSelectVehicle;
      }
    },
    onHide: function () {
      getApp().globalData.operateSelectVehicle = null;
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx",
      "restart-operate": "widgets/commandOperate/restartOperate.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
