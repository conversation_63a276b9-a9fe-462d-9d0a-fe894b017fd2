/**
 * 更多配置参数详情见： https://joyspace.jd.com/pages/ZUjbs3PTLLzDEYS9P0ZA?block=VxGscr
 */
export const config = {
  appid: 2435, // 请使用自己申请的登录 appid , 非小程序 appid, 请前往：http://wsp.m.jd.com/admin/main.htm 查看对应 appid
  returnPage: '/pages/my/index',
  pageType: 'switchTab',
  isLogout: true,
  noWXinfo: undefined,
  h5path: undefined,
  logoPath: undefined,
  isTest: undefined, // 1 预发接口，改为undefined 调用线上接口
  isKepler: undefined,
  navigationBarColor: '#ffffff',
  navigationBarTitle: undefined,
  tabNum: 2,
  requestHost: 'https://wxapplogin2.m.jd.com',
  logPluginName: 'DDDDDDDDD', // 埋点插件的名字 例如：'log-plugin'
  selfTipsDialog: false, // 是否弹窗展示协议授权，默认为false，如果为true，author必须为false
  author: true,
  appName: 'Demo',
  appIcon:
    'https://img13.360buyimg.com/imagetools/jfs/t1/158157/13/41034/9717/64f03a3fF364ca9ae/5b05e95ef602bd94.png',
  hiddenLoginType: '2' // 隐藏按钮 1为 帐密方式；2为微信手机号快捷登录
  // loginConfig:{
  //   jdLoginBtnStyle: 'color:red', // 登录/注册按钮样式
  //   wxBtnStyle:'color:red', // 微信快捷登录按钮样式
  //   userPlaceholder:'', // 帐密登录的 placeholder
  //   loginBtnStyle: 'color:red', // 帐密登录界面登录按钮样式
  //   tabStyle:'color:green',//帐密和手机号登录tab样式
  //   currentTabStyle:'border-color:green'//帐密和手机号登录tab选中
  // }
  // selfTips: [{  //无特殊需求不需要配置
  //   tip:'我是测试1',
  //   url: 'm.jd.com'
  // }, {
  //   tip:'我是测试2',
  //   url:'https://pro.m.jd.com/mall/active/2hqsQcyM5bEUVSStkN3BwrBHqVLd/index.html'
  // }]
};
