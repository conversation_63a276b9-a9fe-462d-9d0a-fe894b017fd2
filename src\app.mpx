<script>
  import mpx, { createApp } from '@mpxjs/core';
  import apiProxy from '@mpxjs/api-proxy';
  import { createPinia } from '@mpxjs/pinia';
  import { checkUpdate, checkLogin } from 'shared/utils/utils';
  import tmap from 'shared/utils/tencentMap';
  import { TMapBaseResponse } from 'shared/types/TMapOptions';
  // if (__mpx_env__ !== 'production') {
  //   const SgmMpSDK = require('@jd/sgm-mp');
  //   const sgmSdk = new SgmMpSDK({
  //     sid: '9c6a9cb705e44baf9d08ac71c16d1e1d',
  //     pid: '9HwAEg@DWMscUYHqq/OyUZf'
  //   });
  // }

  mpx.use(apiProxy, { usePromise: true });
  const pinia = createPinia();
  App({
    onLaunch() {
      wx.loadFontFace({
        family: 'JDLangZhengTi',
        global: true,
        source:
          "url('//storage.360buyimg.com/fatal-solution/JDLangZhengTi_Regular.TTF?Expires=3811060668&AccessKey=n828WHAXD584pTvi&Signature=nZhyNcGpDjiJmYm31qLAqe8%2Bd54%3D')",
        success: console.log
      });
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight;
      const titleBarHeight =
        wx.getMenuButtonBoundingClientRect()?.bottom +
        wx.getMenuButtonBoundingClientRect()?.top -
        systemInfo.statusBarHeight * 2;
      Object.assign(this.globalData, systemInfo);
      Object.assign(this.globalData, {
        ...this.globalData,
        statusBarHeight,
        titleBarHeight
      });
      checkUpdate();
      checkLogin();
      const _this = this;
      wx.authorize({
        scope: 'scope.userLocation',
        success() {
          wx.getLocation({
            type: 'gcj02',
            success(res) {
              wx.setStorageSync('userLocation', {
                latitude: res.latitude,
                longitude: res.longitude
              });
              wx.showLoading({
                title: '加载中',
                mask: true
              });
              tmap.reverseGeocoder({
                location: {
                  latitude: res.latitude,
                  longitude: res.longitude
                },
                success(res) {
                  wx.setStorageSync('userAddress', res.result.address);
                  wx.hideLoading();
                }
              });
            }
          });
        },
        fail() {
          wx.showModal({
            title: '请确认位置信息',
            content:
              '定位获取失败，请您授权小程序获取地理位置（操作路径：右上角-设置-位置信息改为“使用小程序时允许”）',
            showCancel: false,
            confirmText: '我知道了',
            success(res) {
              if (res.confirm) {
                wx.openSetting({
                  success(res) {
                    if (res.authSetting['scope.userLocation']) {
                      wx.getLocation({
                        type: 'gcj02',
                        success(res) {
                          wx.setStorageSync('userLocation', {
                            latitude: res.latitude,
                            longitude: res.longitude
                          });
                          wx.showLoading({
                            title: '加载中',
                            mask: true
                          });
                          tmap.reverseGeocoder({
                            location: {
                              latitude: res.latitude,
                              longitude: res.longitude
                            },
                            success(res) {
                              wx.setStorageSync(
                                'userAddress',
                                res.result.address
                              );
                              wx.hideLoading();
                            }
                          });
                        }
                      });
                    }
                  }
                });
              }
            }
          });
        }
      });
    },
    globalData: {}
  });
</script>
<style>
  page {
    --td-brand-color: #fa2c19;
    --td-radio-font-size: 14px;
  }
</style>

<script name="json">
  module.exports = {
    entryPagePath: 'pages/my/index',

    pages: [
      './pages/carTrack/index',
      './pages/notification/index',
      './pages/login/index/index',
      './pages/login/web-view/web-view',
      './pages/login/wv-common/wv-common',
      './pages/notification/bumpDetail/index',
      './pages/notification/bumpForm/index',
      './pages/notification/repairIssue/index',
      './pages/notification/shadow/index',
      './pages/notification/realtime/index',
      './pages/notification/hardwareReport/index',
      './pages/notification/softwareReport/index',
      './pages/vehicle/index',
      './pages/workbench/index',
      './pages/workbench/freeDrive',
      './pages/workbench/resultPage',
      './pages/workbench/openCabinDoor',
      './pages/workbench/anyDrive/index',
      './pages/remoteControl/index',
      './pages/singlehandControl/index',
      './pages/selectVehiclePages/findCarPage',
      './pages/selectVehiclePages/restartPage',
      './pages/selectVehiclePages/carTrackPage',
      './pages/selectVehiclePages/callFrontPage',
      './pages/selectVehiclePages/callDriverPage',
      './pages/selectVehiclePages/loginVehiclePage',
      './pages/selectVehiclePages/searchVehicle',
      './pages/selectVehiclePages/openGridPage',
      './pages/selectVehiclePages/reportSoftwarePage',
      './pages/selectVehiclePages/reportHardwarePage',
      './pages/selectVehiclePages/remoteControlPage',
      './pages/my/index',
      './pages/redirectPage/index',
      './pages/trackMap/index',
      './pages/insurancePreview/index',
      './pages/transitionPage/index',
      './pages/techMaintenance/index'
    ],
    tabBar: {
      custom: true,
      list: [
        {
          pagePath: 'pages/notification/index',
          text: '消息'
        },
        {
          pagePath: 'pages/vehicle/index',
          text: '车辆'
        },
        {
          pagePath: 'pages/workbench/index',
          text: '工作台'
        },
        {
          pagePath: 'pages/my/index',
          text: '我的'
        }
      ],
      selectedColor: '#3cc51f',
      borderStyle: 'black',
      backgroundColor: '#ffffff',
      color: '#000000'
    },
    lazyCodeLoading: 'requiredComponents',
    rendererOptions: {
      skyline: {
        disableABTest: true,
        defaultDisplayBlock: true
      }
    },
    usingComponents: {},
    permission: {
      'scope.userLocation': {
        desc: '你的位置信息将用于小程序位置接口的效果展示'
      }
    },
    requiredBackgroundModes: ['location'],
    requiredPrivateInfos: [
      'getLocation',
      'onLocationChange',
      'startLocationUpdate',
      'startLocationUpdateBackground'
    ],
    plugins: {
      loginPlugin: {
        version: '1.8.0',
        provider: 'wxefe655223916819e'
      }
    },
    packages: ['./packages/repairDetail/app.mpx?root=repairDetail'],
    preloadRule: {
      'pages/techMaintenance/index': {
        network: 'all',
        packages: ['repairDetail']
      },
    }
  };
</script>
