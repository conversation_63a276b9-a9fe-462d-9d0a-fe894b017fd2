<template>
  <t-popup
    visible="{{visible}}"
    bind:visible-change="closePopup"
    placement="bottom"
    style="width: 100%; height: 724px; border-radius: 16px 16px 0 0"
    close-on-overlay-click="{{false}}"
  >
    <view class="top-bar">
      <view class="popup-title">搜索车辆</view>
      <view class="close-icon-wrapper" bindtap="closePopup">
        <view
          class="close-icon"
          style="background: url({{RemoteControlUrl.ClosePopup}}) no-repeat
                center/100%; width: 14px; height: 14px;"
        ></view>
      </view>
    </view>
    <view class="search-bar">
      <view class="icon-prefix-wrapper">
        <t-icon name="{{VehicleUrl.SearchIcon}}" size="16px" color="#868D9F" />
      </view>
      <view class="input-wrapper">
        <input
          placeholder="请输入车牌号"
          placeholderStyle="font-size: 14px"
          confirm-type="search"
          class="search-input"
          cursor-color="#FA2C19"
          cursor="{{searchValue.length}}"
          model:value="{{searchValue}}"
          maxlength="{{10}}"
          model:focus="{{isSearchFocused}}"
          always-embed="{{true}}"
          bindconfirm="onSearch"
          bindfocus="onFocus"
          bindinput="onInput"
        />
      </view>
      <view class="icon-suffix-wrapper" bindtap="onClear">
        <t-icon name="close-circle-filled" size="16px" color="#868D9F" />
      </view>
    </view>

    <view class="history-container" wx:if="{{!searchValue}}">
      <view class="del">
        <view>搜索记录</view>
        <view
          style="color: rgba(128, 128, 128, 1); display: flex"
          bindtap="handleDel"
        >
          <t-image
            src="{{WorkBenchUrl.DelIcon}}"
            mode="aspectFill"
            width="20px"
            height="20px"
          />清空
        </view>
      </view>

      <view class="his-vehicle-container">
        <view
          class="his-vehicle"
          wx:for="{{historyVehicleList}}"
          wx:key="item"
          bindtap="selectVehicle(item, $event)"
        >
          {{ item }}
        </view></view
      >
    </view>
    <scroll-view class="vehicle-list-wrapper" type="list" scroll-y wx:else>
      <view
        class="vehicle-item"
        wx:for="{{filteredVehicleList}}"
        wx:key="vehicleName"
        bindtap="selectVehicle(item.vehicleName, $event)"
      >
        <view
          class="info"
          wx:class="{{{offline:item.systemStatus===SystemStatus.OFFLINE}}}"
        >
          <view class="vehicle-name" wx:if="{{item.segments}}"
            ><view
              wx:for="{{item.segments}}"
              wx:key="index"
              wx:for-item="segment"
              class="{{segment.highlighted ? 'highlight' : ''}}"
            >
              {{ segment.text }}
            </view></view
          >
          <text wx:else class="vehicle-name">{{ item.vehicleName }}</text>
          <text class="description" wx:if="{{item.takeoverStatus}}"
            >{{ TakeOverTypeName[item.takeoverStatus] }}-{{
              TakeOverSourceTypeName[item.takeoverSource]
            }}-{{ item.takeoverUserName }}</text
          >
        </view>
      </view>
    </scroll-view>
  </t-popup>
</template>
<script lang='ts'>
  import { createComponent } from '@mpxjs/core';
  import {
    RemoteControlUrl,
    WorkBenchUrl,
    VehicleUrl
  } from 'shared/assets/imageUrl';
  import { CommonApi } from 'shared/api/common';
  import {
    SystemStatus,
    TakeOverType,
    TakeOverTypeName,
    TakeOverSourceType,
    TakeOverSourceTypeName
  } from 'shared/utils/constant';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  createComponent({
    data: {
      fetchApi: new CommonApi(),
      RemoteControlUrl,
      WorkBenchUrl,
      VehicleUrl,
      TakeOverType,
      TakeOverTypeName,
      TakeOverSourceType,
      TakeOverSourceTypeName,
      SystemStatus,
      searchValue: '',
      vehicleList: [],
      filteredVehicleList: [],
      historyVehicleList: [],
      isSearchFocused: false
    },
    properties: {
      visible: {
        type: Boolean,
        value: false
      }
    },
    watch: {
      visible(val) {
        if (val) {
          this.getUserVehicleList();
          this.getHisVehicleList();
        } else {
          this.setData({
            searchValue: '',
            isSearchFocused: false,
            filteredVehicleList: this.vehicleList
          });
        }
      }
    },
    methods: {
      closePopup() {
        this.setData({
          searchValue: '',
          isSearchFocused: false,
          filteredVehicleList: this.vehicleList
        });
        this.triggerEvent('closepopup');
      },
      handleDel() {
        this.clearHisVehicleList();
      },
      async getUserVehicleList() {
        try {
          const userLocation = wx.getStorageSync('userLocation');
          const res: any = await this.fetchApi.getUserVehicleList(
            userLocation.longitude,
            userLocation.latitude
          );
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              vehicleList: res.data,
              filteredVehicleList: res.data
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      async getHisVehicleList() {
        try {
          const res: any = await this.fetchApi.getHistoryVehicle();
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              historyVehicleList: res.data
            });
          } else {
            wx.showToast({
              title: res.message,
              icon: 'none'
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      async addHisVehicleList(vehicleName: string) {
        try {
          const res: any = await this.fetchApi.addHistoryVehicle(vehicleName);
          if (res.code === HTTPSTATUSCODE.Success) {
            console.log('添加车辆搜索记录成功！');
          } else {
            wx.showToast({
              title: res.message,
              icon: 'none'
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      async clearHisVehicleList() {
        try {
          const res: any = await this.fetchApi.clearHistoryVehicle();
          if (res.code === HTTPSTATUSCODE.Success) {
            console.log('清除车辆历史搜索记录成功！');
            this.setData({
              historyVehicleList: []
            });
          } else {
            wx.showToast({
              title: res.message,
              icon: 'none'
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      selectVehicle(vehicleName: any, e: any) {
        this.triggerEvent('selectvehicle', {
          vehicleName: vehicleName
        });
        this.addHisVehicleList(vehicleName);
      },

      formatSearchRes(value: any) {
        const keyword = value;
        const vehicles = this.vehicleList;
        const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        if (keyword === '') {
          this.setData({
            searchValue: '',
            filteredVehicleList: vehicles
          });
          return;
        }
        const regex = new RegExp(`(${escapedKeyword})`, 'i');
        const filteredVehicles: any = vehicles
          .filter((vehicle: any) => regex.test(vehicle.vehicleName))
          .map((vehicle: any) => {
            const segments = vehicle.vehicleName
              .split(regex)
              .map((segment: any, index: number) => {
                // 检查当前片段是否为关键字
                const isKeyword = regex.test(segment);
                return {
                  text: segment,
                  highlighted: isKeyword
                };
              });
            return { ...vehicle, segments };
          });
        this.setData({
          searchValue: value,
          filteredVehicleList: filteredVehicles
        });
      },
      onSearch(e: any) {
        this.formatSearchRes(e.detail.value);
        this.setData({
          isSearchFocused: false
        });
        wx.hideKeyboard();
      },
      onClear(e: any) {
        this.setData({
          searchValue: '',
          isSearchFocused: false,
          filteredVehicleList: this.vehicleList
        });
      },
      onFocus(e: any) {
        console.log('onFocus', e);
        this.setData({
          isSearchFocused: true
        });
      },
      onInput(e: any) {
        this.formatSearchRes(e.detail.value);
        this.setData({
          isSearchFocused: true
        });
      }
    }
  });
</script>

<style lang="scss">
  .top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(245, 245, 245, 1);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(26, 26, 26, 1);
    padding: 16px;
    .close-icon-wrapper {
      position: absolute;
      right: 0px;
      width: 60px;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-left: 20px;
    }
  }
  .search-bar {
    width: 90%;
    height: 32px;
    background: rgba(245, 245, 246, 1);
    border-radius: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 12px;
    padding-right: 12px;
    margin-left: 16px;
    margin-top: 6px;
    .input-wrapper {
      width: 100%;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      padding: 0 6px;
      input {
        width: 100%;
        height: 100%;
      }
    }
  }
  .history-container {
    padding-top: 16px;
    padding-right: 16px;
    .del {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
      padding-left: 16px;
    }

    .his-vehicle-container {
      display: flex;
      flex-wrap: wrap;
      padding-left: 4px;
    }
    .his-vehicle {
      height: 24px;
      line-height: 24px;
      background: rgb(245, 245, 245);
      border-radius: 32px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgb(51, 51, 51);
      padding: 0px 6px;
      margin-top: 12px;
      text-align: center;
      margin-left: 8px;
    }
  }
  .vehicle-list-wrapper {
    height: 530px;
    .vehicle-item {
      margin-left: 16px;
      margin-right: 16px;
      height: 52px;
      line-height: 52px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;

      .info {
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(26, 26, 26, 1);
        display: flex;
        &.offline {
          color: rgba(204, 204, 204, 1);
        }
        .vehicle-name {
          font-size: 16px;
          display: flex;
          .highlight {
            color: #fa2c19;
          }
        }
        .description {
          margin-left: 12px;
          font-size: 12px;
        }
      }

      .arrow {
        width: 16px;
        height: 16px;
      }
    }
  }
</style>
<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-icon': 'tdesign-miniprogram/icon/icon',
      't-popup': 'tdesign-miniprogram/popup/popup',
      't-image': 'tdesign-miniprogram/image/image',
      'vehicle-item': '../../shared/ui/vehicleItem.mpx'
    }
  };
</script>
