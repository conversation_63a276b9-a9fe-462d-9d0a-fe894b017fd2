const getAppCode = () => {
  const appCodeMap = new Map([
    ['production', 'rover-k2-minimonitor-ui'],
    ['test1', 'rover-k2-minimonitor-ui-test1'],
    ['beta', 'rover-k2-minimonitor-ui-test1'],
    ['test2', 'rover-k2-minimonitor-ui-test2']
  ]);
  const appCode = appCodeMap.get(__mpx_env__ || 'beta');
  return appCode;
};

const getWebviewHost = () => {
  const WebviewHostMap = new Map([
    ['production', 'https://mcontrol.jdl.cn'],
    ['test1', 'https://jdxvending.jdl.cn'],
    ['beta', 'https://jdxvending.jdl.cn'],
    ['test2', 'https://jdxvending.jdl.cn']
  ]);
  const webviewHost = WebviewHostMap.get(__mpx_env__ || 'beta');
  return webviewHost;
};
const getLOPdomain = () => {
  const LOPDomainMap = new Map([
    ['production', 'https://api-lop.jdl.cn'],
    ['test1', 'https://uat-api-cloud.jdl.cn'],
    ['beta', 'https://uat-api-cloud.jdl.cn']
  ]);
  const domain = LOPDomainMap.get(__mpx_env__ || 'beta');
  return domain;
};
const getLOPDN = () => {
  const LOPDNMap = new Map([
    ['production', 'jdx.k2.minimonitor.public.jsf.product'],
    ['test1', 'jdx.k2.minimonitor.public.jsf.test1'],
    ['beta', 'jdx.k2.minimonitor.public.jsf.beta']
  ]);
  const dn = LOPDNMap.get(__mpx_env__ || 'beta');
  return dn;
};
const getFetchDataUrl = () => {
  const FetchDataUrlMap = new Map([
    ['production', 'https://jdxgateway.jdl.cn'],
    ['test1', 'https://jdxgateway-test1.jdl.cn'],
    ['beta', 'https://jdxgateway-beta.jdl.cn'],
    ['test2', 'https://jdxgateway-test2.jdl.cn']
  ]);
  const url = FetchDataUrlMap.get(__mpx_env__ || 'beta');
  return url;
};

const getRemoteControlUrl = () => {
  const RemoteControlUrlMap = new Map([
    ['production', 'wss://jdxgateway.jdl.cn'],
    ['test1', 'wss://jdxgateway-test1.jdl.cn'],
    ['beta', 'wss://jdxgateway-beta.jdl.cn'],
    ['test2', 'wss://jdxgateway-test2.jdl.cn']
  ]);
  const url = RemoteControlUrlMap.get(__mpx_env__ || 'beta');
  return url;
};

export {
  getAppCode,
  getWebviewHost,
  getFetchDataUrl,
  getRemoteControlUrl,
  getLOPdomain,
  getLOPDN
};
