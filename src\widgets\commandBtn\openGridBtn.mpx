<template>
  <view
    class="open-grid-btn"
    wx:class="{{styleInfo.otherClassName}}"
    bindtap="handleClick"
  >
    <t-image
      src="{{styleInfo.imageSrc}}"
      mode="aspectFill"
      width="{{styleInfo.imageWidth}}"
      height="{{styleInfo.imageHeight}}"
    />
    <text class="label" wx:class="{{styleInfo.otherClassName}}">开舱门</text>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, SingleVehicle } from 'shared/assets/imageUrl';
  import { CheckVehicle, CheckType } from 'shared/utils/checkVehicle';

  createComponent({
    properties: {
      // 'workBench'  ||  'singelVehicle'
      sourceType: {
        type: String,
        value: ''
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      WorkBenchUrl: WorkBenchUrl,
      SingleVehicle: SingleVehicle,
      styleInfo: {}
    },
    methods: {
      formatStyleInfo() {
        if (this.sourceType === 'workBench') {
          this.styleInfo = {
            imageSrc: this.WorkBenchUrl.OpenGrid,
            imageWidth: '93px',
            imageHeight: '87px',
            otherClassName: 'workBench'
          };
        } else {
          this.styleInfo = {
            imageSrc: this.SingleVehicle.OpenHatch,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'singelVehicle'
          };
        }
      },
      handleClick() {
        if (this.sourceType === 'workBench') {
          wx.navigateTo({
            url: '/pages/selectVehiclePages/openGridPage'
          });
        } else if (this.sourceType === 'singleVehicle') {
          const check = new CheckVehicle(
            [{ checkType: CheckType.VehicleStatus }],
            this.vehicleName
          );
          check.checkVehicleFunc().then(res => {
            if (res) {
              getApp().globalData.controlVehicleName = this.vehicleName;
              getApp().globalData.redirectUrl = `/pages/workbench/openCabinDoor?vehicleName=${this.vehicleName}`;
              getApp().globalData.backUrl = '/pages/vehicle/index';
              getApp().globalData.eventType = 'redirect';
              wx.navigateTo({
                url: `/pages/redirectPage/index`
              });
            }
          });
        }
      }
    },
    lifetimes: {
      created: function () {
        this.formatStyleInfo();
      }
    }
  });
</script>

<style lang="scss">
  .open-grid-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    &.workBench {
      width: 93px;
      height: 87px;
      position: relative;
    }
    .label {
      text-align: center;
      font-family: PingFang SC;
      font-weight: normal;
      &.workBench {
        font-size: 12px;
        position: absolute;
        bottom: 10px;
        color: rgba(250, 44, 25, 1);
      }
      &.singelVehicle {
        color: rgba(51, 51, 51, 1);
        font-size: 11px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image"
    }
  }
</script>


