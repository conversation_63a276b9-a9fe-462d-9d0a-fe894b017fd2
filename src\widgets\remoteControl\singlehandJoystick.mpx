<template>
  <view
    class="single-hand-wheel"
    style="background: url({{RemoteControlUrl.SinglehandWheelCircle}}) no-repeat center/100%"
  >
    <pan-gesture-handler onGestureEvent="moveJoystick">
      <view
        class="joystick"
        style="background: url({{RemoteControlUrl.JoyStick}}) no-repeat center/100%"
        bindtouchstart="handleViewVibrate"
      ></view>
    </pan-gesture-handler>
  </view>
</template>
<script>
  import { createComponent } from '@mpxjs/core';
  import { RemoteControlUrl } from 'shared/assets/imageUrl';
  import { singlehandAngleAndSpeed } from 'shared/utils/remoteControl';
  const GestureState = {
    POSSIBLE: 0,
    BEGIN: 1,
    ACTIVE: 2,
    END: 3,
    CANCELLED: 4
  };
  const { shared, spring, runOnJS } = wx.worklet;
  createComponent({
    data: {
      RemoteControlUrl
      // 创建JS线程中位置坐标变量，接收来自UI线程的更新
    },
    properties: {
      isControlStart: {
        type: Boolean,
        value: false
      },
      angle: {
        type: Number,
        value: 0
      },
      speed: {
        type: Number,
        value: 0
      }
    },
    lifetimes: {
      attached() {
        const x = shared(0);
        const y = shared(0);
        const isTouchActive = shared(false);
        const scale = shared(1);
        const center = shared(0);
        this.createSelectorQuery()
          .select('.joystick')
          .boundingClientRect(function (rect) {
            this.center.value = rect.top + rect.width / 2;
          })
          .exec();

        this.applyAnimatedStyle('.joystick', () => {
          'worklet';
          return {
            transform: `translateX(${x.value}px)` + `translateY(${y.value}px)`
          };
        });
        this.applyAnimatedStyle('.single-hand-wheel', () => {
          'worklet';
          return {
            background:
              y.value > 0
                ? `radial-gradient(circle at 50% bottom, rgba(60,110,240,0.5) 0%, rgba(255,255,255,0) ${
                    y.value / 2
                  }%),` +
                  `url(${RemoteControlUrl.SinglehandCircleBackward}) no-repeat center/100%`
                : y.value < 0
                ? `radial-gradient(circle at 50% top, rgba(60,110,240,0.5) 0%, rgba(255,255,255,0) ${Math.abs(
                    y.value / 2
                  )}%),` +
                  `url(${RemoteControlUrl.SinglehandCircleForward}) no-repeat center/100%`
                : `url(${RemoteControlUrl.SinglehandWheelCircle}) no-repeat center/100%`
          };
        });
        this.x = x;
        this.y = y;
        this.scale = scale;
        this.center = center;
        this.isTouchActive = isTouchActive;
      }
    },

    methods: {
      getAngleAndSpeed(x, y) {
        const { speed, angle } = singlehandAngleAndSpeed(x, y);
        this.triggerEvent('getangleandspeed', {
          angle: angle,
          speed: speed
        });
      },
      startWsListenter() {
        this.triggerEvent('startwslistener');
      },
      removeWsListenter() {
        this.triggerEvent('removewslistener');
      },
      brake() {
        this.triggerEvent('brake');
      },
      moveJoystick(evt) {
        'worklet';
        const circleRadius = 130;
        const circleCenterX = 0;
        const circleCenterY = 0;
        // worklet函数中调用页面方法需要bind this
        const brake = this.brake.bind(this);
        const getAngleAndSpeed = this.getAngleAndSpeed.bind(this);
        const startWsListenter = this.startWsListenter.bind(this);
        const removeWsListenter = this.removeWsListenter.bind(this);
        if (evt.state === GestureState.POSSIBLE) {
          this.isTouchActive.value = true;
          this.scale.value = spring(1.2, {}, () => {});
          runOnJS(removeWsListenter)();
        } else if (
          evt.state === GestureState.END ||
          evt.state === GestureState.CANCELLED
        ) {
          this.isTouchActive.value = false;
          this.scale.value = spring(1, {}, () => {});
          this.x.value = 0;
          this.y.value = 0;
          runOnJS(brake)();
          runOnJS(startWsListenter)();
        } else if (evt.state === GestureState.ACTIVE) {
          /**
           *  x: 左负右正
           *  y: 上负下正
           **/
          // 临时计算新的摇杆位置
          const newX = this.x.value + evt.deltaX;
          const newY = this.y.value + evt.deltaY;
          this.x.value = newX;
          this.y.value = newY;
          // 计算触摸点到圆心的距离
          const distanceFromCenter = Math.sqrt(
            (this.x.value - circleCenterX) ** 2 +
              (this.y.value - circleCenterY) ** 2
          );
          let joystickPosition;
          // 如果触摸点在圆内或圆上，直接返回这个点
          if (distanceFromCenter <= circleRadius) {
            joystickPosition = { x: this.x.value, y: this.y.value };
          } else {
            // 如果触摸点在圆外，计算摇杆应该在圆上的位置
            const angle = Math.atan2(
              this.y.value - circleCenterY,
              this.x.value - circleCenterX
            );
            joystickPosition = {
              x: circleCenterX + circleRadius * Math.cos(angle),
              y: circleCenterY + circleRadius * Math.sin(angle)
            };
            this.x.value = joystickPosition.x;
            this.y.value = joystickPosition.y;
          }
        }
        /**
         * worklet函数调用非worklet函数需要用到runOnJs，返回 JS 线程
         * 这里是为了在JS线程中去更新摇杆的位置坐标，保持UI线程和JS线程数据同步
         */
        runOnJS(getAngleAndSpeed)(this.x.value, this.y.value);
      },
      handleViewVibrate() {
        if (this.isControlStart) {
          wx.vibrateShort({
            type: 'heavy',
            success: () => {
              console.log('震动成功');
            },
            fail: () => {
              console.log('震动失败');
            }
          });
        }
      }
    }
  });
</script>
<style lang="scss">
  .single-hand-wheel {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 260px;
    height: 260px;
    border-radius: 130px;
    box-shadow: 0px 0px 20px 3px rgb(125 122 122 / 18%);
    .joystick {
      width: 70px;
      height: 70px;
    }
  }
</style>
<script name='json'>
  module.exports = {
    component: true
  };
</script>
