<template>
  <view class="software-report-container" wx:if="{{currentStep !== 2}}">
    <navi-bar
      wx:if="{{currentStep !== 2}}"
      show-address="{{false}}"
      title="问题提报"
      showReturn="true"
    ></navi-bar>
    <view class="main-container">
      <view class="title-box" wx:if="{{currentStep !== 2}}">{{
        vehicleName
      }}</view>
      <view class="form-group" wx:if="{{currentStep !== 2}}">
        <t-steps current="{{currentStep}}">
          <t-step-item title="选择时间段" icon="Slot">
            <image
              class="step-ico"
              src="https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/time.png?Expires=3873332411&AccessKey=n828WHAXD584pTvi&Signature=slPOG1fFtEVR94vxNu7Eqsmom7w%3D"
            />
          </t-step-item>
          <t-step-item title="问题描述" icon="Slot">
            <image
              class="step-ico"
              src="https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/desc.png?Expires=3873332601&AccessKey=n828WHAXD584pTvi&Signature=dPDf7YcSpn%2FmdwgTKEBvjOcimCs%3D"
            />
          </t-step-item>
          <t-step-item title="提交完成" icon="Slot">
            <image
              class="step-ico"
              src="https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/finish.png?Expires=3873332458&AccessKey=n828WHAXD584pTvi&Signature=a4XZc7%2B0YR9cQ7elZ1s%2BYroUGuE%3D"
            />
          </t-step-item>
        </t-steps>
      </view>
      <view class="form-group" wx:if="{{currentStep == 0}}">
        <view class="field-item inline">
          <view class="form-label" style="flex: 1"> 提报方式 </view>
          <view class="form-options">
            <t-radio value="{{1}}" checked="true" label="已知问题时间" />
          </view>
        </view>
      </view>
      <view wx:if="{{currentStep == 0}}" class="form-group">
        <view class="field-item inline" bindtap="handleShowCanlendar">
          <view class="form-label">日期</view>
          <view class="form-value">
            <text>{{ date }}</text>
          </view>
        </view>
        <t-divider />
        <view class="field-item inline" bindtap="showStartTimePicker">
          <view class="form-label">开始时间</view>
          <view class="form-value">
            <text>{{ startTime }}</text>
          </view>
        </view>
        <t-divider />
        <view class="field-item inline" bindtap="showEndTimePicker">
          <view class="form-label">结束时间</view>
          <view class="form-value">
            <text>{{ endTime }}</text>
          </view>
        </view>
      </view>
      <view wx:elif="{{currentStep == 1}}" class="form-group">
        <view class="field-item flex-columns">
          <view class="label">问题描述</view>
          <t-textarea
            class="textarea"
            placeholder="请输入问题描述"
            max-length="{{50}}"
            value="{{desc}}"
            bindchange="handleInput"
            maxlength="50"
            indicator="true"
          ></t-textarea>
        </view>
        <view class="field-item flex-columns">
          <view class="attachment-wrapper">
            <view class="tips" style="font-size: 14px; color: #808080">
              上传图片（最多可以上传3个）
            </view>
            <upload-image
              mediaType="{{['image']}}"
              maxNum="3"
              bindfileInfo="saveFiles"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="footer" wx:if="{{currentStep<2}}">
      <view wx:if="{{currentStep ==0}}" class="btn primry" bindtap="handleNext">
        下一步
      </view>
      <view wx:if="{{currentStep ==1}}" class="btn normal" bindtap="handlePre">
        上一步
      </view>
      <view
        wx:if="{{currentStep ==1}}"
        class="btn primry"
        bindtap="handleReport"
      >
        提报完成
      </view>
    </view>
    <t-calendar
      visible="{{calendarVisible}}"
      maxDate="{{dateRange.max}}"
      minDate="{{dateRange.min}}"
      defaultValue="{{dateRange.max}}"
      bind:confirm="handleConfirm"
      bind:close="handleCloseCanlendar"
      title="日期选择"
    />
    <t-date-time-picker
      title="{{pickerTitle}}"
      visible="{{pickerVisible}}"
      mode="{{['null', 'second']}}"
      format="HH:mm:ss"
      bindchange="onConfirm"
      bindclose="hidePicker"
      value="{{pickerValue}}"
    />
  </view>

  <view wx:if="{{currentStep == 2}}" class="result">
    <image src="{{WorkBenchUrl.ReportResult}}" />
    <view class="reult-tip">问题提报成功</view>
    <view class="btn primry" bindtap="handleGoBack"> 返回 </view>
  </view>
</template>
<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import { SoftWareIssue } from 'shared/api/softwareIssue';
  import { HTTPSTATUSCODE } from '../../../shared/api/fetch';
  import { WorkBenchUrl } from 'shared/assets/imageUrl';
  import * as dayjs from 'dayjs';
  const fetchApi = new SoftWareIssue();
  createPage<any>({
    data: {
      vehicleName: '',
      currentStep: 0,
      date: dayjs().format('YYYY-MM-DD'),
      startTime: dayjs().format('HH:mm:ss'),
      endTime: dayjs().format('HH:mm:ss'),
      calendarVisible: false,
      pickerTitle: '',
      pickerType: '',
      pickerVisible: false,
      pickerValue: '',
      desc: '',
      attachmentList: [],
      WorkBenchUrl
    },
    computed: {
      dateRange: function () {
        const today = new Date().getTime();
        return {
          max: today,
          min: today - 30 * 24 * 60 * 60 * 1000
        };
      }
    },
    methods: {
      handleInput(e: any) {
        this.setData({
          desc: e?.detail?.value
        });
      },
      handlePre() {
        this.setData({
          currentStep: this.currentStep > 0 ? this.currentStep - 1 : 0
        });
      },
      handleReport() {
        const { vehicleName, date, startTime, endTime, desc, attachmentList } =
          this.data;
        fetchApi
          .issueReport({
            vehicleName: vehicleName,
            startTime: `${date} ${startTime}`,
            endTime: `${date} ${endTime}`,
            description: desc,
            attachmentList: attachmentList
          })
          .then((res: any) => {
            if (res.code === HTTPSTATUSCODE.Success) {
              this.setData({
                currentStep: 2
              });
            }
          });
      },
      handleNext() {
        const { date, startTime, endTime } = this.data;
        const startStr = date.trim() + ' ' + startTime.trim();
        const entStr = date.trim() + ' ' + endTime.trim();
        const start = dayjs(startStr).valueOf();
        const end = dayjs(entStr).valueOf();
        if (end < start) {
          wx.showToast({
            icon: 'none',
            title: '结束时间不能早于开始时间'
          });
          return;
        }
        this.currentStep = this.currentStep < 2 ? this.currentStep + 1 : 2;
      },
      handleShowCanlendar() {
        this.calendarVisible = true;
      },
      handleConfirm(e: any) {
        const { value } = e.detail;
        const date = new Date(value);
        const dateStr = `${date.getFullYear()}-${
          date.getMonth() + 1
        }-${date.getDate()}`;
        this.date = dateStr;
      },
      handleCloseCanlendar() {
        this.calendarVisible = false;
      },
      showStartTimePicker() {
        this.setData({
          pickerType: 'startTime',
          pickerTitle: '开始时间',
          pickerVisible: true,
          pickerValue: this.data.startTime
        });
      },
      showEndTimePicker() {
        this.setData({
          pickerType: 'endTime',
          pickerTitle: '结束时间',
          pickerVisible: true,
          pickerValue: this.data.endTime
        });
      },
      onConfirm(e: any) {
        const { value } = e.detail;
        const { pickerType } = this.data;
        if (pickerType === 'startTime') {
          this.setData({
            startTime: value
          });
        } else if (pickerType === 'endTime') {
          this.setData({
            endTime: value
          });
        }
      },
      hidePicker() {
        this.pickerVisible = false;
        this.pickerTitle = '';
        this.pickerType = '';
      },
      saveFiles(e: any) {
        this.setData({
          attachmentList: e?.detail?.fileKeys
        });
      },
      handleGoBack() {
        wx.navigateBack({
          delta: 1
        });
      }
    },
    onLoad(query: { vehicleName: string }) {
      this.setData({
        vehicleName: query.vehicleName
      });
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    onShow() {
      this.setData({
        date: dayjs().format('YYYY-MM-DD'),
        startTime: dayjs().format('HH:mm:ss'),
        endTime: dayjs().format('HH:mm:ss')
      });
    }
  });
</script>
<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    flex-flow: column;
  }
  .main-container {
    height: calc(100vh - 200px);
    overflow-y: auto;
    display: flex;
    flex-flow: column;
  }
  .title-box {
    margin: 16px 16px 8px;
    flex: 0 0 38px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    text-align: center;
    line-height: 38px;
  }
  .form-group {
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    margin: 0 16px 8px;
    padding: 12px;
    .form-label {
      color: #808080;
    }
    .t-radio__icon--left {
      margin-right: 4px !important;
    }
    .t-steps-item__title--process {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: bold;
      color: rgba(51, 51, 51, 1);
    }
    .t-icon-check-circle-filled:before {
      color: red;
    }
    .field-item {
      &.inline {
        display: flex;
        align-items: center;
      }
      .t-radio__border {
        display: none;
      }
    }
  }
  .form-options {
    position: relative;
    &.inline {
      .t-radio-group {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }
    }
  }
  .t-radio--block {
    padding: 0 !important;
  }
  .step-ico {
    width: 24px;
    height: 24px;
  }
  .form-value {
    flex: 1;
    text-align: right;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: bold;
    color: rgba(26, 26, 26, 1);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    &::after {
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/workbench/arrow-right.png?Expires=3868521457&AccessKey=n828WHAXD584pTvi&Signature=VHgyCxiYJ7xbPV0JaCcRIF90sDI%3D')
        no-repeat center;
      background-size: contain;
      color: rgba(204, 204, 204, 1);
    }
  }
  .footer {
    height: 150px;
    width: 100%;
    background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    box-sizing: border-box;
    padding-bottom: 84px;

    .btn {
      width: 60%;
      height: 40px;
      border-radius: 40px;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      line-height: 40px;
    }
    .normal {
      width: 28%;
      height: 40px;
      border: 1px solid rgba(204, 204, 204, 1);
      border-radius: 30px;
      background: #fff;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
      margin-right: 16px;
    }
  }

  .t-calendar__dates-item--selected {
    background: linear-gradient(
      135deg,
      rgba(250, 100, 25, 1) 0%,
      rgba(250, 89, 25, 1) 16.59259259%,
      rgba(250, 63, 25, 1) 55.40740741%,
      rgba(250, 44, 25, 1) 100%
    ) !important;
  }
  .t-calendar__footer {
    .t-button--block {
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      ) !important;
      border: none;
      border-radius: 40px;
    }
    .t-button--primary::after {
      border: none;
    }
  }
  .t-picker__confirm {
    color: rgb(242, 0, 0) !important;
  }
  .t-textarea {
    width: 100%;
    height: 104px;
    background-color: #f6f6f6 !important;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(128, 128, 128, 1);
    margin-top: 8px;
    margin-bottom: 8px;
    padding: 4px !important;
  }
  .label {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(128, 128, 128, 1);
  }
  .normal {
    width: 106px;
    height: 40px;
    border: 1px solid rgba(204, 204, 204, 1);
    border-radius: 30px;
    background: #fff;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(26, 26, 26, 1);
    margin-right: 16px;
  }
  .t-upload {
    width: 100% !important;
  }
  .result {
    display: flex;
    flex-flow: column;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative;
    image {
      width: 100%;
      height: 100%;
    }
    .reult-tip {
      font-size: 24px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(26, 26, 26, 1);
      display: flex;
      align-items: center;
      position: absolute;
      top: 500px;

      &::before {
        content: '';
        display: 'block';
        width: 34px;
        height: 34px;
        background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/ok.png?Expires=3870158641&AccessKey=n828WHAXD584pTvi&Signature=ipui7s1kSOm3KiZCUOGJgVumrxY%3D')
          no-repeat center;
        background-size: cover;
      }
    }
    .btn {
      width: 312px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      border-radius: 40px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      position: absolute;
      bottom: 240px;
    }
  }
  .t-steps-item__content--horizontal {
    margin-top: 0 !important;
  }
  .t-calendar__title {
    justify-content: left !important;
    font-size: 16px !important;
    font-family: PingFang SC !important;
    font-weight: bold;
    color: rgba(26, 26, 26, 1) !important;
  }

  .t-calendar__dates-item {
    height: 52px !important;
    font-size: 14px !important;
    font-family: PingFang SC !important;
    font-weight: normal !important;
  }
  .t-calendar__dates-item--selected {
    background: linear-gradient(
      135deg,
      rgba(250, 100, 25, 1) 0%,
      rgba(250, 89, 25, 1) 16.59259259%,
      rgba(250, 63, 25, 1) 55.40740741%,
      rgba(250, 44, 25, 1) 100%
    ) !important;
    border-radius: 8px !important;
    color: #fff !important;
  }
  .t-icon-close {
    font-size: 24px !important;
    font-weight: bold !important;
  }

  .t-picker__title,
  .t-picker__cancel,
  .t-picker__confirm {
    font-size: 16px !important;
    font-family: PingFang SC !important;
    font-weight: normal !important;
    color: rgba(26, 26, 26, 1) !important;
  }
  .t-picker__confirm {
    font-size: 16px !important;
    font-family: PingFang SC !important;
    font-weight: bold !important;
    color: rgba(250, 44, 25, 1) !important;
  }
  .t-picker-item__item {
    font-size: 14px !important;
    font-family: PingFang SC !important;
    font-weight: normal !important;
    color: rgba(26, 26, 26, 0.8) !important;
  }
  .t-picker-item__item--active {
    color: rgba(250, 44, 25, 1) !important;
  }
  .t-textarea__placeholder {
    font-size: 14px !important;
    font-family: PingFang SC !important;
    font-weight: normal !important;
    color: rgba(128, 128, 128, 1) !important;
  }
  .t-textarea__indicator {
    color: #999 !important;
  }

  .t-upload__wrapper {
    .t-icon-close {
      font-size: 14px !important;
      font-weight: normal !important;
    }
  }
  .t-upload__close-btn {
    background: rgba(0, 0, 0, 0.7) !important;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "shared/ui/naviBar.mpx",
      "t-steps": "tdesign-miniprogram/steps/steps",
      "t-step-item": "tdesign-miniprogram/step-item/step-item",
      "t-radio": "tdesign-miniprogram/radio/radio",
      "t-radio-group": "tdesign-miniprogram/radio-group/radio-group",
      "t-divider": "tdesign-miniprogram/divider/divider",
      "t-calendar": "tdesign-miniprogram/calendar/calendar",
      "t-date-time-picker": "tdesign-miniprogram/date-time-picker/date-time-picker",
      "t-textarea": "tdesign-miniprogram/textarea/textarea",
      "upload-image": "shared/ui/uploadImage.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
