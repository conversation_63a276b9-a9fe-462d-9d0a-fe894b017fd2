<template>
  <view class="upload-body">
    <t-upload
      files="{{fileList}}"
      mediaType="{{mediaType}}"
      sizeLimit="{{size}}"
      showProgress="{{true}}"
      max="{{maxNum}}"
      bindadd="handleAdd"
      bindsuccess="onSuccess"
      bindfail="onFail"
      bindcomplete="onComplete"
      bindremove="handleRemove"
    />
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { getLOPdomain, getLOPDN } from 'shared/utils/config';
  import { doRequest, HTTPSTATUSCODE } from '../api/fetch';
  const wxfs = wx.getFileSystemManager();
  createComponent<any>({
    properties: {
      maxNum: {
        type: Number,
        value: 0
      },
      defaultAttachmentList: {
        type: Array,
        value: []
      },
      mediaType: {
        type: Array,
        value: ['video', 'image']
      },
      type: {
        type: String,
        value: ''
      }
    },
    data: {
      fileList: [],
      fileKeys: [],
      updateFlag: 0,
      showLoading: false,
      size: { size: 50, unit: 'MB', message: '图片或视频大小不能超过50MB' }
    },
    watch: {
      defaultAttachmentList: {
        handler(val, old) {
          const fileKeys = val?.map(
            (item: {
              fileKey: string;
              name: string;
              url: string;
              type: string;
            }) => ({
              fileKey: item.fileKey,
              type: item.type
            })
          );
          this.triggerEvent('fileInfo', { fileKeys });
          this.setData({
            fileList: val,
            fileKeys: fileKeys
          });
        },
        immediate: true
      }
    },
    methods: {
      handleAdd(e: any) {
        console.log(e.detail.files, 1111);
        const videoList = e.detail.files?.filter(
          (file: any) => file.type === 'video'
        );
        const hasVideo = this.fileList?.find(
          (file: any) => file.type === 'video'
        );
        if (this.type !== 'bumpform') {
          if (videoList?.length > 1 || (hasVideo && videoList?.length >= 1)) {
            wx.showModal({
              content: '最多上传一个视频'
            });
            return;
          }
        }

        this.fileList = this.fileList.concat(e.detail.files);
        const tokenData = wx.getStorageSync('JD_AUTH_TOKEN');
        const plugin = requirePlugin('loginPlugin');
        const pt_key = plugin.getPtKey();
        e.detail.files.forEach((v: any) => {
          doRequest({
            url: '/infrastructure/oss/getPreUrl',
            method: 'POST',
            data: {
              fileKey: v.name,
              bucketName: 'rover-operation'
            },
            headers: {
              'LOP-DN': getLOPDN(),
              authType: 2,
              Cookie: `pt_key=${pt_key}`,
              client: 'm',
              appid: 2435,
              ticket_type: 'mix'
            }
          }).then((res: any) => {
            if (res.code === HTTPSTATUSCODE.Success) {
              const data = res.data;
              const newFileKeys = [...this.fileKeys];
              newFileKeys?.push({
                fileKey: data?.fileKey,
                type: v.type
              });
              this.setData({
                fileKeys: newFileKeys
              });
              this.triggerEvent('fileInfo', { fileKeys: newFileKeys });
              wxfs.readFile({
                filePath: v.url,
                success: (res: any) => {
                  wx.request({
                    url: data?.uploadUrl,
                    method: 'PUT',
                    data: res.data,
                    header: {
                      'Content-Type': 'multipart/form-data'
                    },
                    success: (res: any) => {
                      console.log('上传成功');
                    },
                    fail: err => console.error(err)
                  });
                },
                fail: err => console.error(err)
              });
            } else {
              console.log('上传失败');
            }
          });
        });
      },
      onFail(event: any) {},
      onComplete(event: any) {},
      forceUpdate() {
        this.setData({
          updateFlag: Math.random() // 通过更新一个随机数来强制触发视图更新
        });
      },
      handleRemove(e: any) {
        const { index } = e.detail;
        const { fileList, fileKeys } = this.data;
        fileList.splice(index, 1);
        fileKeys.splice(index, 1);
        this.setData({
          fileList: [...fileList],
          fileKeys
        });
        this.triggerEvent('fileInfo', { fileKeys });
      }
    }
  });
</script>

<style lang="scss">
  .upload-body {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    margin-top: 16rpx;
    position: relative;
    .loading-icon {
      width: 48px;
      height: 48px;
      position: absolute;
      z-index: 999;
      top: 50%;
      left: 50%;
    }
    .image-preview {
      position: relative;
      margin-right: 16rpx;
      margin-bottom: 16rpx;

      .preview-image {
        width: 70px;
        height: 70px;
        border-radius: 8rpx;
      }

      .delete-icon {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        width: 30rpx;
        height: 30rpx;
        background: #fff;
        border-radius: 50%;
        text-align: center;
        line-height: 30rpx;
        box-shadow: 0 0 5rpx rgba(0, 0, 0, 0.2);
      }
    }

    .upload-button {
      width: 150rpx;
      height: 150rpx;
      border: 2rpx dashed #ccc;
      border-radius: 8rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: #999;

      td-icon {
        margin-bottom: 8rpx;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-upload": "tdesign-miniprogram/upload/upload"
    }
  }
</script>
