
<template>
  <view
    class="free-driving-btn"
    wx:class="{{styleInfo.otherClassName}}"
    bindtap="handleClick"
  >
    <t-image
      src="{{styleInfo.imageSrc}}"
      mode="aspectFill"
      width="{{styleInfo.imageWidth}}"
      height="{{styleInfo.imageHeight}}"
    />
    <text class="label" wx:class="{{styleInfo.otherClassName}}">自由跑行</text>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, SingleVehicle } from 'shared/assets/imageUrl';
  import { CheckType, CheckVehicle } from 'shared/utils/checkVehicle';

  createComponent({
    properties: {
      // 'workBench'  ||  'singelVehicle'
      sourceType: {
        type: String,
        value: ''
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      WorkBenchUrl: WorkBenchUrl,
      SingleVehicle: SingleVehicle,
      styleInfo: {}
    },
    methods: {
      formatStyleInfo() {
        if (this.sourceType === 'workBench') {
          this.styleInfo = {
            imageSrc: this.WorkBenchUrl.FreeDriving,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'workBench'
          };
        } else {
          this.styleInfo = {
            imageSrc: this.SingleVehicle.FreeDrivingBtn,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'singelVehicle'
          };
        }
      },
      handleClick() {
        if (this.sourceType === 'workBench') {
          wx.navigateTo({
            url: '/pages/workbench/freeDrive?backUrl=/pages/workbench/index'
          });
        } else if (this.sourceType === 'singleVehicle') {
          const check = new CheckVehicle(
            [{ checkType: CheckType.TaskRepairOrder }],
            this.vehicleName
          );
          check.checkVehicleFunc().then(res => {
            if (res) {
              wx.navigateTo({
                url: `/pages/workbench/freeDrive?vehicleName=${this.vehicleName}&backUrl=/pages/vehicle/index`
              });
            }
          });
        }
      }
    },
    lifetimes: {
      created: function () {
        this.formatStyleInfo();
      }
    }
  });
</script>

<style lang="scss">
  .free-driving-btn {
    display: flex;
    flex-direction: column;
    align-items: center;

    .label {
      text-align: center;
      font-family: PingFang SC;
      font-weight: normal;
      &.workBench {
        font-size: 12px;
        bottom: 10px;
        color: rgba(51, 51, 51, 1);
        margin-top: 6px;
      }
      &.singelVehicle {
        color: rgba(51, 51, 51, 1);
        font-size: 11px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image"
    }
  }
</script>

