
<template>
  <view
    class="report-hardware-btn"
    wx:class="{{styleInfo.otherClassName}}"
    bindtap="handleClick"
  >
    <t-image
      src="{{styleInfo.imageSrc}}"
      mode="aspectFill"
      width="{{styleInfo.imageWidth}}"
      height="{{styleInfo.imageHeight}}"
    />
    <text class="label" wx:class="{{styleInfo.otherClassName}}">{{
      styleInfo.text
    }}</text>
    <t-image
      wx:if="{{sourceType === 'workBench'}}"
      src="{{WorkBenchUrl.ArrowRight}}"
      mode="aspectFill"
      width="16px"
      height="16px"
    />
  </view>
</template>

<script  lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, SingleVehicle } from 'shared/assets/imageUrl';
  import { CheckVehicle, CheckType } from 'shared/utils/checkVehicle';

  createComponent({
    properties: {
      // 'workBench'  ||  'singelVehicle'
      sourceType: {
        type: String,
        value: ''
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      WorkBenchUrl: WorkBenchUrl,
      SingleVehicle: SingleVehicle,
      styleInfo: {}
    },
    methods: {
      formatStyleInfo() {
        if (this.sourceType === 'workBench') {
          this.styleInfo = {
            imageSrc: this.WorkBenchUrl.ReportHardware,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'workBench',
            text: '硬件报修'
          };
        } else {
          this.styleInfo = {
            imageSrc: this.SingleVehicle.HardwareReport,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'singelVehicle',
            text: '硬件报修'
          };
        }
      },
      handleClick() {
        if (this.sourceType === 'workBench') {
          wx.navigateTo({
            url: `/pages/selectVehiclePages/reportHardwarePage?vehicleName=${this.vehicleName}`
          });
        } else if (this.sourceType === 'singleVehicle') {
          const check = new CheckVehicle(
            [{ checkType: CheckType.VehiclePeriod }],
            this.vehicleName
          );
          check.checkVehicleFunc().then(res => {
            if (res) {
              getApp().globalData.controlVehicleName = this.vehicleName;
              getApp().globalData.redirectUrl = `/pages/notification/hardwareReport/index?vehicleName=${this.vehicleName}`;
              getApp().globalData.backUrl = '/pages/vehicle/index';
              getApp().globalData.eventType = 'redirect';
              wx.navigateTo({
                url: `/pages/redirectPage/index`
              });
            }
          });
        }
      }
    },
    lifetimes: {
      created: function () {
        this.formatStyleInfo();
      }
    }
  });
</script>

<style lang="scss">
  .report-hardware-btn {
    &.workBench {
      height: 44px;
      display: flex;
      align-items: center;
    }
    &.singelVehicle {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .label {
      &.workBench {
        width: calc(100vw - 110px);
        margin-left: 6px;
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(51, 51, 51, 1);
      }
      &.singelVehicle {
        text-align: center;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(51, 51, 51, 1);
        font-size: 11px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image",
      "call-front-operate": "widgets/commandOperate/callFrontOperate.mpx"
    }
  }
</script>
