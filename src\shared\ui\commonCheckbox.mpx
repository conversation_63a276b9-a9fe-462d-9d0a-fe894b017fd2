<template>
  <view
    class="checkbox checkbox-checked"
    wx:if="{{checked}}"
    catchtap="onChange"
    style="
      background: no-repeat center/100%
        url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/vehicle/checkbox-selected.png?Expires=3869547149&AccessKey=n828WHAXD584pTvi&Signature=sFpAGyXW%2B%2BoT0x%2FlHfeEKaeomCs%3D');
    "
  ></view>
  <view
    class="checkbox checkbox-unchecked"
    style="
      background: no-repeat center/100%
        url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/vehicle/checkbox-unselected.png?Expires=3869547207&AccessKey=n828WHAXD584pTvi&Signature=Z86fpWaq8x7zXwZU4ApDQY%2BFpaQ%3D');
    "
    wx:else
    catchtap="onChange"
  ></view>
</template>
<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { VehicleUrl } from 'shared/assets/imageUrl';

  createComponent({
    properties: {
      checked: {
        type: Boolean,
        value: false
      }
    },
    methods: {
      onChange() {
        this.triggerEvent('onchange');
      }
    }
  });
</script>

<style lang="scss">
  .checkbox {
    width: 18px;
    height: 18px;
  }
</style>
<script name='json'>
  module.exports = {
    component: true,
    usingComponents: {}
  };
</script>
