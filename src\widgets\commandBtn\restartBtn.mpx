<template>
  <view
    class="restart-btn"
    wx:class="{{styleInfo.otherClassName}}"
    bindtap="handleClick"
  >
    <t-image
      src="{{styleInfo.imageSrc}}"
      mode="aspectFill"
      width="{{styleInfo.imageWidth}}"
      height="{{styleInfo.imageHeight}}"
    />
    <text class="label" wx:class="{{styleInfo.otherClassName}}">开关重启</text>
  </view>

  <restart-operate
    bind:onVisibleChange="onVisibleChange"
    vehicleName="{{vehicleName}}"
    visible="{{visible}}"
  />
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import {
    WorkBenchUrl,
    SingleVehicle,
    RemoteControlUrl
  } from 'shared/assets/imageUrl';

  createComponent({
    properties: {
      // 'workBench'  ||  'singleVehicle'
      sourceType: {
        type: String,
        value: ''
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      WorkBenchUrl: WorkBenchUrl,
      SingleVehicle: SingleVehicle,
      RemoteControlUrl: RemoteControlUrl,
      styleInfo: {},
      visible: false
    },
    methods: {
      formatStyleInfo() {
        if (this.sourceType === 'workBench') {
          this.styleInfo = {
            imageSrc: this.WorkBenchUrl.Restart,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'workBench'
          };
        } else if (this.sourceType === 'singleVehicle') {
          this.styleInfo = {
            imageSrc: this.SingleVehicle.PowerControl,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'singleVehicle'
          };
        }
      },
      handleClick() {
        if (this.sourceType === 'workBench') {
          wx.navigateTo({ url: '/pages/selectVehiclePages/restartPage' });
        } else if (this.sourceType === 'singleVehicle') {
          this.setData({
            visible: true
          });
        }
      },
      onVisibleChange() {
        this.setData({
          visible: false
        });
      }
    },
    lifetimes: {
      created: function () {
        this.formatStyleInfo();
      }
    }
  });
</script>

<style lang="scss">
  .restart-btn {
    display: flex;
    flex-direction: column;
    align-items: center;

    .label {
      text-align: center;
      font-family: PingFang SC;
      font-weight: normal;
      &.workBench {
        font-size: 12px;
        bottom: 10px;
        color: rgba(51, 51, 51, 1);
        margin-top: 6px;
      }
      &.singleVehicle {
        color: rgba(51, 51, 51, 1);
        font-size: 11px;
      }
    }
  }
  .popup-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 16px;
    padding-right: 16px;
    height: 53px;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(26, 26, 26, 1);
    border-bottom: 1px solid rgba(236, 236, 236, 1);
    image {
      width: 14px;
      height: 14px;
    }
  }
  .popup-content {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 33px;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image",
      "t-popup": "tdesign-miniprogram/popup/popup",
      "restart-operate": "widgets/commandOperate/restartOperate.mpx"
    }
  }
</script>


