const { defineConfig } = require('@vue/cli-service');
const TerserPlugin = require('terser-webpack-plugin');
const path = require('path');
const webpack = require('webpack');
module.exports = defineConfig({
  outputDir: `dist/${process.env.MPX_CURRENT_TARGET_MODE}`,
  transpileDependencies: ['tdesign-miniprogram'],
  pluginOptions: {
    mpx: {
      plugin: {
        srcMode: 'wx',
        resolveMode: 'webpack',
        miniNpmPackage: ['tdesign-miniprogram']
      }
    }
  },
  /**
   * 如果希望node_modules下的文件时对应的缓存可以失效，
   * 可以将configureWebpack.snap.managedPaths修改为 []
   */
  configureWebpack: {
    resolve: {
      alias: {
        shared: path.resolve(__dirname, 'src/shared/'),
        app: path.resolve(__dirname, 'src/app/'),
        widgets: path.resolve(__dirname, 'src/widgets/')
      }
    },
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          // terserOptions参考 https://github.com/webpack-contrib/terser-webpack-plugin#terseroptions
          terserOptions: {
            compress: {
              // terser的默认行为会把某些对象方法转为箭头函数，导致ios9等不支持箭头函数的环境白屏，详情见 https://github.com/terser/terser#compress-options
              arrows: false,
              // terser默认会删除非标准directive，为了保障skyline worklet的正常工作，需关闭该配置
              directives: false
            }
          }
        })
      ]
    },
    plugins: [
      new webpack.DefinePlugin({
        __mpx_env__: JSON.stringify(process.env.MPX_ENV || 'development')
      })
    ]
  }
});
