
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="登录车端"
  ></navi-bar>

  <vehicle-list
    selectedVehicleName="{{selectedVehicleName}}"
    checkContent="{{ checkContent }}"
    bind:handleSelect="handleSelect"
  />

  <view class="login-vehicle"
    ><view class="btn" bindtap="handleLogin">登录车端</view></view
  >
</template>

<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  import { CheckType, CheckVehicle } from 'shared/utils/checkVehicle';
  import { CommonApi } from 'shared/api/common';
  import { CallFrontApi } from 'shared/api/callFront';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createPage<any>({
    data: {
      fetchApi: new CommonApi(),
      callFrontFetchApi: new CallFrontApi(),
      CheckType: CheckType,
      selectedVehicleName: null,
      checkContent: [{ checkType: CheckType.VehicleStatus }]
    },
    methods: {
      handleSelect(e: any) {
        this.selectedVehicleName = e.detail.val;
      },
      async handleLogin() {
        if (!this.selectedVehicleName) {
          return;
        }
        const check = new CheckVehicle(
          [{ checkType: CheckType.Distance }],
          this.selectedVehicleName
        );
        const checkRes = await check.checkVehicleFunc();
        if (!checkRes) {
          return;
        }
        const userName = wx.getStorageSync('userName');
        const phoneRes = await this.callFrontFetchApi.getUserPhone(userName);
        if (phoneRes.code === HTTPSTATUSCODE.Success) {
          this.fetchApi
            .loginVehicle(this.selectedVehicleName, phoneRes.data.phone)
            .then((res: any) => {
              wx.showToast({
                icon: 'none',
                title:
                  res.code === HTTPSTATUSCODE.Success ? '登录成功' : res.message
              });
            });
        }
      }
    },
    onLoad: function (options) {
      this.selectedVehicleName = options.vehicleName;
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
  }
  .login-vehicle {
    width: calc(100vw - 36px);
    height: 90px;
    background: rgba(255, 255, 255, 1);
    padding: 8px 18px 9px 18px;
    position: fixed;
    bottom: 0px;
    .btn {
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      border-radius: 20px;
    }
  }
  scroll-view {
    height: calc(100vh - 255px);
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
