module.exports = {
  root: true,
  extends: ['@mpxjs/eslint-config-ts'],
  rules: {
    // .mpx文件规则 https://mpx-ecology.github.io/eslint-plugin-mpx/rules/
  },
  overrides: [
    {
      files: ['**/*.ts'],
      rules: {
        // .ts文件规则 https://eslint.bootcss.com/docs/rules/
        semi: 'off',
        '@typescript-eslint/no-explicit-any': 'off'
      }
    },
    {
      files: ['**/*.js'],
      rules: {
        // .js文件规则 https://eslint.bootcss.com/docs/rules/
        semi: 'off'
      }
    },
    {
      files: ['**/*.mpx'],
      rules: {
        // .js文件规则 https://eslint.bootcss.com/docs/rules/
        semi: 'off',
        indent: 'off',
        'object-shorthand': 'off',
        'no-unused-vars': 'off'
      }
    }
  ]
};
