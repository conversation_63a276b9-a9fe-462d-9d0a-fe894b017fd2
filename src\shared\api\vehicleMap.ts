import { doRequest } from 'shared/api/fetch';

class VehicleMapApi {
  /**
   * 获取用户权限下的车辆列表
   */
  public async getVehicleList() {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/map/getVehicleList',
      method: 'POST'
    };
    return doRequest(requestOptions);
  }

  /**
   * 获取地图上的车辆（轮询）
   */
  public async getMapVehicleList(longitude: any, latitude: any) {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/map/getMapVehicle',
      method: 'POST',
      data: {
        longitude,
        latitude
      }
    };
    return doRequest(requestOptions);
  }

  /**
   * 获取规划路径
   */
  public async getVehiclePath(vehicleName: string) {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/map/getVehiclePath',
      method: 'POST',
      data: { vehicleName }
    };
    return doRequest(requestOptions);
  }
}

export default VehicleMapApi;
