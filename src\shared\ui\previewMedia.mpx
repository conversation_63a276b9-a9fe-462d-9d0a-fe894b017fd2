<template>
  <view class="container">
    <view
      class="media-item"
      wx:for="{{list}}"
      wx:key="index"
      style="width:{{computedImageSize.width + 'rpx'}};height:{{computedImageSize.height + 'rpx'}}"
    >
      <image
        wx:if="{{item.type === 'image'}}"
        src="{{item.thumb || item.tempUrl || item.url}}"
        class="media-image"
        bindtap="previewImage"
        data-url="{{item.thumb || item.tempUrl || item.url}}"
      />
      <view
        wx:if="{{item.type === 'video'}}"
        class="media-video"
        bindtap="previewVideo"
        data-url="{{item.tempUrl || item.url}}"
      >
        <video src="{{item.tempUrl || item.url}}" class="video-thumbnail" />
      </view>

      <!-- 上传中遮罩层 -->
      <view
        wx:if="{{showUploadMask && item.status && item.status !== 'done'}}"
        class="upload-mask"
        data-index="{{index}}"
        data-item="{{item}}"
      >
        <!-- 上传中状态 -->
        <block wx:if="{{item.status === 'loading'}}">
          <view class="progress-container">
            <!-- 圆形旋转动画 -->
            <view class="loading-spinner"></view>
            <!-- 进度条 -->
            <view class="progress-bar">
              <!-- <view
                class="progress-inner"
                style="width: {{item.percent || 0}}%"
              ></view> -->
              <view class="progress-text"> {{ item.percent || 0 }}%</view>
            </view>
          </view>
        </block>

        <!-- 失败状态 -->
        <block wx:if="{{item.status === 'failed'}}">
          <view class="error-container">
            <view class="error-icon">!</view>
            <view class="error-text">上传失败</view>
          </view>
        </block>
      </view>

      <!-- 删除按钮 -->
      <view
        wx:if="{{showDelete}}"
        class="delete-btn"
        data-item="{{item}}"
        bindtap="handleDelete"
      >
        <text class="delete-icon">×</text>
      </view>
    </view>
  </view>
</template>

<script>
  import { createComponent } from '@mpxjs/core';

  createComponent({
    properties: {
      mediaList: {
        type: Array,
        value: []
      },
      showDelete: {
        type: Boolean,
        value: false
      },
      showUploadMask: {
        type: Boolean,
        value: false
      },
      perRow: {
        type: Number,
        value: 4
      },
      totalWidth: {
        type: Number,
        value: 664
      }
    },
    computed: {
      list() {
        return this.mediaList.map(item => ({
          ...item,
          url: item.url?.replace('http://', 'https://')
        }));
      },
      computedImageSize() {
        const perRow = this.perRow || 4;
        const gap = 16;
        const availableWidth = this.totalWidth - gap - gap * (perRow - 1);
        const itemWidth = Math.floor(availableWidth / perRow);
        return {
          width: itemWidth,
          height: 180
        };
      }
    },
    methods: {
      previewImage(event) {
        const { url } = event.currentTarget.dataset;
        const imageUrls = this.list
          .filter(item => item.type === 'image')
          .map(item => item.thumb || item.tempUrl || item.url);
        wx.previewImage({
          urls: imageUrls,
          current: url,
          fail: () => {
            wx.showToast({
              title: '预览失败',
              icon: 'error'
            });
          }
        });
      },

      previewVideo(event) {
        const { url } = event.currentTarget.dataset;
        wx.previewMedia({
          sources: [
            {
              url: url,
              type: 'video'
            }
          ],
          success: function (res) {
            console.log('成功预览视频', res);
          },
          fail: function (err) {
            console.error('预览视频失败', err);
            wx.showToast({
              title: '预览失败',
              icon: 'error'
            });
          }
        });
      },
      handleDelete(event) {
        const { item } = event.currentTarget.dataset;
        this.triggerEvent('delete', { file: item });
      }
    }
  });
</script>

<style lang="scss">
  .container {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: -16rpx;
    margin-right: -16rpx;
  }

  .media-item {
    box-sizing: border-box;
    position: relative;
    border-radius: 16rpx;
    overflow: hidden;
    margin-right: 16rpx;
    margin-bottom: 16rpx;

    .media-video {
      width: 100%;
      height: 100%;
    }

    .media-image,
    .video-thumbnail {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
      display: block;
    }

    // 上传遮罩层
    .upload-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      font-size: 24rpx;
      z-index: 2;

      .progress-container {
        width: 80%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      /* 圆形旋转动画 */
      .loading-spinner {
        width: 40rpx;
        height: 40rpx;
        border: 2rpx solid rgba(255, 255, 255, 0.3);
        border-top-color: white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16rpx;
      }

      .progress-bar {
        height: 50rpx;
        // background: rgba(255, 255, 255, 0.3);
        // border-radius: 6rpx;
        overflow: hidden;
      }

      .progress-inner {
        height: 100%;
        background: #07c160;
        border-radius: 6rpx;
        transition: width 0.3s;
      }

      .error-container {
        text-align: center;
      }

      .error-icon {
        width: 32rpx;
        height: 32rpx;
        line-height: 32rpx;
        border-radius: 50%;
        background: #ff4d4f;
        color: white;
        font-size: 32rpx;
        font-weight: bold;
        margin: 0 auto 16rpx;
      }

      .error-text {
        font-size: 28rpx;
        color: white;
      }
    }

    // 删除按钮
    .delete-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: 44rpx;
      height: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.6);
      border-radius: 0 0 0 16rpx;
      z-index: 10;

      .delete-icon {
        font-size: 36rpx;
        color: white;
        font-weight: bold;
        line-height: 1;
        margin-top: -4rpx;
      }
    }
  }

  /* 旋转动画关键帧 */
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true
  };
</script>