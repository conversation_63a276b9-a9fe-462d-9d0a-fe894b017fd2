<template>
  <t-dialog
    visible="{{showDialog}}"
    confirm-btn="提交"
    cancel-btn="取消"
    bind:confirm="addShadowEvent"
    bind:cancel="closeDialog"
  >
    <view slot="content" class="form">
      <view class="form-item-horizontal">
        <view class="label">上报主题<span class="required">*</span></view>
        <t-radio-group
          borderless
          t-class="box"
          bind:change="onChangeTopic"
          value="{{topic}}"
        >
          <t-radio block="{{false}}" label="场景收集" value="scene" />
          <t-radio block="{{false}}" label="问题缺陷" value="jira" />
        </t-radio-group>
      </view>
      <view class="form-item-horizontal-large">
        <view class="label">上报类型</view>
        <t-radio-group
          borderless
          t-class="box"
          bind:change="onChangeType"
          value="{{type}}"
        >
          <t-radio
            block="{{false}}"
            wx:for="{{bugLabelList}}"
            wx:key="index"
            label="{{item}}"
            value="{{item}}"
          />
        </t-radio-group>
      </view>
      <view class="form-item-horizontal">
        <view class="label">上报时间</view>
        <view class="report-time">{{ reportTime }}</view>
      </view>
      <view class="form-item-vertical">
        <view class="label">备注</view>
        <t-textarea
          bind:enter="onChangeRemark"
          bind:blur="onChangeRemark"
          t-class="textarea-class"
          placeholder="请输入"
          maxlength="80"
          bordered
          disableDefaultPadding="{{true}}"
          indicator
          value="{{remark}}"
        />
      </view>
    </view>
  </t-dialog>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import CarTrackApi from 'shared/api/carTrack';
  import { HTTPSTATUSCODE } from '../../shared/api/fetch';
  import { WorkBenchUrl } from 'shared/assets/imageUrl';
  import * as dayjs from 'dayjs';
  import { formatTraceId } from 'shared/utils/utils';
  import useLoginStore from 'shared/store/useLoginStore';
  import { mapStores } from '@mpxjs/pinia';
  createComponent({
    properties: {
      showDialog: {
        type: Boolean,
        value: false
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      fetchApi: new CarTrackApi(),
      bugLabelList: [],
      reportTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      topic: '',
      type: '',
      remark: '',
      WorkBenchUrl
    },
    lifetimes: {
      attached() {
        this.getBugLabel();
      }
    },
    watch: {
      showDialog(val) {
        if (val) {
          this.setData({
            reportTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
          });
        }
      }
    },
    computed: {
      ...mapStores(useLoginStore)
    },
    methods: {
      onChangeTopic(e: any) {
        this.setData({
          topic: e.detail.value
        });
      },
      onChangeType(e: any) {
        this.setData({
          type: e.detail.value
        });
      },
      onChangeRemark(e: any) {
        this.setData({
          remark: e.detail.value
        });
      },
      closeDialog(e: any) {
        this.triggerEvent('closedialog');
        this.setData({
          topic: '',
          type: '',
          remark: '',
          reportTime: ''
        });
      },
      async getBugLabel() {
        try {
          const res: any = await this.fetchApi.getBugLabel();
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              bugLabelList: res.data
            });
          } else {
            wx.showToast({
              title: res.message,
              icon: 'none'
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      async reportJira(reportData: any, title: string) {
        try {
          const userName = wx.getStorageSync('userName');
          const res: any = await this.fetchApi.reportJira({
            id: reportData.id,
            topic: title,
            description: reportData.content + '[备注]：' + this.remark,
            reportUser: userName
          });
          if (res.code === HTTPSTATUSCODE.Success) {
            wx.hideLoading();
            wx.showToast({
              title: '提报成功',
              duration: 3000,
              icon: 'success'
            });
            this.triggerEvent('closedialog');
          } else {
            wx.hideLoading();
            wx.showToast({
              title: '提报失败',
              icon: 'none',
              duration: 3000
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      async addShadowEvent() {
        try {
          if (!this.topic) {
            wx.showToast({
              title: '请选择上报主题！',
              icon: 'none'
            });
            return;
          }
          wx.showLoading({
            title: '加载中'
          });
          const userName = wx.getStorageSync('userName');
          const shadowInfo = {
            vehicleName: this.vehicleName,
            reportTime: this.reportTime,
            eventNo: 'MINI_MONITOR1929070176',
            traceId: formatTraceId(),
            eventBody: JSON.stringify({
              vehicleName: this.vehicleName,
              timeStamp: this.reportTime,
              userName: userName,
              message: this.type
            })
          };
          const res: any = await this.fetchApi.addShadow(shadowInfo);
          if (res.code === HTTPSTATUSCODE.Success) {
            this.reportJira(
              res.data,
              this.topic === 'scene' ? '场景收集' : '问题缺陷'
            );
            const shadowEvent = {
              id: res.data.id,
              reportTime: dayjs().format('HH:mm:ss'),
              content: `${this.topic === 'scene' ? '场景收集' : '问题缺陷'}${
                this.type ? '；' + this.type : ''
              }`,
              remark: this.remark
            };
            this.triggerEvent('recordevent', {
              eventInfo: shadowEvent
            });
            this.setData({
              topic: '',
              type: '',
              remark: '',
              reportTime: ''
            });
          }
        } catch (err) {
          wx.showToast({
            title: err,
            icon: 'none'
          });
          wx.hideLoading();
          console.error(err);
          this.triggerEvent('closedialog');
        }
      }
    }
  });
</script>

<style lang="scss">
  .form {
    display: flex;
    flex-direction: column;
    .form-item-horizontal {
      display: flex;
      align-items: center;
      font-size: 14px;
      height: 52px;
      border-bottom: 1px solid rgba(245, 245, 245, 1);
      .label {
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(128, 128, 128, 1);
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin-right: 10px;
        position: relative;
        .required {
          color: red;
          position: absolute;
          right: -9px;
        }
      }
      .box {
        font-size: 14px !important;
      }
      .report-time {
        height: 20px;
        line-height: 20px;
        text-align: center;
        color: rgba(26, 26, 26, 1);
      }
    }
    .form-item-horizontal-large {
      display: flex;
      height: 170px;
      font-size: 14px;
      border-bottom: 1px solid rgba(245, 245, 245, 1);
      .box {
        font-size: 14px !important;
        display: flex !important;
        flex-wrap: wrap;
        flex: 1;
      }
      .label {
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(128, 128, 128, 1);
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin-right: 10px;
        position: relative;
      }
    }
    .form-item-vertical {
      margin-top: 5px;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      .label {
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(128, 128, 128, 1);
      }
      .textarea-class {
        margin-top: 6px;
        flex: 1 !important;
        height: 170px !important;
        background: rgba(245, 245, 246, 1) !important;
        border-radius: 8px !important;
        padding: 10px !important;
      }
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-dialog': 'tdesign-miniprogram/dialog/dialog',
      't-textarea': 'tdesign-miniprogram/textarea/textarea',
      't-radio': 'tdesign-miniprogram/radio/radio',
      't-radio-group': 'tdesign-miniprogram/radio-group/radio-group'
    }
  };
</script>
