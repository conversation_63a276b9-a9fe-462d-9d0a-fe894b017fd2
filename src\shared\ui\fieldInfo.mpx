<template>
  <view class="info-item">
    <text class="label">{{ labelName }}</text>
    <text class="value">{{ value }}</text>
    <view
      class="status-tag"
      wx:if="{{status}}"
      wx:class="{{{resolved: status === 'COMPLETE'}}}"
      >{{ statusText }}</view
    >
  </view>
</template>
<script>
  import { createComponent } from '@mpxjs/core';
  import { Notification } from 'shared/assets/imageUrl';
  createComponent({
    data: {
      Notification: Notification
    },
    properties: {
      labelName: {
        type: String,
        value: ''
      },
      value: {
        type: String,
        value: ''
      },
      status: {
        type: String,
        value: ''
      },
      statusText: {
        type: String,
        value: ''
      }
    }
  });
</script>
<style lang="scss">
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .label {
      flex: 0 0 80px;
      color: #999;
      font-size: 14px;
    }

    .value {
      color: #333;
      font-size: 14px;
      margin-right: 8px;
    }

    .status-tag {
      padding: 4px 6px;
      font-size: 12px;
      text-align: center;
      line-height: 20px;
      color: #ffaa00;
      word-break: keep-all;
      background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/tag-bg.png?Expires=3873522051&AccessKey=n828WHAXD584pTvi&Signature=k%2BM%2B%2Fdk%2BghuNlTv7LnR77t9tpiA%3D') no-repeat center;
      background-size: 100% 100%;
      padding: 0 12px;
      &.resolved {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: normal;
        color: #12b35d;
        background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/normal-tag.png?Expires=3873521932&AccessKey=n828WHAXD584pTvi&Signature=fHis37lgycUsifTuKxTaEyV4ULg%3D') no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
</style>
<script name="json">
  module.exports = {
    component: true
  };
</script>