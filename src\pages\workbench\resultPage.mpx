<template>
  <view
    class="result-page"
    style="background: url({{FreeDriveUrl.ResultPageBg}}) no-repeat center/100%"
  >
  </view>
    <view class="title">
      <t-image
        src="{{FreeDriveUrl.TimeIcon}}"
        mode="aspectFill"
        width="34px"
        height="34px"
        bindtap="handleDel"
      />
      生成路线中...
    </view>
  </view>
  <view class="text">生成需要时间，可在地图中查看</view>
  <view class="btns">
    <view class="btn" bindtap="continue">继续设置跑行</view>
    <view class="btn" bindtap="goToMap">去车辆地图</view>
  </view>
  <view class="fail-vehicle" wx:if="{{failVehicle.length>0}}">
    <view
      style="display: flex; justify-content: space-between; align-items: center"
    >
      <view class="tip"> 部分下发指令失败 </view>
      <view class="again-btn" bindtap="again"> 再次发车 </view>
    </view>
    <scroll-view class="vehicle-container" scroll-y type="list">
      <view class="row-conatiner">
        <view class="vehicle" wx:for="{{failVehicle}}" wx:key="item">{{
          item
        }}</view>
      </view>
    </scroll-view>
  </view>

  <t-dialog
    visible="{{showDialog}}"
    title="是否确定去往此地"
    confirm-btn="确定出发"
    cancel-btn="取消"
    bind:confirm="confirmDialog"
    bind:cancel="closeDialog"
  >
    <view slot="content" class="dialog-content">
      <view>去往停车点：{{ stopName }}</view>
      <view class="vehicle-num"
        >已选车辆：<text style="color: rgba(250, 44, 25, 1)">{{
          failVehicle.length
        }}</text
        >辆</view
      >
      <scroll-view class="vehicle-container" scroll-y type="list">
        <view class="row-conatiner">
          <view class="vehicle" wx:for="{{failVehicle}}" wx:key="item">{{
            item
          }}</view>
        </view>
      </scroll-view>
    </view>
  </t-dialog>
</template>

<script>
  import { FreeDriveApi } from 'shared/api/freeDrive';
  import { createPage } from '@mpxjs/core';
  import { WorkBenchUrl, FreeDriveUrl, VehicleUrl } from 'shared/assets/imageUrl';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createPage({
    data: {
      FreeDriveUrl,
      fetchApi: new FreeDriveApi(),
      failVehicle: [],
      showDialog: false,
      selectedStopInfo: {},
      selectedVehicle: {},
      eventChannel: null,
      backUrl: null,
      openType: 'stopPoint',
      anyDriveInfo: null,
      stopName: ''
    },
    methods: {
      rad(d) {
        return (d * Math.PI) / 180.0;
      },
      async dispatchDpature(vehicleList, stopInfo) {
        console.log(stopInfo);
        const res = await this.fetchApi.dispatchDpature(
          stopInfo.stopId,
          stopInfo.stopName,
          vehicleList
        );
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            failVehicle: res.data.vehicleNameList,
            selectedStopInfo: {
              stopId: res.data.stopId,
              stopName: res.data.stopName
            },
            stopName: res.data.stopName
          });
        } else {
          wx.showToast({ icon: 'none', title: res.message });
        }
      },
      async dispatchAnyDrive(vehicleList, anyDriveInfo) {
        console.log(anyDriveInfo);
        const radHead = this.rad(anyDriveInfo.head);
        const res = await this.fetchApi.dispatchAnyDrive({
          longitude: anyDriveInfo.lon,
          latitude: anyDriveInfo.lat,
          heading: radHead,
          stopName: anyDriveInfo.stopName,
          vehicleNameList: vehicleList
        });
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            failVehicle: res.data.vehicleNameList,
            anyDriveInfo: {
              head: res.data.heading,
              lon: res.data.longitude,
              lat: res.data.latitude,
              stopName: res.data.stopName
            },
            stopName: res.data.stopName
          });
        } else {
          wx.showToast({ icon: 'none', title: res.message });
        }
      },
      continue() {
        wx.reLaunch({
          url: `/pages/workbench/freeDrive?backUrl=${this.backUrl}`
        });
      },
      goToMap() {
        wx.switchTab({
          url: '/pages/vehicle/index'
        });
      },
      again() {
        this.setData({
          showDialog: true
        });
      },
      closeDialog() {
        this.setData({
          showDialog: false
        });
      },
      confirmDialog() {
        if (this.openType === 'stopPoint') {
          this.dispatchDpature(this.failVehicle, this.selectedStopInfo);
        } else {
          this.dispatchAnyDrive(this.failVehicle, this.anyDriveInfo);
        }
        this.setData({
          showDialog: false
        });
      }
    },
    onLoad: function () {
      this.eventChannel = this.getOpenerEventChannel();
      this.eventChannel.on('resultPageChannel', data => {
        if (data.openType === 'stopPoint') {
          this.setData({
            selectedVehicle: data.selectedVehicle,
            selectedStopInfo: data.selectedStopInfo,
            openType: data.openType,
            backUrl: data.backUrl
          });
          this.dispatchDpature(data.selectedVehicle, data.selectedStopInfo);
        } else {
          this.setData({
            selectedVehicle: data.selectedVehicle,
            anyDriveInfo: data.anyDriveInfo,
            openType: data.openType,
            backUrl: data.backUrl
          });
          this.dispatchAnyDrive(data.selectedVehicle, data.anyDriveInfo);
        }
      });
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    onUnload: function () {
      this.eventChannel = null;
    }
  });
</script>

<script type="application/json">
  {
    "component": true,
    "navigationStyle": "custom",
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image",
      "t-dialog": "tdesign-miniprogram/dialog/dialog"
    }
  }
</script>

<style lang="scss" scoped>
  .result-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: -1;
  }
  .title {
    font-size: 24px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(26, 26, 26, 1);
    margin-top: 438px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .text {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(100, 100, 100, 1);
    margin-top: 16px;
    text-align: center;
  }
  .btns {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    :first-child {
      margin-right: 19px;
    }

    .btn {
      width: 156px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      border-radius: 20px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
    }
  }
  .fail-vehicle {
    padding-bottom: 4px;
    padding-right: 4px;
    padding-left: 16px;
    padding-top: 11px;
    margin: auto;
    width: 327px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    .tip {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(250, 44, 25, 1);
    }
    .again-btn {
      width: 96px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      border: 1px solid rgba(204, 204, 204, 1);
      border-radius: 20px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
    }
    .vehicle-container {
      .row-conatiner {
        display: flex;
        flex-wrap: wrap;
      }
      .vehicle {
        width: 90px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        background: rgba(245, 245, 245, 1);
        border-radius: 16px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 12px;
        margin-right: 12px;
      }
    }
  }
  .dialog-content {
    .vehicle-num {
      display: flex;
    }
    .vehicle-container {
      background: rgba(245, 245, 245, 1);
      border-radius: 16px;
      padding-left: 8px;
      padding-top: 8px;
      height: 116px;
      .row-conatiner {
        display: flex;
        flex-wrap: wrap;
      }
      .vehicle {
        margin-right: 8px;
        margin-bottom: 8px;
        height: 28px;
        width: 82px;
        line-height: 28px;
        text-align: center;
        background: rgba(255, 255, 255, 1);
        border-radius: 16px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
</style>
