// fm.min-1.1.2.7.js 文档：https://derisks.jdfmgt.com/home/<USER>
const PL=wx;let exMess={};let exWorker=null;const _fmOpt={};let fs=null;try{fs=PL['getFileSystemManager']();}catch(a){console['log'](a);}const baseUrl=_fmOpt['inDev']?'https://pre-we.jd.com':'https://we.jd.com';const keyStr='23IL<N01c7KvwZO56RSTAfghiFyzWJqVabGH4PQdopUrsCuX*xeBjkltDEmn89.-';const md5=function(){function b(m,n){const o=(m&0xffff)+(n&0xffff);const p=(m>>0x10)+(n>>0x10)+(o>>0x10);return p<<0x10|o&0xffff;}function c(m,n){return m<<n|m>>>0x20-n;}function d(m,n,o,p,r,u){return b(c(b(b(n,m),b(p,u)),r),o);}function e(m,n,o,p,q,r,u){return d(n&o|~n&p,m,n,q,r,u);}function f(m,n,o,p,q,r,u){return d(n&p|o&~p,m,n,q,r,u);}function g(m,n,o,p,q,r,u){return d(n^o^p,m,n,q,r,u);}function h(m,n,o,p,q,r,u){return d(o^(n|~p),m,n,q,r,u);}function i(m){let n=0x67452301;let o=-0x10325477;let p=-0x67452302;let q=0x10325476;for(let r=0x0;r<m['length'];r+=0x10){const s=n;const t=o;const u=p;const v=q;n=e(n,o,p,q,m[r+0x0],0x7,-0x28955b88);q=e(q,n,o,p,m[r+0x1],0xc,-0x173848aa);p=e(p,q,n,o,m[r+0x2],0x11,0x242070db);o=e(o,p,q,n,m[r+0x3],0x16,-0x3e423112);n=e(n,o,p,q,m[r+0x4],0x7,-0xa83f051);q=e(q,n,o,p,m[r+0x5],0xc,0x4787c62a);p=e(p,q,n,o,m[r+0x6],0x11,-0x57cfb9ed);o=e(o,p,q,n,m[r+0x7],0x16,-0x2b96aff);n=e(n,o,p,q,m[r+0x8],0x7,0x698098d8);q=e(q,n,o,p,m[r+0x9],0xc,-0x74bb0851);p=e(p,q,n,o,m[r+0xa],0x11,-0xa44f);o=e(o,p,q,n,m[r+0xb],0x16,-0x76a32842);n=e(n,o,p,q,m[r+0xc],0x7,0x6b901122);q=e(q,n,o,p,m[r+0xd],0xc,-0x2678e6d);p=e(p,q,n,o,m[r+0xe],0x11,-0x5986bc72);o=e(o,p,q,n,m[r+0xf],0x16,0x49b40821);n=f(n,o,p,q,m[r+0x1],0x5,-0x9e1da9e);q=f(q,n,o,p,m[r+0x6],0x9,-0x3fbf4cc0);p=f(p,q,n,o,m[r+0xb],0xe,0x265e5a51);o=f(o,p,q,n,m[r+0x0],0x14,-0x16493856);n=f(n,o,p,q,m[r+0x5],0x5,-0x29d0efa3);q=f(q,n,o,p,m[r+0xa],0x9,0x2441453);p=f(p,q,n,o,m[r+0xf],0xe,-0x275e197f);o=f(o,p,q,n,m[r+0x4],0x14,-0x182c0438);n=f(n,o,p,q,m[r+0x9],0x5,0x21e1cde6);q=f(q,n,o,p,m[r+0xe],0x9,-0x3cc8f82a);p=f(p,q,n,o,m[r+0x3],0xe,-0xb2af279);o=f(o,p,q,n,m[r+0x8],0x14,0x455a14ed);n=f(n,o,p,q,m[r+0xd],0x5,-0x561c16fb);q=f(q,n,o,p,m[r+0x2],0x9,-0x3105c08);p=f(p,q,n,o,m[r+0x7],0xe,0x676f02d9);o=f(o,p,q,n,m[r+0xc],0x14,-0x72d5b376);n=g(n,o,p,q,m[r+0x5],0x4,-0x5c6be);q=g(q,n,o,p,m[r+0x8],0xb,-0x788e097f);p=g(p,q,n,o,m[r+0xb],0x10,0x6d9d6122);o=g(o,p,q,n,m[r+0xe],0x17,-0x21ac7f4);n=g(n,o,p,q,m[r+0x1],0x4,-0x5b4115bc);q=g(q,n,o,p,m[r+0x4],0xb,0x4bdecfa9);p=g(p,q,n,o,m[r+0x7],0x10,-0x944b4a0);o=g(o,p,q,n,m[r+0xa],0x17,-0x41404390);n=g(n,o,p,q,m[r+0xd],0x4,0x289b7ec6);q=g(q,n,o,p,m[r+0x0],0xb,-0x155ed806);p=g(p,q,n,o,m[r+0x3],0x10,-0x2b10cf7b);o=g(o,p,q,n,m[r+0x6],0x17,0x4881d05);n=g(n,o,p,q,m[r+0x9],0x4,-0x262b2fc7);q=g(q,n,o,p,m[r+0xc],0xb,-0x1924661b);p=g(p,q,n,o,m[r+0xf],0x10,0x1fa27cf8);o=g(o,p,q,n,m[r+0x2],0x17,-0x3b53a99b);n=h(n,o,p,q,m[r+0x0],0x6,-0xbd6ddbc);q=h(q,n,o,p,m[r+0x7],0xa,0x432aff97);p=h(p,q,n,o,m[r+0xe],0xf,-0x546bdc59);o=h(o,p,q,n,m[r+0x5],0x15,-0x36c5fc7);n=h(n,o,p,q,m[r+0xc],0x6,0x655b59c3);q=h(q,n,o,p,m[r+0x3],0xa,-0x70f3336e);p=h(p,q,n,o,m[r+0xa],0xf,-0x100b83);o=h(o,p,q,n,m[r+0x1],0x15,-0x7a7ba22f);n=h(n,o,p,q,m[r+0x8],0x6,0x6fa87e4f);q=h(q,n,o,p,m[r+0xf],0xa,-0x1d31920);p=h(p,q,n,o,m[r+0x6],0xf,-0x5cfebcec);o=h(o,p,q,n,m[r+0xd],0x15,0x4e0811a1);n=h(n,o,p,q,m[r+0x4],0x6,-0x8ac817e);q=h(q,n,o,p,m[r+0xb],0xa,-0x42c50dcb);p=h(p,q,n,o,m[r+0x2],0xf,0x2ad7d2bb);o=h(o,p,q,n,m[r+0x9],0x15,-0x14792c6f);n=b(n,s);o=b(o,t);p=b(p,u);q=b(q,v);}return[n,o,p,q];}function j(m){const n='0123456789abcdef';let o='';for(let p=0x0;p<m['length']*0x4;p++){o+=n['charAt'](m[p>>0x2]>>p%0x4*0x8+0x4&0xf)+n['charAt'](m[p>>0x2]>>p%0x4*0x8&0xf);}return o;}function k(m){const n=(m['length']+0x8>>0x6)+0x1;const o=new Array(n*0x10);for(let p=0x0;p<n*0x10;p++)o[p]=0x0;for(let q=0x0;q<m['length'];q++){o[q>>0x2]|=(m['charCodeAt'](q)&0xff)<<q%0x4*0x8;}o[i>>0x2]|=0x80<<i%0x4*0x8;o[n*0x10-0x2]=m['length']*0x8;return o;}function l(m){return j(i(k(m)));}return{'hexMD5':l};}();function _getSystemInfo(b){return new Promise((c,d)=>{PL['getSystemInfo']({'success'(f){_setDeviceInfo({'vlv':f['SDKVersion'],'ve':f['version']||'','fs':f['fontSizeSetting']||-0x1,'la':f['language']||'','br':f['brand']||'','mo':f['model']||'','pr':f['pixelRatio']||-0x1,'pl':f['platform']||'','sh':f['screenHeight']||'','sw':f['screenWidth']||'','sbh':f['statusBarHeight']||'','sy':f['system']||'','wh':f['windowHeight']||'','ww':f['windowWidth']||'','bl':f['benchmarkLevel']||''},b);c();},'fail'(e){d(e);},'complete'(){}});});}function _getScreenBrightness(b){return new Promise((c,d)=>{PL['getScreenBrightness']({'success'(e){_setDeviceInfo({'sbr':e['value']},b);c();},'fail'(e){d(e);},'complete'(){}});});}function _getNetworkType(b){return new Promise((c,d)=>{PL['getNetworkType']({'success'(f){_setDeviceInfo({'nt':f['networkType']||''},b);c();},'fail'(e){d(e);},'complete'(){}});});}function _getUserInfo(b){return new Promise((c,d)=>{PL['getUserInfo']({'success':function(f){f['userInfo']&&_setDeviceInfo({'au':f['userInfo']['avatarUrl'],'ge':f['userInfo']['gender'],'nn':f['userInfo']['nickName'],'city':f['userInfo']['city'],'province':f['userInfo']['province'],'country':f['userInfo']['country']},b);c();},'fail':function(f){d(f);}});});}function _getWifiInfo(b){return new Promise((c,d)=>{try{PL['getConnectedWifi']({'success':function(f){if(f['wifi']){let g={};g['bs']=f['wifi']['BSSID'];g['ss']=f['wifi']['SSID'];g['aj']=f['wifi']['autoJoined'];g['jj']=f['wifi']['justJoined'];g['sc']=f['wifi']['secure'];g['sgs']=f['wifi']['signalStrength'];_setDeviceInfo({'wi':g},b);}c();},'fail'(f){d(f);}});}catch(f){d(f);}});}function _getHCEState(b){return new Promise((c,d)=>{try{PL['getHCEState']({'success':function(f){_setDeviceInfo(f&&0x0==f['errCode']?{'hh':0x0}:f['errCode']?{'hh':f['errCode']}:{'hh':0x1},b);c();},'fail':function(f){_setDeviceInfo({'hh':0x1},b);c();}});}catch(f){_setDeviceInfo({'hh':0x1},b);c();}});}function _getAppKeyAndName(){return PL['getAccountInfoSync']();}var header={'Content-Type':'text/plain'};function getData(b,c,d,e,f){const g=tdencrypt(b['deviceInfo']);PL['request']({'url':baseUrl+'/stone/1/'+(c==undefined||c==null||c==''?'L64RTJ562VJEYNEQN67XMUWSR4UFLOIQHJYZ3MWERRIKJGP24SDSBDS4I4AMVU24Y3Y7A4UPDICN2':c),'method':'POST','header':header,'data':g,'success':h=>{if(h['data']&&h['data']['code']==0x0&&h['data']['data']){_fmOpt['expireTime']=h['data']['data']['expireTime']||'600000';PL['setStorageSync']('_we_tk',h['data']['data']['tk']);PL['setStorageSync']('tk',h['data']['data']['tk']);let i=new Date()['getTime']();PL['setStorageSync']('oldTime',i);let j=h['data']['data']['tk'];try{if(fs){fs['writeFileSync'](wx['env']['USER_DATA_PATH']+'/tk.txt',j,'utf8');}}catch(k){console['log'](k);}}},'fail'(){},'complete'(h){reportCallback(e,h);}});}function getTk(b,c,d){let e=PL['getStorageSync']('_we_tk')||PL['getStorageSync']('tk');if(!e){try{if(fs){fs['readFile']({'filePath':wx['env']['USER_DATA_PATH']+'/tk.txt','encoding':'utf8','position':0x0,'success'(f){let g=/^(jdd|L64R)/;let h=g['test'](f['data'])?f['data']:'';b(h,c,d);},'fail'(f){b('',c,d);}});}else{b('',c,d);}}catch(f){b('',c,d);console['log'](f);}}else{b(e,c,d);}}function reportData(b,c,d){let e=c['_data']['openid'];if(b){getData(c,b,'',d);}else{if(e||exMess&&exMess['codeOff']){getData(c,'','',d);}else{PL['login']({'force':![],'success':f=>{getData(c,'',f['code'],d,f['anonymousCode']);},'fail':()=>{getData(c,'','',d);}});}}}function reportCallback(b,c){if(b&&typeof b==='function'){const d=PL['getStorageSync']('_we_tk')||PL['getStorageSync']('tk');if(c&&c['data']){if(c['data']['code']==0x0){b({'tk':c['data']['data']['tk']});}else{b({'errMsg':c['data']['msg'],'tk':d});}}else{b({'errMsg':c['errMsg'],'tk':d});}}}function tdencrypt(b){const c=JSON['stringify'](b);b=encodeURIComponent(c);let d='';let e;let f;let g='';let h;let j;let k;let l='';let m=0x0;do{e=b['charCodeAt'](m++);f=b['charCodeAt'](m++);g=b['charCodeAt'](m++);h=e>>0x2;j=(e&0x3)<<0x4|f>>0x4;k=(f&0xf)<<0x2|g>>0x6;l=g&0x3f;if(isNaN(f)){k=l=0x40;}else if(isNaN(g)){l=0x40;}d=d+keyStr['charAt'](h)+keyStr['charAt'](j)+keyStr['charAt'](k)+keyStr['charAt'](l);e=f=g='';h=j=k=l='';}while(m<b['length']);return d+'/';}function compareVersion(b,c){b=b['split']('.');c=c['split']('.');const d=Math['max'](b['length'],c['length']);while(b['length']<d){b['push']('0');}while(c['length']<d){c['push']('0');}for(let e=0x0;e<d;e++){const f=parseInt(b[e]);const g=parseInt(c[e]);if(f>g){return 0x1;}if(f<g){return-0x1;}}return 0x0;}function getCanvasId(b,c){const d=PL['getSystemInfoSync']()['SDKVersion'];if(compareVersion(d,'1.9.9')>=0x0&&!_fmOpt['canvasOff']){_getDeviceUUID(b,b['deviceInfo'],e=>{_setDeviceInfo({'ci':e},b);c&&c();});}else{c&&c();}}function _getDeviceUUID(b,c,d,e,f){const g={'st':new Date()['getTime']()};const {ctx}=b['_dom'];const {canvasId}=b['_data'];if(!ctx){if(e&&e instanceof Function){e({'errMsg':'Canvas\x20creation\x20failed,Please\x20check\x20if\x20canvasId\x20is\x20consistent'});}return;}const h=c['pl']||'';if(h=='android'){ctx['globalCompositeOperation']='xor';}else{ctx['globalCompositeOperation']='multiply';}ctx['rect'](0x0,0x0,0xa,0xa);ctx['rect'](0x2,0x2,0x6,0x6);ctx['textBaseline']='alphabetic';ctx['fillStyle']='#ff6600';ctx['fillRect'](0x20,0x1,0x3e,0x14);ctx['fillStyle']='#006699';ctx['fillText']('Cwwm\x20aa\x20fjorddbank\x20glbyphs\x20veext\x20qtuiz,\x20😃',0x2,0xf);ctx['fillStyle']='rgba(102,\x20204,\x200,\x200.2)';ctx['font']='18pt\x20Arial';ctx['fillText']('Cwwm\x20aa\x20fjorddbank\x20glbyphs\x20veext\x20qtuiz,\x20😃',0x4,0x2d);ctx['fillStyle']='rgb(255,0,255)';g['txts']=new Date()['getTime']();const j=0x10;let k=j;let l=j;ctx['beginPath']();ctx['arc'](k,l,j,0x0,Math['PI']*0x2,!![]);ctx['closePath']();ctx['fill']();k=j*0x2,l=j;ctx['fillStyle']='rgb(0,255,255)';ctx['beginPath']();ctx['arc'](k,l,j,0x0,Math['PI']*0x2,!![]);ctx['closePath']();ctx['fill']();k=j*1.5,l=j*0x2;ctx['fillStyle']='rgb(255,255,0)';ctx['beginPath']();ctx['arc'](k,l,j,0x0,Math['PI']*0x2,!![]);ctx['closePath']();ctx['fill']();k=j*1.5,l=j*1.5;ctx['fillStyle']='rgb(255,0,255)';ctx['arc'](k,l,j*1.5,0x0,Math['PI']*0x2,!![]);ctx['arc'](k,l,j/0x2,0x0,Math['PI']*0x2,!![]);ctx['fill']();g['circlet']=new Date()['getTime']();const m=0xf;ctx['beginPath']();ctx['textBaseline']='top';ctx['font']='14px\x20Arial';ctx['fillStyle']='#000000';ctx['closePath']();let n='';const p=['cpi','br','mo','pr','pl','sh','sw','hh'];for(let r=0x0;r<p['length'];r++){const s=p[r];n=c[s]||'';ctx['fillText'](n,0x0,m*(r+0x1));}g['fixedt']=new Date()['getTime']();let q='';ctx['draw'](![],()=>{PL['canvasToTempFilePath']({'canvasId':canvasId,'x':0x0,'y':0x0,'width':0x64,'height':0x64,'destWidth':0x64,'destHeight':0x64,'fileType':'png','success'(t){try{g['drawt']=new Date()['getTime']();const u=PL['getFileSystemManager']()['readFileSync'](t['tempFilePath'],'base64');q=md5['hexMD5'](u);g['md5t']=new Date()['getTime']();c['cavt']=g;d&&d(q);}catch(v){d&&d('');}},'fail'(){d&&d('');},'complete'(t){f&&f(t);}});});}function initDeviceInfo(b){const c=_getAppKeyAndName();_setDeviceInfo({'vid':c['miniProgram']['appId']},b);return Promise['all']([_getSystemInfo(b),_getScreenBrightness(b),_getNetworkType(b),_getHCEState(b)]);}const tkWorker=function(b,c,d,e){let f=typeof b==='string'?b:'';let g=typeof c==='string'?c:'';this['_data']={'canvasId':d,'openid':f,'unionId':g};this['_dom']={'ctx':e};};function _setDeviceInfo(b,c){typeof b==='object'&&(c['deviceInfo']={...c['deviceInfo'],...b});}tkWorker['prototype']={'init'(){const b=new Date()['getTime']();this['deviceInfo']={};_setDeviceInfo({'sv':'*******','clist':b,'bk':_fmOpt['bizKey'],'unionId':this['_data']['unionId'],'cliet':b,'oid':this['_data']['openid'],'mpt':0x1},this);},'getToken'(b){const c=this;initDeviceInfo(c)['then'](d=>{_setDeviceInfo({'cliet':new Date()['getTime']()},c);if(!_fmOpt['canvasOff']){getCanvasId(c,()=>{getTk(reportData,c,b);});}else{getTk(reportData,c,b);}});}};module['exports']={'config':(b,c)=>{exMess['canvasId']='fbfd3ec3c4ab4213b9a09b5c5e769ceb';_fmOpt['canvasOff']=c['canvasOff'];_fmOpt['bizKey']=c['bizKey'];_fmOpt['inDev']=c['inDev'];if(!c['canvasOff']){exMess['ctx']=PL['createCanvasContext'](exMess['canvasId'],b);}},'init':b=>{exMess['openid']=b['openid'];exMess['unionId']=b['unionId'];exMess['codeOff']=b['codeOff'];exWorker=new tkWorker(exMess['openid'],exMess['unionId'],exMess['canvasId'],exMess['ctx']);exWorker['init']();},'getEid':b=>{const c=PL['getStorageSync']('_we_tk')||PL['getStorageSync']('tk');let d=new Date()['getTime']();let e=PL['getStorageSync']('oldTime');if(c){b({'tk':c});if(e&&d-e>_fmOpt['expireTime']){exWorker['getToken']('');}return c;}return exWorker&&exWorker['getToken'](b);},'tkWorker':tkWorker};
