
<template>
  <view
    class="call-front-btn"
    wx:class="{{styleInfo.otherClassName}}"
    bindtap="handleClick"
  >
    <t-image
      src="{{styleInfo.imageSrc}}"
      mode="aspectFill"
      width="{{styleInfo.imageWidth}}"
      height="{{styleInfo.imageHeight}}"
    />
    <text class="label" wx:class="{{styleInfo.otherClassName}}">呼叫一线</text>
  </view>

  <call-front-operate
    visible="{{visible}}"
    call-front="call-front"
    vehicleName="{{vehicleName}}"
    bind:onVisibleChange="onVisibleChange"
  />
</template>

<script  lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, SingleVehicle } from 'shared/assets/imageUrl';

  createComponent({
    properties: {
      // 'workBench'  ||  'singelVehicle'
      sourceType: {
        type: String,
        value: ''
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      WorkBenchUrl: WorkBenchUrl,
      SingleVehicle: SingleVehicle,
      styleInfo: {},
      visible: false
    },
    methods: {
      formatStyleInfo() {
        if (this.sourceType === 'workBench') {
          this.styleInfo = {
            imageSrc: this.WorkBenchUrl.CallLine,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'workBench'
          };
        } else {
          this.styleInfo = {
            imageSrc: this.SingleVehicle.CallFrontliner,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'singelVehicle'
          };
        }
      },
      handleClick() {
        if (this.sourceType === 'workBench') {
          wx.navigateTo({ url: '/pages/selectVehiclePages/callFrontPage' });
        } else if (this.sourceType === 'singleVehicle') {
          this.visible = true;
        }
      },
      onVisibleChange() {
        this.visible = false;
      }
    },
    lifetimes: {
      created: function () {
        this.formatStyleInfo();
      }
    }
  });
</script>

<style lang="scss">
  .call-front-btn {
    display: flex;
    flex-direction: column;
    align-items: center;

    .label {
      text-align: center;
      font-family: PingFang SC;
      font-weight: normal;
      &.workBench {
        font-size: 12px;
        bottom: 10px;
        color: rgba(51, 51, 51, 1);
        margin-top: 6px;
      }
      &.singelVehicle {
        color: rgba(51, 51, 51, 1);
        font-size: 11px;
      }
    }
  }
  .call-front {
    width: 100%;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image",
      "call-front-operate": "widgets/commandOperate/callFrontOperate.mpx"
    }
  }
</script>
