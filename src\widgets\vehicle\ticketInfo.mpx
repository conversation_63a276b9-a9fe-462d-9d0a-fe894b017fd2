<template>
  <view class="ticket-info-card" wx:if="{{ticketInfo.issueNumber}}">
    <view class="info-section" wx:class="{{ ['one'] }}">
      <view class="info-item">
        <text class="info-label">工单编号</text>
        <text class="info-value"
          >{{ ticketInfo.issueNumber }}({{ issueStatusName }})</text
        >
      </view>
    </view>

    <scroll-view
      class="info-section"
      wx:class="{{ ['two'] }}"
      scroll-y
      type="list"
      wx:if="{{ticketInfo.alarmEventList.length > 0}}"
    >
      <view
        class="alarm-event-info"
        wx:for="{{ticketInfo.alarmEventList}}"
        wx:key="index"
      >
        <view class="alarm-event">
          <view class="number">
            {{ index + 1 }}
          </view>
          <text class="alarm-event-name">
            {{ item }}
          </text></view
        >
        <view
          class="line"
          wx:if="{{index < ticketInfo.alarmEventList.length-1}}"
        ></view>
      </view>
    </scroll-view>

    <view class="info-section" wx:class="{{ ['three'] }}">
      <view class="info-item">
        <text class="info-label">工单持续时间</text>
        <text class="info-value">{{ continueTime }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">远驾跟进人</text>
        <text class="info-value">{{
          ticketInfo.acceptUsername || '暂无'
        }}</text>
      </view>
    </view>
  </view>
  <view class="no-ticket-icon" wx:else>
    <image src="{{SingleVehicle.NoTicketIcon}}" />
    <view>暂无工单</view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { getEnumEventName, formatTimestamp } from 'shared/utils/utils';
  import { SingleVehicle } from 'shared/assets/imageUrl';
  import { SingleVehicleApi } from 'shared/api/singleVehicle';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createComponent<any>({
    properties: {
      vehicleName: String
    },
    data: {
      SingleVehicle,
      timer: null,
      continueTime: '-',
      fetchApi: new SingleVehicleApi(),
      ticketInfo: {},
      issueStatusName: null
    },
    lifetimes: {
      created() {
        this.getVehicleTicketInfo();
      },
      detached() {
        clearInterval(this.timer);
      }
    },
    methods: {
      async getVehicleTicketInfo() {
        const res = await this.fetchApi.getVehicleTicketInfo(this.vehicleName);
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            ticketInfo: res.data,
            issueStatusName: getEnumEventName(
              'ISSUE_STATUS_ENUM',
              res.data?.issueStatus
            )
          });
          const createdTimeStamp: number = new Date(
            res?.data?.createTime
          ).getTime();
          this.formatContinueTime(createdTimeStamp);

          this.timer = setInterval(() => {
            this.formatContinueTime(createdTimeStamp);
          }, 1000);
        }
      },
      formatContinueTime(createdTimeStamp: number) {
        const nowStamp = new Date().getTime();
        this.setData({
          continueTime: formatTimestamp(nowStamp - createdTimeStamp, 'hh:mm:ss')
        });
      }
    }
  });
</script>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {}
  }
</script>

<style lang="scss" scoped>
  .ticket-info-card {
    padding: 8px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 1);
  }

  .info-section {
    padding-bottom: 8px;
    padding-top: 8px;
    &.one {
      border-bottom: 1px solid rgba(151, 151, 151, 1);
    }
    &.two {
      border-bottom: 1px solid rgba(151, 151, 151, 1);
      height: 40vh;
    }
  }

  .alarm-event-info {
    .alarm-event {
      display: flex;
      align-items: center;
      height: 20px;
      .number {
        min-width: 20px;
        min-height: 20px;
        line-height: 20px;
        text-align: center;
        border-radius: 50%;
        background: rgba(254, 214, 207, 1);
        font-size: 14px;
        font-family: JDZhengHT;
        font-weight: normal;
        color: rgba(250, 44, 25, 1);
      }
      .alarm-event-name {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(50, 50, 50, 1);
      }
    }
    .line {
      margin-left: 9px;
      width: 2px;
      height: 34px;
      background: rgba(254, 214, 207, 1);
    }
  }

  .info-item {
    margin-bottom: 8px;
    display: flex;
  }

  .info-label {
    width: 75px;
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(153, 153, 153, 1);
  }

  .info-value {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(51, 51, 51, 1);
  }

  .info-item:last-child {
    margin-bottom: 0;
  }

  .no-ticket-icon {
    height: 500px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(153, 153, 153, 1);
    image {
      width: 96px;
      height: 109px;
    }
  }
</style>
