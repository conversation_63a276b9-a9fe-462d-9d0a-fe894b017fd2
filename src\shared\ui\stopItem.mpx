<template>
  <view wx:class="{{{'redCard': isOverTime, 'orangeCard': !isOverTime}}}">
    {{ isOverTime ? '停靠超时' : '停靠倒计时' }}
    {{ showTime }}
  </view>
</template>
<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  createComponent<any>({
    properties: {
      status: { type: String, value: '' },
      waitingTime: { type: String, value: '' },
      arrivedTime: { type: String, value: '' }
    },
    data: {
      showTime: null,
      timeout: null,
      overTimeCount: 0,
      isOverTime: false // true超时  false倒计时
    },
    methods: {
      startTime(fn: any) {
        this.timeout = setTimeout(() => {
          fn();
        }, 1000);
      },
      formatTimingString(timeCount: any) {
        const setDb = (num: any) => {
          if (num < 10) {
            return '0' + num;
          } else {
            return '' + num;
          }
        };
        const hour = setDb(Math.floor((timeCount % 86400000) / 3600000));
        const minutes = setDb(Math.floor((timeCount % 3600000) / 60000));
        const seconds = setDb(Math.floor((timeCount % 60000) / 1000));
        return hour + ':' + minutes + ':' + seconds;
      }
    },

    lifetimes: {
      created: function () {
        const fn = () => {
          const arrivedTimeStamp = new Date(this.arrivedTime).getTime() || 0;
          let timer =
            this.waitingTime * 60000 -
              (new Date().getTime() - arrivedTimeStamp) || 0;
          if (timer < 0) {
            this.overTimeCount = Math.abs(timer);
          }

          if (timer >= 1000) {
            timer = timer - 1000;
            const timingString = this.formatTimingString(timer);
            this.showTime = timingString;
          } else {
            this.overTimeCount =
              this.waitingTime && arrivedTimeStamp
                ? new Date().getTime() -
                  arrivedTimeStamp -
                  this.waitingTime * 60000
                : arrivedTimeStamp
                ? new Date().getTime() - arrivedTimeStamp
                : this.overTimeCount + 1000;
            const timingString = this.formatTimingString(this.overTimeCount);
            this.showTime = timingString;
            this.isOverTime = true;
          }

          this.timeout = this.startTime(fn);
        };
        this.timeout = this.startTime(fn);
      },
      detached: function () {
        clearInterval();
      }
    }
  });
</script>
<style lang="scss">
  .redCard {
    height: 18px;
    line-height: 18px;
    font-size: 10px;
    font-weight: normal;
    font-family: JDZhengHT;
    color: rgba(250, 44, 25, 1);
    background: rgba(250, 44, 25, 0.07);
    border-radius: 19px;
    padding: 0px 8px;
    margin-left: 20px;
  }
  .orangeCard {
    height: 18px;
    line-height: 18px;
    font-size: 10px;
    font-weight: normal;
    font-family: JDZhengHT;
    color: rgba(232, 140, 0, 1);
    background: rgba(255, 191, 0, 0.07);
    border-radius: 19px;
    padding: 0px 8px;
    margin-left: 20px;
  }
</style>