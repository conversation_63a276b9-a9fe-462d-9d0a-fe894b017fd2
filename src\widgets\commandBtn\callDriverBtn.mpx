<template>
  <view
    class="call-driver-btn"
    wx:class="{{styleInfo.otherClassName}}"
    bindtap="handleClick"
  >
    <t-image
      src="{{styleInfo.imageSrc}}"
      mode="aspectFill"
      width="{{styleInfo.imageWidth}}"
      height="{{styleInfo.imageHeight}}"
    />
    <text class="label" wx:class="{{styleInfo.otherClassName}}">呼叫远驾</text>
  </view>
  <call-driver-operate
    vehicleName="{{vehicleName}}"
    visible="{{modalVisible}}"
    bind:onVisibleChange="onVisibleChange"
  ></call-driver-operate>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, SingleVehicle } from 'shared/assets/imageUrl';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { CallFrontApi } from 'shared/api/callFront';

  createComponent({
    properties: {
      // 'workBench'  ||  'singelVehicle'
      sourceType: {
        type: String,
        value: ''
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      fetchApi: new CallFrontApi(),
      WorkBenchUrl: WorkBenchUrl,
      SingleVehicle: SingleVehicle,
      styleInfo: {},
      modalVisible: false
    },
    methods: {
      formatStyleInfo() {
        if (this.sourceType === 'workBench') {
          this.styleInfo = {
            imageSrc: this.WorkBenchUrl.CallDriver,
            imageWidth: '93px',
            imageHeight: '87px',
            otherClassName: 'workBench'
          };
        } else {
          this.styleInfo = {
            imageSrc: this.SingleVehicle.CallCockpit,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'singelVehicle'
          };
        }
      },
      handleClick() {
        if (this.sourceType === 'workBench') {
          wx.navigateTo({ url: '/pages/selectVehiclePages/callDriverPage' });
        } else if (this.sourceType === 'singleVehicle') {
          this.setData({
            modalVisible: true
          });
        }
      },
      onVisibleChange() {
        this.setData({
          modalVisible: false
        });
      }
    },
    lifetimes: {
      created: function () {
        this.formatStyleInfo();
      }
    }
  });
</script>

<style lang="scss">
  .call-driver-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    &.workBench {
      width: 93px;
      height: 87px;
      position: relative;
    }
    .label {
      text-align: center;
      font-family: PingFang SC;
      font-weight: normal;
      &.workBench {
        font-size: 12px;
        position: absolute;
        bottom: 10px;
        color: rgba(60, 110, 240, 1);
      }
      &.singelVehicle {
        color: rgba(51, 51, 51, 1);
        font-size: 11px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image",
      "call-driver-operate": "widgets/commandOperate/callDriverOperate"
    }
  }
</script>

