<template>
  <view class="my-container">
    <navi-bar
      show-address="{{true}}"
      title="京小鸽"
      bindsearchlocation="openSearchLocation"
      backgroundColor="transparent"
    ></navi-bar>
    <view class="user-info">
      <view class="user-avatar">
        <image
          src="https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/login/avatar.png?Expires=3872319611&AccessKey=n828WHAXD584pTvi&Signature=fOKrcbBGkTNIooNW7I89D4%2Bt%2F18%3D"
        />
      </view>
      <view class="info-text">
        <text class="name">{{ userName }}</text>
        <text class="phone">{{ maskPhone }}</text>
      </view>
    </view>
    <view class="setting-board">
      <view
        class="field-item"
        bindtap="handleSheetVisible"
        wx:if="{{collisionNotification}}"
      >
        <view class="form-label">碰撞电话语音通知</view>
        <view class="form-value">
          <view wx:if="{{stationNum}}" style="display: inline-block">
            <view style="display: inline-block">{{ stationNum }}</view>
            <view style="display: inline-block; color: #808080"
              >个站点已开启</view
            >
          </view>
        </view>
      </view>
    </view>
    <view class="btn primry" bindtap="loginOut">退出登录</view>
    <action-sheet
      visible="{{sheetVisible}}"
      placement="bottom"
      title="选择站点"
      desc="开启可接收碰撞电话通知"
      bindclose="handleClose"
      wx:key="{{sheetKey}}"
    >
      <view style="width: 100%; height: 600px">
        <scroll-view
          class="btn-list"
          type="list"
          scroll-y
          style="width: 100%; height: 100%"
          show-scrollbar="{{false}}"
        >
          <t-divider />
          <t-cell
            title="{{item.stationName}}"
            wx:for="{{stationList}}"
            wx:key="stationId"
          >
            <t-switch
              bindchange="handleChange"
              data-index="{{item.stationId}}"
              value="{{item.opened || item.owner}}"
              disabled="{{item.owner}}"
              slot="note"
            />
          </t-cell>
        </scroll-view>
      </view>
    </action-sheet>
  </view>
  <search-location
    visible="{{showSearchLocation}}"
    bindclosepopup="closeSearchLocation"
  ></search-location>
  <real-tab-bar />
</template>
<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import { My } from 'shared/api/my';
  import { HTTPSTATUSCODE } from '../../shared/api/fetch';
  import { NotificationApi } from 'shared/api/notification';
  import { IndexPageUrl } from 'shared/assets/imageUrl';
  import { sendGlobalEvent } from 'shared/utils/emit';
  import permissionManager from '../../shared/utils/permissionManager';
  import { PermissionKey } from '../../shared/utils/constant';
  const fetchApi = new My();
  createPage({
    data: {
      userName: '',
      sheetKey: '0',
      IndexPageUrl,
      stationNum: 0,
      sheetVisible: false,
      stationList: [],
      showSearchLocation: false,
      selectedTabKey: 'my',
      notificationApi: new NotificationApi(),
      maskPhone: ''
    },
    computed: {
      collisionNotification() {
        return permissionManager.hasPermission(PermissionKey.collisionNoteBtn);
      }
    },
    methods: {
      loginOut() {
        wx.showModal({
          content: '确认退出小程序？',
          confirmText: '确认',
          cancelText: '取消',
          success(res) {
            if (res.confirm) {
              wx.removeStorageSync('JD_AUTH_TOKEN');
              wx.removeStorageSync('JD_REFRESH_TOKEN');
              wx.removeStorageSync('appResourceInfoList');
              wx.removeStorageSync('authorizedTabs');
              const returnPage = encodeURIComponent('/pages/my/index');
              wx.redirectTo({
                url: `/pages/login/index/index?returnPage=${returnPage}`
              });
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      handleClose() {
        this.sheetVisible = false;
      },
      handleSheetVisible() {
        this.sheetVisible = true;
        this.getStationList();
      },
      getStationList() {
        fetchApi.getConfigStationCrashList().then((res: any) => {
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              stationList: res?.data || [],
              stationNum: res?.data?.filter((i: any) => i.opened)?.length || 0,
              sheetKey: Date.now().toString()
            });
          }
        });
      },
      handleChange(e: any) {
        const { value } = e?.detail;
        const { dataset } = e?.currentTarget;

        fetchApi
          .configAccidentVoiceNotify({
            key: dataset?.index,
            value
          })
          .then((res: any) => {
            if (res.code != HTTPSTATUSCODE.Success) {
              wx.showToast({
                icon: 'none',
                title: res.message
              });
            } else {
              const item = this.data.stationList?.find(
                (i: any) => i.stationId === dataset?.index
              );
              if (item) {
                item.opened = value;
              }
              this.setData({
                stationList: [...this.data.stationList]
              });
            }
          });

        this.stationNum = value
          ? this.stationNum + 1
          : this.stationNum > 0
            ? this.stationNum - 1
            : 0;
      },
      openSearchLocation(e: any) {
        this.setData({
          showSearchLocation: true
        });
      },
      closeSearchLocation(e: any) {
        this.setData({
          showSearchLocation: false
        });
      },
      handleClickTab(e: any) {
        wx.vibrateShort({
          type: 'heavy',
          success: () => {
            console.log('震动成功');
          },
          fail: () => {
            console.log('震动失败');
          }
        });
        const data = e.currentTarget.dataset;
        const url = data.path;
        this.setData({
          selectedTabKey: data.key
        });
        wx.switchTab({ url });
      },
      async getTodoList() {
        const res: any = await this.notificationApi.getUserTodoList();
        if (res.code === HTTPSTATUSCODE.Success) {
          sendGlobalEvent('update_notice_num', { val: res.data.todoSize });
        }
      },
      async getUserMaskPhone(userName: string) {
        try {
          const res: any = await fetchApi.getUserMaskPhone(userName);
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              maskPhone: res.data.maskPhone
            });
          }
        } catch (err) {
          console.error(err);
        }
      }
    },
    onLoad() {
      const userName = wx.getStorageSync('userName');
      this.userName = userName;
      this.getUserMaskPhone(userName);
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    onShow() {
      this.getStationList();
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selectedTabKey: 'my'
        });
        console.log('getTabBar log success');
      }

      if (this.collisionNotification) {
        this.getStationList();
        this.getTodoList();
      }
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    flex-flow: column;
    background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/workbench/work-bench-bg.png?Expires=3868527449&AccessKey=n828WHAXD584pTvi&Signature=q7QDp4B1AuDo1YYwaAHkwzJJvsc%3D')
      no-repeat center;
    background-size: cover;
  }
  .my-container {
    display: flex;
    flex-flow: column;
    height: 100vh;
  }
  .user-info {
    height: 92px;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 100%
    );
    border-radius: 4px;
    box-sizing: border-box;
    margin: 16px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    .user-avatar {
      width: 56px;
      height: 56px;
      border-radius: 56px;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .info-text {
      display: flex;
      flex-flow: column;
      line-height: 30px;
      margin-left: 16px;
      .name {
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(26, 26, 26, 1);
      }
      .phone {
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(26, 26, 26, 1);
      }
    }
  }
  .setting-board {
    box-sizing: border-box;
    margin: 0 16px 12px;
    flex: 1;
    .field-item {
      display: flex;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      padding: 12px;
    }
  }
  .form-label {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(128, 128, 128, 1);
    margin-right: 6px;
    flex: 1;
    .required {
      color: #fa2c19;
    }
  }
  .form-value {
    flex: 1;
    text-align: right;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: bold;
    color: rgba(26, 26, 26, 1);
    max-width: 77%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding-right: 20px;
    position: relative;
    &::after {
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/workbench/arrow-right.png?Expires=3868521457&AccessKey=n828WHAXD584pTvi&Signature=VHgyCxiYJ7xbPV0JaCcRIF90sDI%3D')
        no-repeat center;
      background-size: contain;
      color: rgba(204, 204, 204, 1);
      position: absolute;
      right: 0;
      top: 1px;
    }
  }
  .btn {
    width: 60%;
    height: 40px;
    border-radius: 40px;
    background: linear-gradient(
      135deg,
      rgba(250, 100, 25, 1) 0%,
      rgba(250, 89, 25, 1) 16.59259259%,
      rgba(250, 63, 25, 1) 55.40740741%,
      rgba(250, 44, 25, 1) 100%
    );
    border-radius: 20px;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    line-height: 40px;
    margin: 180px auto;
  }
  .t-cell__title-text {
    font-size: 14px !important;
    font-family: PingFang SC !important;
    font-weight: normal;
    color: rgba(128, 128, 128, 1) !important;
  }
  .tab-bar {
    bottom: 0;
    width: 100%;
    pointer-events: auto;
    position: absolute;
    height: 90px;
    border-radius: 15px 15px 0 0;
    background-color: #ffff;
    box-shadow: 0px -1px 8px rgb(165 165 165 / 55%);
    display: flex;
    justify-content: space-around;
    align-items: center;
    .tab-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      width: 80px;
      height: 80px;
      &.message {
        position: relative;
        .notice-num {
          top: 10px;
          position: absolute;
          min-width: 20px;
          min-height: 20px;
          border-radius: 10px;
          background-color: red;
          color: white;
          font-size: 12px;
          line-height: 20px;
          text-align: center;
          z-index: 99;
          left: 42px;
        }
      }
    }
    .tab-text-unselected,
    .tab-text-selected {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
    }
    .tab-text-selected {
      color: rgba(26, 26, 26, 1);
    }
  }
  .t-icon-close {
    font-weight: bold !important;
    font-size: 24px !important;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "action-sheet": "../../shared/ui/actionSheet.mpx",
      "t-cell": "tdesign-miniprogram/cell/cell",
      "t-switch": "tdesign-miniprogram/switch/switch",
      "t-divider": "tdesign-miniprogram/divider/divider",
      "search-location": "widgets/searchLocation.mpx",
      "real-tab-bar": "shared/ui/realTabBar.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
