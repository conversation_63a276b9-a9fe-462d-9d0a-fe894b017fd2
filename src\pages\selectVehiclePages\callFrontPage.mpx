
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="呼叫一线"
    backUrl="/pages/workbench/index"
  ></navi-bar>

  <vehicle-list
    selectedVehicleName="{{selectedVehicleName}}"
    tip="选择车辆后，可呼叫一线"
    bind:handleSelect="handleSelect"
  />

  <call-front-operate
    visible="{{visible}}"
    vehicleName="{{selectedVehicleName}}"
    bind:onVisibleChange="onVisibleChange"
  />
</template>

<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  interface Data {
    selectedVehicleName: any;
    visible: any;
  }
  createPage<Data>({
    data: {
      selectedVehicleName: null,
      visible: false
    },
    onLoad() {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    methods: {
      handleSelect(e: any) {
        this.setData({
          selectedVehicleName: e.detail.val
        });
        if (e.detail.val) {
          this.setData({
            visible: true
          });
        }
      },
      onVisibleChange() {
        this.setData({
          visible: false
        });
      }
    },
    onShow: function () {
      console.log('呼叫一线车辆列表', getCurrentPages());
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx",
      "call-front-operate": "widgets/commandOperate/callFrontOperate.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
