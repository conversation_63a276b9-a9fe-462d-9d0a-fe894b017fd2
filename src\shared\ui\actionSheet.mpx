<template>
  <view class="action-sheet">
    <t-popup
      visible="{{ visible }}"
      placement="{{placement}}"
      customStyle="{{ customStyle }}"
    >
      <view class="action-sheet-content">
        <view class="header">
          <text class="title">{{ title }}</text>
          <text class="desc" wx:if="{{desc}}">{{ desc }}</text>
          <t-icon name="close" size="20" bind:tap="handleClose" />
        </view>
        <slot></slot>
      </view>
    </t-popup>
  </view>
</template>

<script>
  import { createComponent } from '@mpxjs/core';

  createComponent({
    properties: {
      visible: {
        type: Boolean,
        value: false
      },
      customStyle: {
        type: String,
        value: ''
      },
      title: {
        type: String,
        value: ''
      },
      desc: {
        type: String,
        value: ''
      },
      placement: {
        type: 'top' | 'left' | 'right' | 'bottom' | 'center',
        value: 'bottom'
      }
    },
    methods: {
      handleClose() {
        this.triggerEvent('close');
      }
    }
  });
</script>

<style lang="scss">
  .action-sheet {
    .action-sheet-content {
      background-color: #fff;
      border-radius: 16px 16px 0 0;
      padding: 16px;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          font-size: 18px;
          font-weight: bold;
        }
        .desc {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: normal;
          color: rgba(128, 128, 128, 1);
          flex: 1;
          margin-left: 8px;
        }
      }
      .description {
        margin-top: 8px;
        font-size: 14px;
        color: #666;
      }
      .image {
        margin-top: 16px;
        width: 100%;
      }
    }
    .content {
      max-height: 600px;
      overflow-y: auto;
      position: relative;
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-icon': 'tdesign-miniprogram/icon/icon',
      't-popup': 'tdesign-miniprogram/popup/popup'
    }
  };
</script>
