<template>
  <view class="accident-form-container">
    <navi-bar
      show-address="{{false}}"
      title="生成事故单"
      show-return="true"
    ></navi-bar>
    <view class="main-container">
      <view class="title-box">{{ vehicleName }}</view>
      <view class="form-view">
        <view class="form-group">
          <view class="field-item field-method">
            <view class="form-label">
              处理方式
              <text class="required">*</text>
            </view>
            <view class="form-options">
              <t-radio-group
                v-model="handleMethod"
                value="{{handleMethod}}"
                bind:change="handleChangeMethod"
              >
                <t-radio value="{{HandleMethodEnum.QUICK_SETTLEMENT}}"
                  >快速处理(私了)
                </t-radio>
                <t-radio
                  value="{{HandleMethodEnum.POLICE_DETERMINATION_NO_INSURANCE}}"
                  >需交警定责，无需保险报案
                </t-radio>
                <t-radio
                  value="{{HandleMethodEnum.POLICE_DETERMINATION_WITH_INSURANCE}}"
                  >需交警定责，需保险报案
                </t-radio>
              </t-radio-group>
            </view>
          </view>
          <t-divider />
          <view class="field-item field-compensated">
            <view class="form-label"
              >赔偿金额 <text class="required">*</text>
            </view>
            <view class="form-options">
              <t-radio-group
                v-model="compensated"
                value="{{compensated}}"
                bind:change="handleChangeCompensated"
              >
                <t-radio value="{{false}}" label="无需赔偿" />
                <t-radio value="{{true}}" label="需赔偿" />
              </t-radio-group>
              <view class="input-wrapper" wx:if="{{compensated === true}}">
                <input
                  class="input"
                  type="digit"
                  wx:model="{{amount}}"
                  value="{{amount}}"
                  placeholder="金额"
                  bindinput="handleChangeAmount"
                />
                <text class="currency"> 元 </text>
              </view>
            </view>
          </view>
          <t-divider />
          <view class="attachment-wrapper">
            <view class="tips">
              上传凭证({{ attachmentList.length }}/20)
              <text class="required">*</text>
              <text class="tip-message">可上传处理协议书，事故现场照片</text>
            </view>
            <upload-image
              defaultAttachmentList="{{ defaultAttachmentList }}"
              bindfileInfo="saveFiles"
              type="{{ 'bumpform' }}"
            />
          </view>
          <t-divider />
          <view
            wx:if="{{handleMethod==HandleMethodEnum.POLICE_DETERMINATION_NO_INSURANCE || handleMethod==HandleMethodEnum.POLICE_DETERMINATION_WITH_INSURANCE}}"
            class="field-item"
          >
            <view class="form-label">
              上报监管(车网) <text class="required">*</text>
            </view>
            <view class="form-options inline">
              <t-radio-group
                v-model="isReportVehicleNet"
                value="{{isReportVehicleNet}}"
                bind:change="changeIsReportVehicleNet"
              >
                <t-radio block="{{false}}" value="{{false}}" label="无需上报" />
                <t-radio block="{{false}}" value="{{true}}" label="需要上报" />
              </t-radio-group>
            </view>
          </view>
          <view
            class="phone-number"
            wx:if="{{handleMethod==HandleMethodEnum.POLICE_DETERMINATION_NO_INSURANCE || handleMethod==HandleMethodEnum.POLICE_DETERMINATION_WITH_INSURANCE}}"
          >
            <view
              bindtap="handleCall('122')"
              wx:if="{{handleMethod==HandleMethodEnum.POLICE_DETERMINATION_NO_INSURANCE || handleMethod==HandleMethodEnum.POLICE_DETERMINATION_WITH_INSURANCE}}"
            >
              <text> 拨打交警电话 </text>
              <image
                src="https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/phone-icon.png?Expires=3871173937&AccessKey=n828WHAXD584pTvi&Signature=%2F4J8dZHCcnSnm0Yw%2B7QFUNHTFbk%3D"
              />
              <text> 122 </text>
            </view>

            <view
              bindtap="handleCall('95590')"
              wx:if="{{handleMethod==HandleMethodEnum.POLICE_DETERMINATION_WITH_INSURANCE}}"
            >
              <text> 拨打保险公司电话 </text>
              <image
                src="https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/phone-icon.png?Expires=3871173937&AccessKey=n828WHAXD584pTvi&Signature=%2F4J8dZHCcnSnm0Yw%2B7QFUNHTFbk%3D"
              />
              <text> 95590 </text>
            </view>
          </view>
        </view>

        <view class="form-group">
          <view class="field-item">
            <view class="form-label">初判事故分类 </view>
            <view class="form-options inline">
              <t-radio-group
                v-model="accidentType"
                value="{{accidentType}}"
                bind:change="handleChangeAccidentType"
              >
                <t-radio block="{{false}}" value="{{AccidentType.PASSIVE}}"
                  >被动碰撞
                </t-radio>
                <t-radio block="{{false}}" value="{{AccidentType.ACTIVE}}"
                  >主动碰撞
                </t-radio>
              </t-radio-group>
            </view>
          </view>
          <t-divider />
          <view class="field-item">
            <view class="form-label">事故责任判定 </view>
            <view class="form-options inline accident-judge">
              <t-radio-group
                v-model="accidentJudge"
                value="{{accidentJudge}}"
                bind:change="handleChangAccidentJudge"
              >
                <t-radio
                  block="{{false}}"
                  value="{{AccidentJudge.FULL_LIABILITY}}"
                  >无人车全责
                </t-radio>
                <t-radio
                  block="{{false}}"
                  value="{{AccidentJudge.NO_LIABILITY}}"
                  >无人车无责
                </t-radio>
                <t-radio
                  block="{{false}}"
                  value="{{AccidentJudge.SHARED_LIABILITY}}"
                  >多方责任
                </t-radio>
              </t-radio-group>
            </view>
          </view>
          <t-divider />
          <view class="field-item flex-columns">
            <view class="label">事故初步原因</view>
            <t-textarea
              class="textarea"
              placeholder="请输入事故初步原因，选填"
              maxlength="{{50}}"
              indicator="true"
              bindchange="handleInput"
              value="{{reason}}"
            ></t-textarea>
          </view>
        </view>
      </view>
    </view>
    <view class="footer">
      <view class="btn normal" bindtap="debounce('SAVE')">暂存</view>
      <view class="btn primry" bindtap="debounce('SUBMIT')">
        提报完成
      </view>
    </view>
  </view>
</template>
<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import {
    HandleMethod,
    AccidentType,
    AccidentJudge
  } from 'shared/utils/constant';
  import { AccidentDetial } from 'shared/api/accidentDetail';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  const accidentApi = new AccidentDetial();
  createPage({
    data: {
      vehicleName: '',
      messageId: '',
      accidentNo: '',
      handleMethod: null,
      compensated: null,
      amount: null,
      accidentType: null,
      accidentJudge: null,
      isReportVehicleNet: null,
      reason: '',
      attachmentList: [],
      defaultAttachmentList: [],
      HandleMethodEnum: HandleMethod,
      AccidentType: AccidentType,
      AccidentJudge: AccidentJudge,
      timeout: null,
    },
    methods: {
      handleCall(phone: string) {
        wx.makePhoneCall({
          phoneNumber: phone,
          success() {
            console.log('拨打电话成功');
          },
          fail() {
            console.log('拨打电话失败');
          }
        });
      },
      handleInput(e: any) {
        this.reason = e?.detail?.value;
      },
      changeIsReportVehicleNet(e: any) {
        this.isReportVehicleNet = e?.detail?.value;
      },
      handleChangeCompensated(e: any) {
        this.compensated = e?.detail?.value;
        this.amount = null;
      },
      handleChangeAmount(e: any) {
        let val = e?.detail?.value;
        const regex = /^\d*(\.\d{0,2})?$/;
        if (!regex.test(val)) {
          val = val.slice(0, -1);
        }

        if (val.split('.')[0].length > 7) {
          wx.showToast({
            icon: 'none',
            title: '最多输入7位正数'
          });
          val = val.slice(0, -1);
        }
        this.setData({
          amount: val
        });
      },
      handleChangeMethod(e: any) {
        this.handleMethod = e?.detail?.value;
        this.isReportVehicleNet = null;
      },
      handleChangeAccidentType(e: any) {
        this.accidentType = e?.detail?.value;
      },
      handleChangAccidentJudge(e: any) {
        this.accidentJudge = e?.detail?.value;
      },
      showToast(message: string) {
        wx.showToast({
          icon: 'none',
          title: message
        });
      },
      validateFormFields() {
        if (!this.handleMethod) {
          this.showToast('请选择处理方式');
          return false;
        } else if (this.compensated != false && this.compensated != true) {
          this.showToast('请填写赔偿金额');
          return false;
        } else if (this.amount == null && this.compensated === true) {
          this.showToast('请填写赔偿金额');
          return false;
        } else if (this.attachmentList.length == 0) {
          this.showToast('请上传凭证');
          return false;
        } else if (
          (this.handleMethod == 'POLICE_DETERMINATION_NO_INSURANCE' ||
            this.handleMethod == 'POLICE_DETERMINATION_WITH_INSURANCE') &&
          this.isReportVehicleNet != false &&
          this.isReportVehicleNet != true
        ) {
          this.showToast('请选择是否上报');
          return false;
        }
        return true;
      },
      debounce(type: 'SAVE' | 'SUBMIT') {
        if (this.timeout) {
          clearTimeout(this.timeout);
        }
        this.timeout = setTimeout(() => {
          this.accidentOperate(type);
        }, 1000);
      },
      accidentOperate(type: 'SAVE' | 'SUBMIT') {
        if (type === 'SUBMIT') {
          const validateResult = this.validateFormFields();
          if (!validateResult) {
            return;
          }
        }
        accidentApi
          .accidentGenerate({
            messageId: this.messageId,
            accidentNo: this.accidentNo,
            operateType: type,
            compensated: this.compensated,
            amount: this.amount,
            accidentType: this.accidentType,
            accidentJudge: this.accidentJudge,
            reason: this.reason,
            handleMethod: this.handleMethod,
            attachmentList: this.attachmentList,
            isReportVehicleNet: this.isReportVehicleNet
          })
          .then((res: any) => {
            if (res.code === HTTPSTATUSCODE.Success) {
              wx.showToast({
                icon: 'none',
                title: type == 'SAVE' ? '暂存成功' : '提交成功'
              });
              setTimeout(() => {
                wx.navigateBack({
                  delta: 2
                });
              }, 1500);
            }
          });
      },
      saveFiles(e: any) {
        this.setData({
          attachmentList: e?.detail?.fileKeys?.map((i: any) => ({
            type: i.type,
            fileKey: i.fileKey
          }))
        });
      },
      getAccidentReportDetail(accidentNo: string) {
        accidentApi.getAccidentReportDetail(accidentNo).then((res: any) => {
          if (res.code == HTTPSTATUSCODE.Success) {
            const {
              compensated,
              amount,
              accidentType,
              accidentJudge,
              isReportVehicleNet,
              reason,
              attachmentList,
              handleMethod
            } = res?.data || {};
            this.setData({
              amount,
              accidentType,
              accidentJudge,
              isReportVehicleNet,
              reason,
              attachmentList,
              handleMethod,
              compensated,
              defaultAttachmentList: attachmentList?.map((item: any) => ({
                url: item.url,
                type: item.type,
                name: item.fileKey,
                fileKey: item.fileKey
              }))
            });
          }
        });
      }
    },
    onLoad(options: {
      vehicleName: string;
      accidentNo: string;
      messageId: string;
    }) {
      this.vehicleName = options.vehicleName;
      this.accidentNo = options.accidentNo;
      this.messageId = options.messageId;
      this.getAccidentReportDetail(options.accidentNo);
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>
<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    flex-flow: column;
  }
  .main-container {
    height: calc(100vh - 200px);
    overflow-y: auto;
    display: flex;
    flex-flow: column;
  }
  .title-box {
    margin: 16px 16px 8px;
    flex: 0 0 38px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    text-align: center;
    line-height: 38px;
  }
  .phone-number {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(128, 128, 128, 1);
    display: flex;
    align-items: center;
    view {
      flex: 1;
    }
    image {
      width: 10px;
      height: 10px;
    }
  }
  .form-group {
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    margin: 0 16px 8px;
    padding: 12px;
    .t-icon-check-circle-filled:before {
      color: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
    }
    .field-item {
      display: flex;
      &.flex-columns {
        flex-flow: column;
      }
    }
    .form-label {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
      margin-right: 6px;
      flex: 0 0 98px;
      white-space: nowrap;
      .required {
        color: #fa2c19;
      }
    }
    .t-radio--block {
      padding: 0 16px 8px;
    }
    .t-radio__border {
      display: none;
    }
    .t-radio__title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
    }
  }
  .form-options {
    position: relative;
    &.inline {
      .t-radio-group {
        display: flex;
        flex-flow: row;
        flex-wrap: wrap;
        justify-content: space-between;
      }
      .t-radio {
        margin-right: 24px;
      }
    }
  }
  .input-wrapper {
    display: flex;
    align-items: center;
    position: absolute;
    left: 100px;
    top: 45px;
    bottom: 20px;
    input {
      height: 24px;
      background: rgba(245, 245, 245, 1);
      border-radius: 2px;
      width: 66px;
      font-size: 14px;
      font-family: JDZhengHT;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
      text-indent: 4px;
      margin-right: 4px;
    }
  }
  .tips {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(128, 128, 128, 1);
    .required {
      color: rgba(250, 44, 25, 1);
    }
    .tip-message {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(150, 150, 150, 1);
      margin-left: 8px;
    }
  }
  .textarea {
    width: 96%;
    background-color: #f6f6f6 !important;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;
    color: #666;
    height: 104px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(128, 128, 128, 1);
    margin-top: 16px;
    margin-bottom: 8px;
  }

  .counter {
    text-align: right;
    font-size: 14px;
    color: #999;
    margin-top: 4px;
  }
  .footer {
    height: 90px;
    background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    padding-bottom: 30px;
    .btn {
      width: 80%;
      height: 40px;
      border-radius: 40px;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      line-height: 40px;
    }
    .normal {
      width: 106px;
      height: 40px;
      border: 1px solid rgba(204, 204, 204, 1);
      border-radius: 30px;
      background: #fff;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
      margin-right: 16px;
    }
  }
  .t-upload {
    width: 100% !important;
  }
  .accident-judge {
    &.inline {
      .t-radio {
        margin-right: 15px;
        &:last-child {
          margin-top: 4px;
        }
        &:first-child {
          margin-right: 0;
        }
      }
    }
  }
  .field-method,
  .field-compensated {
    .form-label {
      flex: 0 0 84px;
    }
  }
  .t-textarea__placeholder {
    font-size: 14px !important;
    font-family: PingFang SC !important;
    font-weight: normal !important;
    color: rgba(128, 128, 128, 1) !important;
  }
  .t-textarea {
    width: 100%;
    height: 104px;
    background-color: #f6f6f6 !important;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(128, 128, 128, 1);
    margin-top: 8px;
    margin-bottom: 8px;
    padding: 4px !important;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "shared/ui/naviBar.mpx",
      "upload-image": "shared/ui/uploadImage.mpx",
      "t-radio": "tdesign-miniprogram/radio/radio",
      "t-radio-group": "tdesign-miniprogram/radio-group/radio-group",
      "t-divider": "tdesign-miniprogram/divider/divider",
      "t-textarea": "tdesign-miniprogram/textarea/textarea",
      "preview-media": "shared/ui/previewMedia.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
