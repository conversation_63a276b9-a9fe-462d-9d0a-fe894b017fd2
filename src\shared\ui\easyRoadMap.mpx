<template>
  <view class="easy-roadmap">
    <view class="process-line">
      <view class="line-container" wx:for="{{stopItemList}}" wx:key="id">
        <view
          class="line"
          wx:if="{{index > 0}}"
          style="width: {{winW/(stopItemList.length-1)-20}}px"
        >
          <view
            class="driven-line"
            wx:if="{{item.percent > 0}}"
            style="width: {{(winW/(stopItemList.length-1)-12)*item.percent}}px"
          />
        </view>

        <view class="circle-icon" wx:class="{{ item.travelStatus }}" />
      </view>
    </view>

    <view class="drive-info">
      <text
        wx:if="{{driveInfo.time}}"
        wx:class="{{{isOverTime: isOverTime}}}"
        >{{ driveInfo.time }}</text
      >
      <text wx:if="{{driveInfo.time}}" class="divider">|</text>
      <text wx:if="{{driveInfo.name}}">{{ driveInfo.name }}</text>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl } from 'shared/assets/imageUrl';
  import { ScheduleState, StopTravelStatus } from 'shared/utils/constant';
  import { formatTimestamp, isEmpty } from 'shared/utils/utils';
  interface Data {
    stayingStop: any;
    isOverTime: Boolean;
    timeInterval: any;
    driveInfo: any;
    winW: number;
  }
  createComponent<Data>({
    properties: {
      scheduleState: String,
      stopList: Object
    },
    data: {
      stayingStop: null,
      isOverTime: false,
      timeInterval: null,
      driveInfo: {},
      winW: wx.getSystemInfoSync().windowWidth - 70
    },
    computed: {
      stopItemList() {
        if (!isEmpty(this.stopList)) {
          return this.stopList;
        } else {
          return [
            {
              id: Math.random(),
              travelStatus: 'INIT',
              percent: 0
            },
            {
              id: Math.random(),
              travelStatus: 'INIT',
              percent: 0
            }
          ];
        }
      }
    },
    methods: {
      formatDriveInfo() {
        if (isEmpty(this.stopList)) {
          this.driveInfo = {
            time: null,
            name: '暂时没有待完成任务～'
          };
          return;
        }
        const isReturnOrSetout = [
          ScheduleState.SETOUT,
          ScheduleState.RETURN
        ].includes(this.scheduleState as ScheduleState);
        this.stayingStop = this.stopList.find(
          (item: any) => item.travelStatus === StopTravelStatus.STAY
        );
        const stayingStopIndex = this.stopList.findIndex(
          (item: any) => item.travelStatus === StopTravelStatus.STAY
        );
        if (isReturnOrSetout) {
          this.formatReturnOrSetout();
        } else if (
          this.stayingStop &&
          stayingStopIndex > 0 &&
          stayingStopIndex < this.stopList.length - 1
        ) {
          this.formatStaying();
        } else {
          this.driveInfo = {
            time: null,
            name: null
          };
        }
      },
      formatReturnOrSetout() {
        const currentStop = this.stopList.find((item: any) => {
          return item.travelStatus === StopTravelStatus.START;
        });
        const totalTime =
          new Date().getTime() - new Date(currentStop?.startTime).getTime();
        this.driveInfo = {
          time: '已行驶' + formatTimestamp(totalTime, 'hh:mm:ss') || '--',
          name: currentStop?.name || '--'
        };
      },
      formatStaying() {
        const { waitingTime, arrivedTime, estDepartTime } = this.stayingStop;
        let overTimeCount = 0;
        let duration;
        let isOverTime = false;
        const arrivedTimeStamp = arrivedTime
          ? new Date(arrivedTime).getTime()
          : 0;
        let timer =
          waitingTime && arrivedTimeStamp
            ? waitingTime * 60000 - (new Date().getTime() - arrivedTimeStamp)
            : 0;
        if (timer < 0) {
          overTimeCount = Math.abs(timer);
        }
        if (timer >= 1000) {
          timer = timer - 1000;
          duration = formatTimestamp(timer, 'hh:mm:ss');
        } else {
          overTimeCount =
            waitingTime && arrivedTimeStamp
              ? new Date().getTime() - arrivedTimeStamp - waitingTime * 60000
              : arrivedTimeStamp
              ? new Date().getTime() - arrivedTimeStamp
              : overTimeCount + 1000;
          duration = formatTimestamp(overTimeCount, 'hh:mm:ss');
          this.isOverTime = true;
        }
        this.driveInfo = {
          time: estDepartTime
            ? estDepartTime + '返程'
            : this.isOverTime
            ? '超时' + duration
            : duration,
          name: this.stayingStop.name
        };
      }
    },
    lifetimes: {
      created: function () {
        this.formatDriveInfo();
        this.timeInterval = setInterval(this.formatDriveInfo.bind(this), 1000);
      },
      detached: function () {
        this.timeInterval && clearInterval(this.timeInterval);
      }
    }
  });
</script>

<style lang="scss">
  .easy-roadmap {
    height: 54px;
    background: rgba(171, 175, 204, 0.1);
    border-radius: 4px;
    padding: 16px 8px 0px 8px;
    .circle-icon {
      display: inline-block;
      width: 10px;
      height: 10px;
      background: rgba(255, 255, 255, 1);
      border: 2px solid rgba(250, 44, 25, 1);
      border-radius: 50%;
      &.INIT {
        border: 2px solid rgba(224, 224, 224, 1);
      }

      &.START {
        border: 2px solid rgba(224, 224, 224, 1);
      }
    }

    .process-line {
      display: flex;
      align-items: center;
      .line-container {
        display: flex;
        align-items: center;
        .line {
          height: 4px;
          background: rgba(224, 224, 224, 1);
          .driven-line {
            width: 140px;
            height: 4px;
            background: linear-gradient(
              270deg,
              rgba(253, 87, 54, 1) 0%,
              rgba(250, 44, 25, 1) 100%
            );
          }
        }
      }
    }

    .drive-info {
      width: 100%;
      text-align: center;
      margin-top: 12px;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
      .isOverTime {
        color: rgba(250, 44, 25, 1);
      }
      .divider {
        margin-left: 3px;
        margin-right: 3px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "alarmEvent": "shared/ui/alarmEvent.mpx"
    }
  }
</script>
