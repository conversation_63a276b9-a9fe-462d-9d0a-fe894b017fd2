<template>
  <web-view
    id="webview"
    wx:if="{{webviewUrl}}"
    src="{{webviewUrl}}"
    bindmessage="onWebViewMessage"
  ></web-view>
</template>

<style lang="scss">
  .container {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>

<script>
  import { createPage } from '@mpxjs/core';
  import { getWebviewHost, getLOPdomain, getLOPDN } from 'shared/utils/config';
  import { doRequest } from 'shared/api/fetch';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { getAppCode } from 'shared/utils/config';

  createPage({
    data() {
      return {
        webviewUrl: ''
      };
    },
    methods: {
      onWebViewMessage(event) {
        const message = event.detail.data;
        console.log('event', event);
        console.log(message);
        getApp().globalData.anyDriveInfo = message[0];
      }
    },
    onLoad(query) {
      const plugin = requirePlugin('loginPlugin');
      const k = plugin.getPtKey();
      const name = wx.getStorageSync('userName');
      this.webviewUrl = `${getWebviewHost()}/routeTask?k=${k}&userName=${name}`;
      console.log(`${getWebviewHost()}/routeTask?k=${k}&userName=${name}`);
    }
  });
</script>

<script name="json">
  module.exports = {
    navigationBarTitleText: '线路勘查',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black'
  };
</script>
