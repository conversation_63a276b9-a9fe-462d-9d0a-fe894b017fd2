
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="开关重启"
  ></navi-bar>

  <vehicle-list
    selectedVehicleName="{{selectedVehicleName}}"
    bind:handleSelect="handleSelect"
  />

  <restart-operate
    wx:if="{{visible}}"
    vehicleName="{{selectedVehicleName}}"
    visible="{{visible}}"
    bind:onVisibleChange="onVisibleChange"
  />
</template>

<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  import { CheckType } from 'shared/utils/checkVehicle';
  interface Data {
    CheckType: any;
    selectedVehicleName: any;
    visible: any;
  }
  createPage<Data>({
    data: {
      CheckType: CheckType,
      selectedVehicleName: null,
      visible: false
    },
    methods: {
      handleSelect(e: any) {
        this.selectedVehicleName = e.detail.val;
        if (e.detail.val) {
          this.visible = true;
        }
      },
      onVisibleChange() {
        this.visible = false;
        this.selectedVehicleName = null;
      }
    },
    onLoad: function (options) {
      if (options.vehicleName) {
        this.selectedVehicleName = options.vehicleName;
        this.visible = true;
      }
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
  }
  .popup-container {
    height: 170px;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx",
      "restart-operate": "widgets/commandOperate/restartOperate.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
