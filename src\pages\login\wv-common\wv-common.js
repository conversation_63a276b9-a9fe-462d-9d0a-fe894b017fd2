/**
 *
 * WARNING: 请谨慎修改，若导致登录业务不可用，业务自行承担风险
 *
 */
import util from '../util.js';
const plugin = requirePlugin('loginPlugin');

Page({
  onLoad: function (options = {}) {
    // h5_url 是 encodeURIComponent 处理后的链接
    // noLogin 是否需要进行登录态打通，默认不传为0，即：需要进行登录态打通
    const { h5_url = '', noLogin = 0 } = options;
    // 存储链接方便进行分享操作
    this.setData({ h5_url, noLogin });
    // 登录相关参数存储
    util.h5Init(options);
    // 设置自定义导航栏
    util.setCustomNavigation();
    // 判断是否需要进行登录态打通处理
    if (noLogin == 1) {
      // 单纯作为 webview 组件使用，不进行登录态打通操作
      this.setData({ url: decodeURIComponent(h5_url) });
    } else {
      // 进行登录态打通后，再打开 webview H5 页面
      this._genToken();
    }
  },
  _genToken() {
    const { h5_url } = this.data;
    // 插件获取登录态 Token，未登录情况下，将会直接生成空登录态 Token
    plugin
      .genToken({
        h5_url,
      })
      .then(res => {
        let { isSuccess, err_code, url, tokenkey, err_msg } = res;
        // 获取登录态成功
        if (isSuccess && !err_code) {
          this.setData({ url: `${url}?to=${h5_url}&tokenkey=${tokenkey}` });
        } else if (err_code == 268) {
          // 当前原生小程序登录态已失效，需要清除无效登录态
          //   此步骤不可省略，必须先清除本地登录态缓存
          plugin.removeLoginStorage(true);

          // TODO: 业务可以自行选择是否需要提示，也可直接静默清除登录态后，直接打开 H5 页面，不进行打通登录态处理。
          wx.showModal({
            title: '提示',
            content: err_msg || '登录已失效，请重新登录',
            success: res => {
              // 点击确认
              if (res.confirm) {
                /**
                 * TODO: 业务可以按需处理跳转页面，
                 *   如果业务需要更改，请自行充分验证，防止循环跳转。
                 */
                util.redirectPage(
                  `/pages/login/index/index?webview=1&returnPage=${h5_url}&pageType=h5`
                );
              } else {
                // 点击取消
                // TODO: 业务可以按需处理跳转页面，请自行充分验证，防止循环跳转。
              }
            },
          });
        } else {
          // 打通登录态失败
          wx.showModal({
            title: '提示',
            content: err_msg || '页面跳转失败，请重试',
            success: res => {
              if (res.confirm) {
                this._genToken();
              }
            },
          });
        }
      })
      .catch(res => console.jdLoginLog(res));
  },
  onShareAppMessage: function () {
    const { h5_url, noLogin = 0 } = this.data;
    return {
      title: '京东',
      path: `/pages/login/wv-common/wv-common?h5_url=${h5_url}&noLogin=${noLogin}`,
    };
  },
});
