import util from '../../pages/login/util.js';

let privacyHandler;
let privacyResolves = new Set();
const config = util.getLoginConfig();
let closeOtherPagePopUpHooks = new Set();

if (wx.onNeedPrivacyAuthorization) {
  wx.onNeedPrivacyAuthorization(resolve => {
    if (typeof privacyHandler === 'function') {
      privacyHandler(resolve);
    }
  });
}

const closeOtherPagePopUp = closePopUp => {
  closeOtherPagePopUpHooks.forEach(hook => {
    if (closePopUp !== hook) {
      hook();
    }
  });
};

Component({
  data: {
    visible: false,
    logo: config.appIcon,
    appName: config.appName,
    desc1: '用户隐私保护指引提示',
    urlTitle: '用户隐私保护指引',
    title: `${config.appName} 申请`,
    desc2: '点击“同意”按钮代表你已阅读并同意前述协议',
  },
  lifetimes: {
    attached() {
      const closePopUp = () => this.disPopUp();
      privacyHandler = resolve => {
        privacyResolves.add(resolve);
        this.popUp();
        closeOtherPagePopUp(closePopUp);
      };
      closeOtherPagePopUpHooks.add(closePopUp);
      this.closePopUp = closePopUp;
    },
    detached() {
      closeOtherPagePopUpHooks.delete(this.closePopUp);
    },
  },
  methods: {
    handleAgree(e) {
      this.disPopUp();
      privacyResolves.forEach(resolve =>
        resolve({ event: 'agree', buttonId: 'agree-btn' })
      );
      privacyResolves.clear();
    },
    handleDisagree(e) {
      this.disPopUp();
      privacyResolves.forEach(resolve => resolve({ event: 'disagree' }));
      privacyResolves.clear();
    },
    popUp() {
      const { visible } = this.data;
      !visible && this.setData({ visible: true });
    },
    disPopUp() {
      const { visible } = this.data;
      visible && this.setData({ visible: false });
    },
    openPrivacyContract() {
      wx.openPrivacyContract({
        fail: console.warn,
      });
    },
  },
});
