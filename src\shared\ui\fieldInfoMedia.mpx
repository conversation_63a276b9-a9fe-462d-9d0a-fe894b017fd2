<template>
   <view class="info-item">
    <text class="label">{{ labelName }}</text>
    <preview-media-hori mediaList="{{value || []}}" style="width: 100%;"/>
  </view>
</template>
<script>
  import { createComponent } from '@mpxjs/core'
  import { Notification } from 'shared/assets/imageUrl';
  createComponent({
     data: {
      Notification: Notification
    },
    properties: {
      labelName: {
        type: String,
        value: ''
      },
      value: {
        type: Array,
        value: []
      },
      status: {
        type: String,
        value: ''
      },
      statusText: {
        type: String,
        value: ''
      }
    },
    onShow() {
      console.log('filedInfoMedia=' + this.mediaList);
    },
    methods: {
    }
  })
</script>
<style lang="scss" scoped>
.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    .label {
      flex: 0 0 80px;
      color: #999;
      font-size: 14px;
    }
}
</style>
<script name="json">
  module.exports = {
    usingComponents:{
      "preview-media-hori": "./previewMediaHori.mpx"
    },
    component: true
  };
</script>