<template>
  <view class="vehicle-item-wrapper" bindtap="debounce">
    <view class="vehicle-info">
      <view
        wx:class="{{{ 'vehicle-name-normal': itemInfo.systemStatus === SystemStatus.NORMAL, 'vehicle-name-abnormal': itemInfo.systemStatus === SystemStatus.ABNORMAL, 'vehicle-name-offline': itemInfo.systemStatus === SystemStatus.OFFLINE}}}"
        >{{ itemInfo.vehicleName }}</view
      >
      <view class="business-status">{{
        itemInfo.businessStatus ? '有任务' : '无任务'
      }}</view>
      <view class="station-name">{{ itemInfo.stationName }}</view>
    </view>
    <common-checkbox
      wx:if="{{itemInfo.systemStatus !== SystemStatus.OFFLINE}}"
      checked="{{itemInfo.checked}}"
      bindonchange="debounce"
    ></common-checkbox>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, FreeDriveUrl, VehicleUrl } from 'shared/assets/imageUrl';
  import { SystemStatus } from 'shared/utils/constant';
  interface Data {
    WorkBenchUrl: any;
    FreeDriveUrl: any;
    VehicleUrl: any;
    SystemStatus: any;
    fetchApi: any;
    searchMode: any;
    vehicleList: any[];
  }
  createComponent<any>({
    properties: {
      itemInfo: {
        type: Object,
        value: {}
      }
    },
    data: {
      WorkBenchUrl,
      FreeDriveUrl,
      VehicleUrl,
      SystemStatus,
      timeout: null
    },
    methods: {
      debounce() {
        // 清除之前的定时器
        if (this.timeout) {
          clearTimeout(this.timeout);
        }
        // 设置新的定时器
        this.timeout = setTimeout(() => {
          this.handleSelectItem();
        }, 500); // 500毫秒的防抖时间
      },
      handleSelectItem() {
        const that = this;
        console.log('debounced');
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        that.triggerEvent('handleClick', {
          val: that.itemInfo.vehicleName
        });
        wx.hideLoading();
      }
    }
  });
</script>

<style lang="scss" scoped>
  .vehicle-item-wrapper {
    height: 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 17px 0px;
    margin: 0px 16px;
    border-bottom: 1px solid rgba(134, 141, 159, 0.1);
    white-space: nowrap;
    .vehicle-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .vehicle-name-normal,
      .vehicle-name-abnormal,
      .vehicle-name-offline {
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 500;
        margin-right: 12px;
      }
      .vehicle-name-normal {
        color: rgba(18, 179, 93, 1);
      }
      .vehicle-name-abnormal {
        color: rgba(250, 44, 25, 1);
      }
      .vehicle-name-offline {
        color: rgba(137, 147, 175, 1);
      }
      .business-status {
        width: 44px;
        height: 20px;
        line-height: 20px;
        background: rgba(134, 141, 159, 0.1);
        border-radius: 4px;
        font-size: 12px;
        text-align: center;
        color: rgba(134, 141, 159, 1);
        margin-right: 12px;
      }
      .station-name {
        font-size: 14px;
        color: rgba(100, 100, 100, 1);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "common-checkbox": "shared/ui/commonCheckbox.mpx"
    }
  }
</script>
