<template>
  <view class="drivable-direction-container" wx:if="{{info}}">
    <view class="left">
      <t-image
        wx:if="{{info.enableLeft}}"
        src="{{LeftNormal}}"
        mode="aspectFill"
        width="20"
        height="40"
      />
      <t-image
        wx:else
        src="{{LeftObstacles}}"
        mode="aspectFill"
        width="20"
        height="40"
      />
    </view>
    <view class="middle">
      <t-image
        t-class="img-top"
        wx:if="{{info.enableFront}}"
        src="{{TopNormal}}"
        mode="aspectFill"
        width="40"
        height="20"
      />
      <t-image
        t-class="img-top"
        wx:else
        src="{{TopObstacles}}"
        mode="aspectFill"
        width="40"
        height="20"
      />
      <t-image
        src="{{SingleVehicle.drivableCenter}}"
        mode="aspectFill"
        width="78"
        height="78"
      />
      <t-image
        t-class="img-bottom"
        wx:if="{{info.enableBack}}"
        src="{{BottomNormal}}"
        mode="aspectFill"
        width="40"
        height="20"
      />
      <t-image
        t-class="img-bottom"
        wx:else
        src="{{BottomObstacles}}"
        mode="aspectFill"
        width="40"
        height="20"
      />
    </view>
    <view class="right">
      <t-image
        wx:if="{{info.enableRight}}"
        src="{{RightNormal}}"
        mode="aspectFill"
        width="20"
        height="40" />
      <t-image
        wx:else
        src="{{RightObstacles}}"
        mode="aspectFill"
        width="20"
        height="40"
    /></view>
  </view>
</template>


<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { SingleVehicle } from 'shared/assets/imageUrl';

  createComponent({
    properties: {
      info: {
        type: Object
      }
    },
    data: {
      SingleVehicle: SingleVehicle,
      LeftNormal: require('shared/assets/obstaclesImg/left-normal.png'),
      LeftObstacles: require('shared/assets/obstaclesImg/left-obstacles.png'),
      TopNormal: require('shared/assets/obstaclesImg/top-normal.png'),
      TopObstacles: require('shared/assets/obstaclesImg/top-obstacles.png'),
      RightNormal: require('shared/assets/obstaclesImg/right-normal.png'),
      RightObstacles: require('shared/assets/obstaclesImg/right-obstacles.png'),
      BottomNormal: require('shared/assets/obstaclesImg/bottom-normal.png'),
      BottomObstacles: require('shared/assets/obstaclesImg/bottom-obstacles.png')
    }
  });
</script>

<style lang="scss" scoped>
  .drivable-direction-container {
    display: flex;
    align-items: center;
    widows: 90px;
    height: 90px;
    .left {
      position: relative;
      left: 12px;
    }
    .middle {
      .img-top {
        position: relative;
        left: 19px;
        top: 12px;
      }
      .img-bottom {
        position: relative;
        left: 19px;
        bottom: 12px;
      }
    }
    .right {
      position: relative;
      right: 12px;
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image"
    }
  }
</script>