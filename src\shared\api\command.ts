import { doRequest } from 'shared/api/fetch';

export class CommandApi {
  sendCommand = ({
    vehicleName,
    commandType,
    operateTime
  }: {
    vehicleName: string;
    commandType: string;
    operateTime: string;
  }) => {
    const requestOptions = {
      method: 'POST',
      url: '/mobile/applet/command/send',
      data: {
        vehicleName,
        commandType,
        operateTime
      }
    };
    return doRequest(requestOptions);
  };
}
