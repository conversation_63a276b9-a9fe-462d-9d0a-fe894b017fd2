
<template>
  <t-popup
    visible="{{visible}}"
    placement="bottom"
    bind:visible-change="cancel"
  >
    <view
      class="popup-context"
      wx:style="{{{height: 52*(userList.length+1)+'px'}}}"
    >
      <view class="title">
        <text>呼叫一线</text>
        <image src="{{RemoteControlUrl.ClosePopup}}" bindtap="cancel"></image>
      </view>
      <view class="call-front" wx:class="{{{'call-front-container':true}}}">
        <view
          class="user"
          wx:for="{{userList}}"
          wx:key="userName"
          bindtap="handleClick(item)"
        >
          <view style="display: flex">
            <text class="realName">{{ item.realName }}</text>
            <text class="userName">{{ item.erp }}</text></view
          >
          <view class="station-leader-icon" wx:if="{{index === 0}}"
            >站点负责人</view
          >
        </view>
      </view>
    </view>
  </t-popup>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { RemoteControlUrl } from 'shared/assets/imageUrl';
  import { CallFrontApi } from 'shared/api/callFront';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createComponent<any>({
    properties: {
      vehicleName: {
        type: String,
        value: ''
      },
      visible: {
        type: Boolean,
        value: false
      }
    },
    externalClasses: ['call-front'],
    data: {
      fetchApi: new CallFrontApi(),
      RemoteControlUrl: RemoteControlUrl,
      userList: [],
      selectUser: {}
    },
    watch: {
      vehicleName: {
        handler(val, old) {
          if (val && val !== old) {
            this.getFrontUserList();
          }
        },
        immediate: true
      }
    },
    methods: {
      async getFrontUserList() {
        const res = await this.fetchApi.getFrontUserList(this.vehicleName);
        if (res.code === HTTPSTATUSCODE.Success) {
          this.userList = res.data;
        }
      },
      cancel() {
        this.triggerEvent('onVisibleChange');
      },
      handleClick(item: any) {
        this.setData({
          selectUser: item,
          showConfirm: true,
          visible: false
        });

        this.confirmDialog();
      },
      async confirmDialog() {
        const res = await this.fetchApi.getErpPhone(this.vehicleName);
        if (res.code === HTTPSTATUSCODE.Success) {
          wx.makePhoneCall({
            phoneNumber: res.data.phone,
            success: () => {
              this.triggerEvent('onVisibleChange');
            },
            fail: () => {
              this.triggerEvent('onVisibleChange');
            }
          });
        }
      }
    }
  });
</script>

<style lang="scss" scoped>
  .popup-context {
    padding-bottom: 38px;
  }
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 16px;
    padding-right: 16px;
    height: 52px;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(26, 26, 26, 1);
    border-bottom: 1px solid rgba(236, 236, 236, 1);
    image {
      width: 14px;
      height: 14px;
    }
  }
  .call-front-container {
    padding-right: 16px;
    padding-left: 16px;
    padding-bottom: 72px;
    z-index: 11501;
    .user {
      display: flex;
      justify-content: space-between;
      padding-top: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #f0f0f0;

      .realName {
        border-right: 1px solid rgba(229, 229, 229, 1);
        padding-right: 8px;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(0, 0, 0, 1);
      }
      .userName {
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(0, 0, 0, 1);
        margin-left: 8px;
      }

      .station-leader-icon {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(255, 255, 255, 1);
        width: 78px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        background: linear-gradient(
          135deg,
          rgba(255, 191, 0, 1) 0%,
          rgba(255, 191, 0, 1) 47.14022121773389%,
          rgba(250, 187, 0, 1) 100%
        );
        border-radius: 20px;
      }
    }
  }
  .dialog {
    height: 168px;
    .content {
      height: 30px;
      width: 100%;
      text-align: center;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(53, 53, 53, 1);
    }
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "t-dialog": "tdesign-miniprogram/dialog/dialog",
      "t-popup": "tdesign-miniprogram/popup/popup",
      "t-input": "tdesign-miniprogram/input/input"
    }
  }
</script>
