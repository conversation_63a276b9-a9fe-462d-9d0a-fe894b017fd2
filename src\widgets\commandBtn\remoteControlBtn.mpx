
<template>
  <view
    class="remote-control-btn"
    wx:class="{{styleInfo.otherClassName}}"
    bindtap="getVehicleInfo"
  >
    <t-image
      src="{{styleInfo.imageSrc}}"
      mode="aspectFill"
      width="{{styleInfo.imageWidth}}"
      height="{{styleInfo.imageHeight}}"
    />
    <text class="label" wx:class="{{styleInfo.otherClassName}}">车辆遥控</text>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { CommonApi } from 'shared/api/common';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { WorkBenchUrl, SingleVehicle } from 'shared/assets/imageUrl';
  import {
    SystemStatus,
    TakeOverSourceType,
    TakeOverType
  } from '../../shared/utils/constant';
  createComponent({
    properties: {
      // 'workBench'  ||  'singelVehicle'
      sourceType: {
        type: String,
        value: ''
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      WorkBenchUrl: WorkBenchUrl,
      SingleVehicle: SingleVehicle,
      styleInfo: {},
      fetchApi: new CommonApi()
    },
    methods: {
      async getVehicleInfo() {
        try {
          if (this.vehicleName) {
            const res: any = await this.fetchApi.getVehicleInfo(this.vehicleName);
            if (res.code === HTTPSTATUSCODE.Success) {
              if (
                !res.data.systemStatus ||
                res.data.systemStatus === SystemStatus.OFFLINE
              ) {
                wx.showToast({
                  title: '车辆离线不能遥控！',
                  icon: 'none',
                  duration: 3000
                });
              } else {
                if (res.data.takeoverStatus === TakeOverType.TAKEOVER) {
                  if (
                    res.data.takeoverSource !== TakeOverSourceType.MINI_MONITOR
                  ) {
                    wx.showToast({
                      title: `${res.data.takeoverUserName}正在接管中！`,
                      icon: 'none'
                    });
                    return;
                  } else {
                    const userName = wx.getStorageSync('userName');
                    if (userName === res.data.takeoverUserName) {
                      getApp().globalData.controlVehicleName = this.vehicleName;
                      getApp().globalData.redirectUrl = `/pages/remoteControl/index?vehicleName=${this.vehicleName}`;
                      getApp().globalData.backUrl = '/pages/vehicle/index';
                      getApp().globalData.eventType = 'redirect';
                      wx.navigateTo({
                        url: `/pages/redirectPage/index`
                      });
                    } else {
                      wx.showToast({
                        title: `${res.data.takeoverUserName}正在接管中！`,
                        icon: 'none'
                      });
                      return;
                    }
                  }
                } else {
                  getApp().globalData.controlVehicleName = this.vehicleName;
                  getApp().globalData.redirectUrl = `/pages/remoteControl/index?vehicleName=${this.vehicleName}`;
                  getApp().globalData.backUrl = '/pages/vehicle/index';
                  getApp().globalData.eventType = 'redirect';
                  wx.navigateTo({
                    url: `/pages/redirectPage/index`
                  });
                }
              }
            }
          } else {
            getApp().globalData.redirectUrl = `/pages/remoteControl/index`;
            getApp().globalData.backUrl = '/pages/workbench/index';
            getApp().globalData.eventType = 'redirect';
            wx.navigateTo({
              url: `/pages/redirectPage/index`
            });
          }
        } catch (err) {
          console.error(err);
        }
      },
      formatStyleInfo() {
        if (this.sourceType === 'workBench') {
          this.styleInfo = {
            imageSrc: this.WorkBenchUrl.VehicleControl,
            imageWidth: '93px',
            imageHeight: '87px',
            otherClassName: 'workBench'
          };
        } else {
          this.styleInfo = {
            imageSrc: this.SingleVehicle.RemoteControl,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'singelVehicle'
          };
        }
      }
    },
    lifetimes: {
      created: function () {
        this.formatStyleInfo();
      }
    }
  });
</script>

<style lang="scss">
  .remote-control-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    &.workBench {
      width: 93px;
      height: 87px;
      position: relative;
    }
    .label {
      text-align: center;
      font-family: PingFang SC;
      font-weight: normal;
      &.workBench {
        font-size: 12px;
        position: absolute;
        bottom: 10px;
        color: rgba(255, 119, 0, 1);
      }
      &.singelVehicle {
        color: rgba(51, 51, 51, 1);
        font-size: 11px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image"
    }
  }
</script>
