<template>
  <view class="cabin-container">
    <navi-bar
      show-address="{{false}}"
      title="开舱门"
      showReturn="true"
    ></navi-bar>
    <view class="main-container">
      <view class="title">
        <text class="title-text">{{ vehicleName }}</text>
      </view>
      <view class="direct-desc">
        <text class="desc-title">左侧货箱</text>
        <text class="desc">点击格口开舱门</text>
        <text class="tips" bindtap="openSheet">如何分左右？</text>
      </view>
      <grid-list
        gridList="{{gridData?.leftGridList}}"
        boxColumnCount="{{gridData?.leftBoxColumnNum}}"
        caseSize="{{caseSize}}"
        selectGrid="{{selectGrid}}"
        bindopenGrid="openOneGrid"
      />
      <view class="direct-desc">
        <text class="desc-title">右侧货箱</text>
      </view>
      <grid-list
        gridList="{{gridData?.rightGridList}}"
        boxColumnCount="{{gridData?.rightBoxColumnNum}}"
        caseSize="{{caseSize}}"
        selectGrid="{{selectGrid}}"
        bindopenGrid="openOneGrid"
      />
    </view>
    <view class="footer">
      <view class="btn" bindtap="openAllGrid">一键全开</view>
    </view>
    <action-sheet
      visible="{{sheetVisible}}"
      placement="bottom"
      title="货箱方向示意"
      bindclose="handleClose"
    >
      <view style="color: #999999; margin-top: 8px; font-size: 14px">
        车辆前进方向，左手边为左侧货箱，右手边为右侧货箱
      </view>
      <image
        style="width: 277px; height: 277px; margin: 20px auto; display: block"
        src="https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/singleVehicle/tip-icon.png?Expires=3869380181&AccessKey=n828WHAXD584pTvi&Signature=K60mPuNGiiYFL5yAD0Q9xbnHZGo%3D"
      />
    </action-sheet>
  </view>

  <t-dialog
    visible="{{curOpenGrid ? true : false}}"
    confirm-btn="确认开门"
    cancel-btn="取消"
    bind:confirm="confirmDialog"
    bind:cancel="closeDialog"
  >
    <view wx:class="modal-info" slot="middle"
      >仅开舱门，<text wx:style="color: rgba(250,44,25,1)"
        >系统不自动完结业务单，</text
      >
      <text>{{ text }}</text>
    </view>
  </t-dialog>
</template>
<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import { OpenGridApi } from 'shared/api/openGrid';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createPage<any>({
    data: {
      fetchApi: new OpenGridApi(),
      vehicleName: '',
      sheetVisible: false,
      gridData: {},
      selectGrid: [],
      curOpenGrid: null,
      text: null
    },
    computed: {
      caseSize() {
        return {
          width: 340,
          height: 222
        };
      }
    },
    methods: {
      openSheet() {
        this.sheetVisible = true;
      },
      handleClose() {
        this.sheetVisible = false;
      },
      async getBoxGrid(vehicleName: any) {
        const res = await this.fetchApi.getBoxGrid(vehicleName);
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            gridData: res.data
          });
        }
      },
      openOneGrid(val: any) {
        this.setData({
          curOpenGrid: [val.detail.gridNo],
          text: '确认开舱门吗？'
        });
      },
      openAllGrid() {
        const grid: any[] = [];
        if (
          this.gridData.leftBoxColumnNum <= 0 &&
          this.gridData.rightBoxColumnNum <= 0
        ) {
          return;
        }
        if (this.gridData.rightGridList && this.gridData.leftGridList) {
          this.gridData.rightGridList.forEach((v: any) => {
            grid.push(v.gridNo);
          });
          this.gridData.leftGridList.forEach((v: any) => {
            grid.push(v.gridNo);
          });
        } else if (this.gridData.leftGridList) {
          this.gridData.leftGridList.forEach((v: any) => {
            grid.push(v.gridNo);
          });
        } else if (this.gridData.rightGridList) {
          this.gridData.rightGridList.forEach((v: any) => {
            grid.push(v.gridNo);
          });
        }
        this.setData({
          curOpenGrid: grid,
          text: '确认一键全开舱门吗'
        });
      },
      confirmDialog() {
        this.openGrid();
        this.closeDialog();
      },
      closeDialog() {
        this.setData({
          curOpenGrid: null
        });
      },
      async openGrid() {
        this.setData({
          selectGrid: this.selectGrid.concat(this.curOpenGrid)
        });

        const res = await this.fetchApi.openGrid({
          vehicleName: this.vehicleName,
          gridNoList: this.curOpenGrid
        });
        if (res.code === HTTPSTATUSCODE.Success) {
          wx.showToast({
            icon: 'none',
            title: '开门成功'
          });
        } else {
          wx.showToast({
            icon: 'none',
            title: res.message
          });
        }
      }
    },
    onLoad(query: { vehicleName: string }) {
      console.log('开舱门页面', getCurrentPages());
      this.vehicleName = query.vehicleName;
      this.getBoxGrid(query.vehicleName);
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>
<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-flow: column;
  }
  .main-container {
    height: calc(90vh - 87px);
    overflow-y: auto;
    display: flex;
    flex-flow: column;
    background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/bg.png?Expires=3869201385&AccessKey=n828WHAXD584pTvi&Signature=IeY7iPzJVE2qfoxnLviyEpwXSu8%3D')
      no-repeat center;
    background-size: cover;
  }
  .title {
    width: 96%;
    flex: 0 0 72px;
    margin: 10px 16px 14px 10px;
    background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/notification/title.png?Expires=3869202152&AccessKey=n828WHAXD584pTvi&Signature=XktxvW2tNsF1XZ0NskutNgvIjPo%3D')
      no-repeat center;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-family: JDZhengHT;
    font-weight: normal;
    color: rgba(255, 255, 255, 1);
    .title-text {
      padding-top: 10px;
    }
  }
  .direct-desc {
    margin: 0 16px 12px;
    display: flex;
    align-items: center;
    .desc-title {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: bold;
      color: rgba(51, 51, 51, 1);
    }
    .desc {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
      flex: 1;
      margin-left: 4px;
    }
    .tips {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(13, 133, 255, 1);
    }
  }
  .footer {
    height: 10vh;
    background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      width: 80%;
      height: 40px;
      border-radius: 40px;
      background: linear-gradient(
        90deg,
        rgba(242, 0, 0, 1) 0%,
        rgba(255, 79, 24, 1) 100%
      );
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      line-height: 40px;
    }
  }
  .t-dialog {
    .modal-info {
      padding-left: 20px;
      padding-right: 20px;
      text-align: left;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(53, 53, 53, 1);
    }
  }
  .t-icon-close {
    font-weight: bold!important;
    font-size: 24px!important;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "grid-list": "../../widgets/gridList.mpx",
      "action-sheet": "../../shared/ui/actionSheet.mpx",
      "t-dialog": "tdesign-miniprogram/dialog/dialog"
    },
    "navigationStyle": "custom"
  }
</script>
