<template>
  <view class="container">
    <view class="media-item" wx:for="{{list}}">
      <image
        wx-if="{{item.type === 'image'}}"
        src="{{item.url}}"
        class="media-image"
        bindtap="previewImage(item.url)"
      ></image>
      <view
        wx-if="{{item.type === 'video'}}"
        class="media-video"
        bindtap="previewVideo(item.url)"
      >
        <video src="{{item.url}}" class="video-thumbnail"></video>
      </view>
    </view>
  </view>
</template>

<script>
  import { createComponent } from '@mpxjs/core';

  createComponent({
    properties: {
      mediaList: {
        type: Array,
        value: []
      }
    },
    computed: {
      list() {
        return this.mediaList.map(item => ({
          ...item,
          url: item.url.replace('http://', 'https://')
        }));
      }
    },
    onShow() {
      console.log(this.mediaList);
    },
    methods: {
      previewImage(src) {
        wx.previewImage({
          urls: [src]
        });
      },
      previewVideo(src) {
        wx.previewMedia({
          sources: [
            {
              url: src, // 视频的 URL
              type: 'video' // 媒体类型，'video' 表示视频
            }
          ],
          success: function (res) {
            console.log('成功预览视频', res);
          },
          fail: function (err) {
            console.error('预览视频失败', err);
          }
        });
      }
    }
  });
</script>

<style lang="scss">
  .container {
    display: flex;
    gap: 8px;
    padding: 8px 8px 16px;
    width: 100%;
  }

  .media-item {
    position: relative;
    width: 22%;
    height: 75px;
    border-radius: 8px;
  }
  .media-video {
    width: 100%;
    height: 100%;
  }
  .media-image,
  .video-thumbnail {
    width: 100%;
    height: 100%;
    border-radius: 8px;
  }

  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    color: white;
  }
</style>

<script name="json">
  module.exports = {
    component: true
  };
</script>