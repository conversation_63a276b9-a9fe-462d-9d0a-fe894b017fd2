<template>
  <view class="delay-info-wrapper">
    <view class="title">当前指令延时(S)</view>
    <view class="value">
      {{ delayValue }}
      <view
        class="delay-icon"
        style="background: url({{RemoteControlUrl.DelayWarning}}) no-repeat center/100%"
      ></view>
    </view>
  </view>
</template>
<script lang='ts'>
  import { createComponent } from '@mpxjs/core';
  import { RemoteControlUrl } from 'shared/assets/imageUrl';
  createComponent({
    data: {
      RemoteControlUrl: RemoteControlUrl
    },
    properties: {
      delayInfo: {
        type: Object,
        value: {}
      }
    },
    computed: {
      delayValue() {
        if (this.delayInfo) {
          if (this.delayInfo.commandDelay) {
            return this.delayInfo.commandDelay;
          } else {
            return 0;
          }
        }
      }
    }
  });
</script>
<style lang="scss">
  .delay-info-wrapper {
    width: 107px;
    height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    margin-bottom: 12px;
    .title {
      color: rgba(51, 51, 51, 1);
    }
    .value {
      color: rgb(250, 44, 25);
      width: 70px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 14px;
      .delay-icon {
        width: 14px;
        height: 12px;
        margin-left: 4px;
      }
    }
  }
</style>
<script name="json">
  module.exports = {
    component: true
  };
</script>
