import {
  addGlobalEventListener,
  removeGlobalEventListener,
  emitter
} from 'shared/utils/emit';

interface Config {
  onOpen?: (data: any) => void;
  onError?: (data: any) => void;
  onClose?: (data: any) => void;
  onMessage?: (data: any) => void;
  initMessage: any;
}

export default class WebSocketClient {
  socket: any;
  config: Config;
  wsUrl: string;
  token: string | undefined;
  constructor(wsUrl: string, config: Config, token?: string) {
    this.wsUrl = wsUrl;
    this.token = token;
    this.config = config;
    this.initSocket();
  }

  initSocket() {
    this.socket = wx.connectSocket({
      url: this.wsUrl,
      header: {
        'content-type': 'application/json'
      },
      timeout: 60000,
      protocols: [this.token as string],
      success: (res: any) => {
        console.log(res);
      },
      fail: (res: any) => {
        console.log(res);
      }
    });
    if (this.socket) {
      this.socket.onOpen((data: any) => {
        this.socket.send({
          data: JSON.stringify(this.config.initMessage),
          fail: (err: any) => {
            console.error(err);
            console.error('消息发送失败');
          }
        });
        this.config.onOpen && this.config.onOpen(data);
      });
      this.socket.onError((err: any) => {
        this.config.onError && this.config.onError(err);
      });
      this.socket.onClose((data: any) => {
        this.config.onClose && this.config.onClose(data);
      });
      this.socket.onMessage((data: any) => {
        this.config.onMessage && this.config.onMessage(data);
      });
    }
  }

  reConnect() {
    this.socket = wx.connectSocket({
      url: this.wsUrl,
      protocols: [this.token as string]
    });
    this.initSocket();
  }

  sendMessage(data: any) {
    if (data && this.socket) {
      this.socket.send({
        data: JSON.stringify(data),
        fail: (err: any) => {
          console.error(err);
          console.error('消息发送失败');
        }
      });
    }
  }

  closeSocket() {
    if (this.socket) {
      this.socket.close({
        success: (res: any) => {
          console.log(res);
          console.log('关闭WebSocket连接');
        },
        fail: (err: any) => {
          console.error(err);
          console.error('关闭WebSocket连接失败');
        }
      });
    }
  }

  // 订阅
  subscribe(eventName: string, cb: AnyFunc) {
    addGlobalEventListener(eventName, cb);
  }

  // 取消订阅
  unsubscribe(eventName: string, cb: AnyFunc) {
    removeGlobalEventListener(eventName, cb);
  }

  // 销毁
  destroy() {
    this.socket.close();
    this.socket = null;
    emitter.all.clear();
  }
}
