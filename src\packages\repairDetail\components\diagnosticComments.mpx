<template>
  <view class="diagnostic-comments-container">
    <!-- 留言列表 -->

    <view class="comments-list">
      <view wx:for="{{commentsList}}" wx:key="id" class="comment-thread">
        <!-- 主留言 -->
        <message-card
          comment="{{item}}"
          isReply="{{false}}"
          bind:reply="handleReplyComment"
        />

        <!-- 回复留言 -->
        <view wx:if="{{item.replies && item.replies.length > 0}}">
          <message-card
            wx:for="{{item.replies}}"
            wx:for-item="reply"
            wx:key="id"
            comment="{{reply}}"
            isReply="{{true}}"
            bind:reply="handleReplyComment"
          />
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{commentsList.length === 0}}" class="empty-state">
        <text class="empty-text">暂无留言</text>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <comment-action-bar
      replyTo="{{replyToUser}}"
      bind:sendComment="handleSendComment"
      bind:cancelReply="handleCancelReply"
      bind:notification="handleNotification"
      bind:dashboard="handleDashboard"
    />
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  interface Comment {
    id: number;
    type: string;
    realName: string;
    content: string;
    createTime: string;
    attachment: any[];
    replies?: Comment[];
    replyToRealName?: string;
    replyToCommentId?: number;
  }

  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      },
      commentsList: {
        type: Array,
        value: []
      },
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      replyToUser: null as { commentId: number; userName: string } | null
    },
    methods: {
      handleReplyComment(event: any) {
        const { commentId, userName } = event.detail;
        // 设置回复状态
        this.setData({
          replyToUser: {
            commentId,
            userName
          }
        });
      },

      handleCancelReply() {
        // 取消回复状态
        this.setData({
          replyToUser: null
        });
      },

      async handleSendComment(event: any) {
        const { content, replyTo, successFiles } = event.detail;
        console.log('单号:', this.properties.orderNumber);
        console.log(
          '发送留言:',
          content,
          '回复用户:',
          replyTo,
          '上传文件:',
          successFiles
        );
        const formatSuccessFiles =
          successFiles?.map((file: any) => {
            const res = file.result || {};
            return {
              type: res.type === 'file' ? 'document' : file.type,
              fileKey: res.fileKey || '',
              bucketName: 'rover-operation'
            };
          }) || [];
        try {
          const res = await this.techMaintenanceApi.postComment({
            number: this.properties.orderNumber,
            replyToCommentId: replyTo ? replyTo.commentId : null,
            content: content || '',
            attachment: formatSuccessFiles
          });
          if (res.code === HTTPSTATUSCODE.Success) {
            wx.showToast({
              title: '留言发送成功',
              icon: 'success',
              duration: 2000
            });
          } else {
            wx.showToast({
              title: '留言发送失败',
              icon: 'none',
              duration: 2000
            });
          }
        } catch (error) {
          console.error('发送留言失败:', error);
          wx.showToast({
            title: '发送留言失败',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        // 清除回复状态
        this.setData({
          replyToUser: null
        });
      },

      handleNotification() {
        // 处理通知按钮点击
        wx.showToast({
          title: '通知功能开发中',
          icon: 'none',
          duration: 2000
        });
      },

      handleDashboard() {
        // 处理仪表设置按钮点击
        wx.showToast({
          title: '仪表设置功能开发中',
          icon: 'none',
          duration: 2000
        });
      }
    }
  });
</script>

<style lang="scss">
  .diagnostic-comments-container {
    height: 100%;
    width: 100%;
    box-sizing: border-box;

    .comments-list {
      width: 100%;
      padding-bottom: 16px;

      .comment-thread {
        margin-bottom: 8px;
      }

      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;

        .empty-text {
          font-size: 16px;
          color: #999999;
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "message-card": "./messageCard.mpx",
      "comment-action-bar": "./commentActionBar.mpx"
    }
  }
</script>