<template>
  <view class="insurance-info" wx:if="{{insuranceInfo.length > 0}}">
    <scroll-view
      scroll-y
      type="list"
      show-scrollbar="{{false}}"
      class="insurance-info-list"
    >
      <view
        wx:for="{{insuranceInfo}}"
        wx:key="vehicleInsuranceId"
        class="insurance-info-item"
      >
        <view class="info-item">
          <text class="info-label">保单号</text>
          <text class="info-value">{{ item.policyNumber }}</text>
          <view
            class="insurance-check-icon"
            style="background: url({{SingleVehicle.InsuranceCheckIcon}}) no-repeat center/100%; width: 12px; height: 12px; margin-left: 12px; margin-right: 4px;"
          ></view>
          <view
            class="check-text"
            bindtap="handleCheckInsurance"
            data-vehicle-insurance-id="{{item.vehicleInsuranceId}}"
          >
            查看
          </view>
        </view>
        <view class="info-item">
          <text class="info-label">投保地</text>
          <text class="info-value"
            >{{ item.provinceName }}-{{ item.cityName }}-{{
              item.stationName
            }}</text
          >
        </view>
        <view class="info-item">
          <text class="info-label">生效日期</text>
          <text class="info-value"
            >{{ item.effectiveStartTime }}-{{ item.effectiveEndTime }}</text
          >
        </view>
        <view class="line" wx:if="{{index < insuranceInfo.length - 1}}"></view>
      </view>
    </scroll-view>
  </view>
  <view class="no-insurance-icon" wx:else>
    <image src="{{SingleVehicle.NoTicketIcon}}" />
    <view>暂无保单</view>
  </view>
</template>

<script>
  import { createComponent } from '@mpxjs/core';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { SingleVehicle } from 'shared/assets/imageUrl';
  import { getLOPdomain, getLOPDN } from 'shared/utils/config';
  import { doRequest } from 'shared/api/fetch';
  createComponent({
    properties: {
      vehicleName: String
    },
    data: {
      insuranceInfo: [],
      SingleVehicle
    },
    lifetimes: {
      created() {
        console.log(this.vehicleName);
        this.getVehicleInsurance();
      }
    },
    methods: {
      async getVehicleInsurance() {
        const res = await doRequest({
          url: '/mobile/applet/map/getVehicleInsurance',
          method: 'POST',
          data: {
            vehicleName: this.vehicleName
          }
        });
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            insuranceInfo: res.data
          });
        }
      },
      handleCheckInsurance(e) {
        wx.navigateTo({
          url: `/pages/insurancePreview/index?vehicleInsuranceId=${e.currentTarget.dataset.vehicleInsuranceId}`
        });
      }
    }
  });
</script>

<style lang="scss">
  .insurance-info {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    .insurance-info-list {
      width: 100%;
      height: 700px;
      .insurance-info-item {
        width: 100%;
        display: flex;
        flex-direction: column;

        .info-item {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          .check-text {
            font-size: 12px;
            font-family: PingFang SC;
            font-weight: 500;
            color: rgba(60, 110, 240, 1);
          }
        }
        .line {
          width: 100%;
          height: 1px;
          background-color: rgba(151, 151, 151, 0.2);
          margin: 12px 0;
        }

        .info-label {
          width: 75px;
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: normal;
          color: rgba(153, 153, 153, 1);
        }

        .info-value {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: normal;
          color: rgba(51, 51, 51, 1);
        }

        .info-item:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  .no-insurance-icon {
    height: 500px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(153, 153, 153, 1);
    image {
      width: 96px;
      height: 109px;
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true,
    usingComponents: {}
  };
</script>