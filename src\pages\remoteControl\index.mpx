<template>
  <view class="navi">
    <view class="back" bindtap="back">
      <view
        style="background: url({{RemoteControlUrl.NaviBackArrow}}) no-repeat center/100%; width: 20px; height: 20px;"
      >
      </view>
      退出
    </view>
    <view class="time">{{ currentTime }}</view>
    <view
      class="title-wrapper"
      style="background: url({{RemoteControlUrl.TitleBg}}) no-repeat center/100%; width: 55%; height: 54px;"
    >
      <view class="vehicle-name" bindtap="openSelectVehicle">{{
        vehicleName
      }}</view>
      <view
        class="select-vehicle"
        style="background: url({{RemoteControlUrl.SelectCarArrow}}) no-repeat center/100%; width: 22px; height: 22px;"
        bindtap="openSelectVehicle"
      ></view>
    </view>
    <common-switch
      control-start="{{isControlStart}}"
      checked="{{isForceControl}}"
      bindchangechecked="onChangeForceControl"
      bindopenforcecontrol="openForceControl"
    >
    </common-switch>
  </view>
  <view class="index" id="sgm-remote-control">
    <view class="content">
      <view class="left-wheel">
        <pan-gesture-handler onGestureEvent="handlepanAngle">
          <view
            class="left-joystick"
            style="background: url({{RemoteControlUrl.JoyStick}}) no-repeat center/100%"
            bindtouchstart="handleViewVibrate"
          ></view>
        </pan-gesture-handler>
      </view>
      <view class="command-area">
        <delay-info
          wx:if="{{isControlStart}}"
          delay-info="{{responseData}}"
        ></delay-info>
        <button
          plain="{{true}}"
          class="custom-btn"
          hover-class="custom-btn-hover"
          wx:if="{{!isControlStart}}"
          bindtap="startControl"
          disabled="{{startBtnDisabled}}"
        >
          <view
            style="
              background: url({{RemoteControlUrl.ControlStart}}) no-repeat
                center/100%;
            "
            class="custom-icon"
          ></view>
          开启遥控器
        </button>
        <button
          plain="{{true}}"
          class="custom-btn"
          hover-class="custom-btn-hover"
          wx:if="{{isControlStart}}"
          bindtap="endControl"
          disabled="{{endBtnDisabled}}"
        >
          <view
            style="
              background: url({{RemoteControlUrl.ControlEnd}}) no-repeat
                center/100%;
            "
            class="custom-icon"
          ></view>
          关闭遥控器
        </button>
        <button
          plain="{{true}}"
          class="custom-btn"
          hover-class="custom-btn-hover"
          wx:if="{{!isControlStart}}"
          bindtap="openSettingModal"
        >
          <view
            style="
              background: url({{RemoteControlUrl.ControlSetting}}) no-repeat
                center/100%;
            "
            class="custom-icon"
          ></view
          >遥控设置
        </button>
        <view class="command-btn-area" wx:if="{{isControlStart}}">
          <command-btn
            wx:for="{{remoteCommandList}}"
            wx:key="key"
            openCommand="{{item.openCommand}}"
            openLabel="{{item.openLabel}}"
            closeCommand="{{item.closeCommand}}"
            closeLabel="{{item.closeLabel}}"
            iconSrc="{{item.iconSrc}}"
            closeIconSrc="{{item.closeIconSrc}}"
            bindsendcommand="sendCommand"
          ></command-btn>
        </view>
      </view>
      <view class="right-wheel">
        <pan-gesture-handler onGestureEvent="handlepanSpeed">
          <view
            class="right-joystick"
            style="background: url({{RemoteControlUrl.JoyStick}}) no-repeat center/100%"
            bindtouchstart="handleViewVibrate"
          ></view>
        </pan-gesture-handler>
      </view>
    </view>
  </view>
  <control-settings
    visible="{{showSettings}}"
    bindclosepopup="closeSettingModal"
    vehicleName="{{vehicleName}}"
    backUrl="{{backUrl}}"
  ></control-settings>
  <select-vehicle
    visible="{{showSelectVehicle}}"
    bindclosepopup="closeSelectVehicle"
    selectedVehicleName="{{vehicleName}}"
    bindselectvehicle="selectVehicle"
    isControlStart="{{isControlStart}}"
  >
  </select-vehicle>
</template>

<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import { RemoteControlUrl } from 'shared/assets/imageUrl';
  import * as dayjs from 'dayjs';
  import {
    REMOTE_COMMAND,
    REMOTE_CONTROL,
    WSReadyState,
    COMMAND_TYPE
  } from 'shared/utils/constant';
  import WebSocketClient from 'shared/api/websocket';
  import useLoginStore from 'shared/store/useLoginStore';
  import { mapStores } from '@mpxjs/pinia';
  import { calculateAngle, calculateSpeed } from 'shared/utils/remoteControl';
  import RemoteControlApi, {
    getDistanceInMeters
  } from 'shared/api/remoteControl';
  import { CommandApi } from 'shared/api/command';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { getRemoteControlUrl } from 'shared/utils/config';
  const GestureState = {
    POSSIBLE: 0,
    BEGIN: 1,
    ACTIVE: 2,
    END: 3,
    CANCELLED: 4
  };
  const { shared, spring, runOnJS } = wx.worklet;
  let requestId = 0;
  const coefficient = 0.04;
  createPage({
    data: {
      WSReadyState,
      RemoteControlUrl: RemoteControlUrl,
      fetchApi: new RemoteControlApi(),
      commanApi: new CommandApi(),
      isControlStart: false,
      showSettings: false,
      showSelectVehicle: false,
      currentTime: dayjs().format('HH:mm'),
      timeUpdater: null as any,
      vehicleName: '',
      isForceControl: false,
      wsClient: null as any,
      wsTimer: null as any,
      wsListener: null as any,
      responseData: null as any,
      commandType: COMMAND_TYPE.MOBILE,
      getDistanceInMeters,
      remoteCommandList: [
        {
          key: 'flashLight',
          openCommand: REMOTE_COMMAND.FLASH_LIGHT_OPEN,
          closeCommand: REMOTE_COMMAND.FLASH_LIGHT_CLOSE,
          openLabel: '开双闪',
          closeLabel: '关双闪',
          iconSrc: RemoteControlUrl.FlashLight,
          closeIconSrc: RemoteControlUrl.FlashLightClose
        },
        {
          key: 'lowLight',
          openCommand: REMOTE_COMMAND.LOW_LIGHT_OPEN,
          closeCommand: REMOTE_COMMAND.LOW_LIGHT_CLOSE,
          openLabel: '开大灯',
          closeLabel: '关大灯',
          iconSrc: RemoteControlUrl.LowLight,
          closeIconSrc: RemoteControlUrl.LowLightClose
        },
        {
          key: 'leftTurnLight',
          openCommand: REMOTE_COMMAND.LEFT_TURN_LIGHT_OPEN,
          closeCommand: REMOTE_COMMAND.LEFT_TURN_LIGHT_CLOSE,
          openLabel: '开左转灯',
          closeLabel: '关左转灯',
          iconSrc: RemoteControlUrl.LeftTurnLight,
          closeIconSrc: RemoteControlUrl.LeftTurnLightClose
        },
        {
          key: 'rightTurnLight',
          openCommand: REMOTE_COMMAND.RIGHT_TURN_LIGHT_OPEN,
          closeCommand: REMOTE_COMMAND.RIGHT_TURN_LIGHT_CLOSE,
          openLabel: '开右转灯',
          closeLabel: '关右转灯',
          iconSrc: RemoteControlUrl.RightTurnLight,
          closeIconSrc: RemoteControlUrl.RightTurnLightClose
        }
      ],
      angle: 0,
      speed: 0,
      cv: 0,
      backUrl: '',
      isNavigateBump: false,
      startBtnDisabled: false,
      endBtnDisabled: false
    },
    computed: {
      ...mapStores(useLoginStore)
    },
    lifetimes: {
      attached() {
        const x = shared(0);
        const y = shared(0);
        const scale = shared(1);
        const pressed = shared(false);
        this.applyAnimatedStyle('.left-joystick', () => {
          'worklet';
          return {
            transform: `translate(${x.value}px, 0px)`
          };
        });
        this.applyAnimatedStyle('.left-wheel', () => {
          'worklet';
          return {
            background:
              x.value > 0
                ? `radial-gradient(circle at 100% 50%, rgba(60,110,240,0.5) 0%, rgba(255,255,255,0) ${
                    x.value / 2
                  }%),` +
                  `url(${RemoteControlUrl.LeftWheelCircle}) no-repeat center/100%`
                : x.value < 0
                ? `radial-gradient(circle at 0% 50%, rgba(60,110,240,0.5) 0%, rgba(255,255,255,0) ${Math.abs(
                    x.value / 2
                  )}%),` +
                  `url(${RemoteControlUrl.LeftWheelCircle}) no-repeat center/100%`
                : `url(${RemoteControlUrl.LeftWheelCircle}) no-repeat center/100%`
          };
        });
        const scale1 = shared(1);
        const pressed1 = shared(false);
        this.applyAnimatedStyle('.right-joystick', () => {
          'worklet';
          return {
            transform: `translate(0px, ${y.value}px)`
          };
        });
        this.applyAnimatedStyle('.right-wheel', () => {
          'worklet';
          return {
            background:
              y.value > 0
                ? `radial-gradient(circle at 50% bottom, rgba(60,110,240,0.5) 0%,rgba(255,255,255,0) ${
                    y.value / 2
                  }%),` +
                  `url(${RemoteControlUrl.RightWheelCircle}) no-repeat center/100%`
                : y.value < 0
                ? `radial-gradient(circle at 50% top, rgba(60,110,240,0.5) 0%,rgba(255,255,255,0) ${Math.abs(
                    y.value / 2
                  )}%),` +
                  `url(${RemoteControlUrl.RightWheelCircle}) no-repeat center/100%`
                : `url(${RemoteControlUrl.RightWheelCircle}) no-repeat center/100%`
          };
        });
        this.x = x;
        this.y = y;
        this.scale = scale;
        this.pressed = pressed;

        this.scale1 = scale1;
        this.pressed1 = pressed1;
      }
    },
    onLoad(query: any) {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
      console.log(query);
      if (query.vehicleName) {
        this.setData({
          vehicleName: query.vehicleName
        });
        this.fetchApi.whistle(query.vehicleName);
      } else {
        this.openSelectVehicle();
      }
      const backUrl = getApp().globalData.backUrl;
      const eventType = getApp().globalData.eventType;
      const isNavigateBump = getApp().globalData.isNavigateBump;
      if (isNavigateBump) {
        this.setData({
          isNavigateBump: isNavigateBump
        });
      }
      if (eventType === 'navigate') {
        this.setData({
          backUrl: backUrl
        });
      }
    },
    onHide() {
      this.removeWsListener();
      if (this.vehicleName && this.wsClient) {
        this.timeUpdater && clearInterval(this.timeUpdater);
        this.brake();
        this.recovery(this.vehicleName);
        this.removeWsListener();
      }
    },
    onUnload() {
      this.removeWsListener();
      if (this.vehicleName && this.wsClient) {
        this.timeUpdater && clearInterval(this.timeUpdater);
        this.brake();
        this.recovery(this.vehicleName);
        this.removeWsListener();
        this.setData({
          vehicleName: ''
        });
      }
    },
    onShow() {
      if (this.timeUpdater) {
        clearInterval(this.timeUpdater);
      } else {
        const that = this;
        that.timeUpdater = setInterval(() => {
          const timeString = dayjs().format('HH:mm');
          if (that.currentTime !== timeString) {
            that.setData({
              currentTime: timeString
            });
          }
        }, 1000);
      }
    },

    methods: {
      back() {
        console.log(this.backUrl);
        if (this.isNavigateBump) {
          wx.navigateBack({
            delta: 1
          });
        } else {
          wx.reLaunch({
            url: this.backUrl
          });
        }
      },
      // ws 60s无操作监听
      startWsListener() {
        this.wsListener && clearTimeout(this.wsListener);
        if (this.wsClient) {
          this.wsListener = setTimeout(() => {
            this.endControl();
          }, 60000);
        }
      },
      removeWsListener() {
        this.wsListener && clearTimeout(this.wsListener);
      },
      // main方法：下发角度、速度
      formatSendMessage() {
        requestId += 1;
        const tv = this.speed;
        if (tv === 0 || (tv > 0 && this.cv < 0) || (tv < 0 && this.cv > 0)) {
          this.setData({
            cv: 0
          });
          const data = {
            eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
            data: {
              id: requestId,
              vehicleName: this.vehicleName,
              commandType: this.commandType,
              moduleName: 'supervisor',
              timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              targetVelocity: 0,
              targetAngle: this.angle
            }
          };
          this.wsClient.sendMessage(data);
        } else {
          if (tv - this.cv > 0) {
            const speed = this.cv + coefficient;

            if (speed <= tv) {
              this.setData({
                cv: speed
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: speed,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            } else {
              this.setData({
                cv: tv
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: tv,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            }
          } else if (tv - this.cv < 0) {
            const speed = this.cv - coefficient;
            if (speed >= tv) {
              this.setData({
                cv: speed
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: speed,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            } else {
              this.setData({
                cv: tv
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: tv,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            }
          } else {
            this.setData({
              cv: tv
            });
            const data = {
              eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
              data: {
                id: requestId,
                vehicleName: this.vehicleName,
                commandType: this.commandType,
                moduleName: 'supervisor',
                timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                targetVelocity: tv,
                targetAngle: this.angle
              }
            };
            this.wsClient.sendMessage(data);
          }
        }
      },
      // 监听ws创建成功
      handleOnOpen() {
        this.handleBroadcast('进入移动端摇车', this.vehicleName);
        this.wsTimer = setInterval(() => {
          this.formatSendMessage();
        }, 100);
      },
      // 远程语音播报
      handleBroadcast(value: string, vehicleName: string) {
        this.fetchApi.broadcast(value, this.vehicleName);
      },
      // 遥控超时，下发一次RESET
      handleRestControl() {
        requestId += 1;
        const data = {
          eventType: REMOTE_CONTROL.REMOTE_REQUEST_RESET_ABNORMAL_CONTROL,
          data: {
            id: requestId,
            vehicleName: this.vehicleName,
            commandType: 'MOBILE',
            moduleName: 'supervisor',
            timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
        };
        this.wsClient.sendMessage(data);
      },
      // 急停
      async stop(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        try {
          const res: any = await this.fetchApi.openControl(vehicleName);
          if (res.code === HTTPSTATUSCODE.Success) {
            wx.hideLoading();

            const userName = wx.getStorageSync('userName');
            // 建立长链
            if (!this.wsClient && userName) {
              const wsUrl = `${getRemoteControlUrl()}/minimonitor/client/ws/mobile/control?userName=${userName}`;
              const token = wx.getStorageSync('JD_AUTH_TOKEN');
              this.wsClient = new WebSocketClient(
                wsUrl,
                {
                  onOpen: (data: any) => {
                    this.handleOnOpen();
                    console.log('ws opened');
                  },
                  onClose: this.onClose,
                  onError: this.onError,
                  onMessage: (data: any) => {
                    this.onMessage(data);
                  },
                  initMessage: {
                    eventType: 'REMOTE_REQUEST_ENTER_CONTROL',
                    data: {
                      vehicleName: this.vehicleName,
                      timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
                    }
                  }
                },
                token
              );
            }
            this.setData({
              isControlStart: true,
              startBtnDisabled: false
            });
            this.startWsListener();
            wx.hideLoading();
          } else {
            wx.hideLoading();
            wx.showToast({
              title: res.message,
              icon: 'none',
              duration: 3000
            });
            this.setData({
              startBtnDisabled: false
            });
          }
        } catch (err) {
          console.error(err);
          this.setData({
            startBtnDisabled: false
          });
        }
      },
      // 恢复
      async recovery(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        this.setData({
          endBtnDisabled: true
        });
        try {
          this.wsTimer && clearInterval(this.wsTimer);
          // 断开长链
          this.wsClient &&
            this.wsClient.sendMessage({
              eventType: 'REMOTE_REQUEST_QUIT_CONTROL',
              data: {
                vehicleName: this.vehicleName,
                timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
              }
            });
          this.wsClient && this.wsClient.closeSocket();
          this.wsClient = null;
          this.wsTimer = null;
          this.setData({
            isControlStart: false,
            commandType: COMMAND_TYPE.MOBILE,
            isForceControl: false,
            endBtnDisabled: false,
            wsClient: null,
            wsTimer: null
          });
          const res: any = await this.fetchApi.closeControl(vehicleName);
          if (res.code === HTTPSTATUSCODE.Success) {
            this.handleBroadcast('退出移动端摇车', this.vehicleName);
            wx.hideLoading();
          } else {
            wx.hideLoading();
            wx.showToast({
              title: res.message,
              icon: 'none',
              duration: 3000
            });
          }
        } catch (err) {
          this.setData({
            endBtnDisabled: false
          });
          console.error(err);
        }
      },
      // 急刹
      async brake() {
        try {
          if (this.wsClient) {
            const res = await this.fetchApi.brake(this.vehicleName);
            console.log(res);
          }
        } catch (err) {
          console.error(err);
        }
      },
      // 强制遥控
      openForceControl() {
        this.setData({
          isForceControl: true,
          commandType: COMMAND_TYPE.SUPER
        });
      },
      startControl() {
        this.vehicleName && this.getVehiclePosition(this.vehicleName);
      },
      endControl() {
        this.vehicleName && this.recovery(this.vehicleName);
        this.removeWsListener();
      },
      onMessage(data: any) {
        const res = JSON.parse(data.data);
        if (res) {
          const { eventType } = res;
          // console.log(JSON.parse(data.data));
          if (eventType === 'REMOTE_REQUEST_VEHICLE_RESPONSE') {
            if (res.data.state === 'TIME_EXCEPTION') {
              this.handleRestControl();
            }
            const responseData = res.data;
            const { delayInfo } = res.data;
            if (delayInfo) {
              const supervisorModule = delayInfo.find(
                (item: any) => item.moduleName === 'supervisor'
              );
              const responseTimeStamp = Date.now();
              // 除2取单行通路的延时
              const commandDelay =
                (responseTimeStamp - supervisorModule?.transitTime) / 2 / 1000;
              responseData.commandDelay = commandDelay;
              responseData.responseTimeStamp = responseTimeStamp;
              this.setData({
                responseData
              });
            } else {
              this.setData({
                responseData: null
              });
            }
          } else if (eventType === 'CHANGE_VEHICLE_STATE_EVENT') {
            wx.showToast({
              title: '该车辆已被手柄遥控！',
              icon: 'none',
              duration: 3000
            });
          }
        }
      },
      onError(data: any) {
        console.log(data);
        console.log('ws error');
      },
      onClose(data: any) {
        console.log(data);
        console.log('ws closed');
      },
      openSettingModal() {
        this.setData({
          showSettings: true
        });
      },
      closeSettingModal() {
        this.setData({
          showSettings: false
        });
      },
      openSelectVehicle() {
        this.setData({
          showSelectVehicle: true
        });
      },
      closeSelectVehicle() {
        this.setData({
          showSelectVehicle: false
        });
      },
      onChangeForceControl() {
        this.setData({
          isForceControl: !this.isForceControl,
          commandType: COMMAND_TYPE.MOBILE
        });
      },
      // 切车逻辑封装
      async changeVehicle(oldVehicleName: string, newVehicleName: string) {
        try {
          const oldRecoveryRes: any = await this.fetchApi.closeControl(
            oldVehicleName
          );
          if (oldRecoveryRes.code === HTTPSTATUSCODE.Success) {
            const newStopRes: any = await this.fetchApi.openControl(
              newVehicleName
            );
            this.handleBroadcast('退出移动端摇车', oldVehicleName);
            if (newStopRes.code === HTTPSTATUSCODE.Success) {
              this.setData({
                vehicleName: newVehicleName
              });
              this.handleBroadcast('进入移动端摇车', newVehicleName);
              const oldRecoveryData = {
                eventType: 'REMOTE_REQUEST_QUIT_CONTROL',
                data: {
                  vehicleName: oldVehicleName,
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
                }
              };
              const newStopData = {
                eventType: 'REMOTE_REQUEST_ENTER_CONTROL',
                data: {
                  vehicleName: newVehicleName,
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
                }
              };
              this.wsClient && this.wsClient.sendMessage(oldRecoveryData);
              this.wsClient && this.wsClient.sendMessage(newStopData);
              setTimeout(() => {
                this.fetchApi.closeControl(oldVehicleName);
              }, 2000);
            } else {
              /**
               * 新车急停失败：
               * 1. toast提示
               * 2. 长链断开
               * 3. 清除定时器
               */
              wx.showToast({
                title: newStopRes.message,
                icon: 'none'
              });
              const oldRecoveryData = {
                eventType: 'REMOTE_REQUEST_QUIT_CONTROL',
                data: {
                  vehicleName: oldVehicleName,
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
                }
              };
              this.wsClient && this.wsClient.sendMessage(oldRecoveryData);
              this.wsClient && this.wsClient.closeSocket();
              this.wsClient = null;
              this.wsTimer && clearInterval(this.wsTimer);
              this.wsTimer = null;
              this.setData({
                isControlStart: false,
                commandType: COMMAND_TYPE.MOBILE,
                isForceControl: false,
                vehicleName: newVehicleName
              });
              this.fetchApi.whistle(newVehicleName);
            }
          } else {
            wx.showToast({
              title: oldRecoveryRes.message,
              icon: 'none'
            });
          }
        } catch (err) {
          console.error(err);
          wx.showToast({
            title: err,
            icon: 'none'
          });
        }
      },
      async selectVehicle(e: any) {
        try {
          // 长链不存在时，只更新vehicleName
          if (!this.wsClient) {
            this.setData({
              vehicleName: e.detail.vehicleInfo.vehicleName
            });
            this.fetchApi.whistle(e.detail.vehicleInfo.vehicleName);
          } else {
            // 长链存在时，判断距离 -> 下发旧车恢复 -> 下发新车急停 -> ws msg，串行逻辑
            const poiRes: any = await this.fetchApi.getVehiclePosition(
              e.detail.vehicleInfo.vehicleName
            );
            const userLocation = wx.getStorageSync('userLocation');
            if (poiRes.code === HTTPSTATUSCODE.Success) {
              const distance = this.getDistanceInMeters(
                userLocation.latitude,
                userLocation.longitude,
                poiRes.data?.latitude,
                poiRes.data?.longitude
              );
              if (distance <= 100 && distance >= 0) {
                this.changeVehicle(
                  this.vehicleName,
                  e.detail.vehicleInfo.vehicleName
                );
              } else {
                // 距离弹窗
                const that = this;
                wx.showModal({
                  content: `当前您与${e.detail.vehicleInfo.vehicleName}的距离超过100m。请核对车号，避免接管错车辆。`,
                  cancelText: '取消',
                  confirmText: '核对无误',
                  confirmColor: '#FA4219',
                  success: function (res) {
                    if (res.confirm) {
                      that.changeVehicle(
                        that.vehicleName,
                        e.detail.vehicleInfo.vehicleName
                      );
                    } else if (res.cancel) {
                      console.log('用户点击取消');
                    }
                  },
                  fail(res) {
                    console.log(res);
                  }
                });
              }
            } else {
              wx.showToast({
                title: '车辆位置获取失败',
                icon: 'none'
              });
            }
          }
        } catch (err) {
          console.error(err);
          wx.showToast({
            title: err,
            icon: 'none'
          });
        }
      },
      // 用户位置与无人车 距离弹窗
      showPositionModal(vehicleName: string) {
        const that = this;
        wx.showModal({
          content: `当前您与${vehicleName}的距离超过100m。请核对车号，避免接管错车辆。`,
          cancelText: '取消',
          confirmText: '核对无误',
          confirmColor: '#FA4219',
          success: function (res) {
            if (res.confirm) {
              // 下发急停
              that.stop(vehicleName);
            } else if (res.cancel) {
              console.log('用户点击取消');
              that.setData({
                startBtnDisabled: false
              });
            }
          },
          fail(res) {
            console.log(res);
            that.setData({
              startBtnDisabled: false
            });
          }
        });
      },
      // 校验用户位置
      async getVehiclePosition(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        this.setData({
          startBtnDisabled: true
        });
        try {
          const res: any = await this.fetchApi.getVehiclePosition(vehicleName);
          const userLocation = wx.getStorageSync('userLocation');
          if (res.code === HTTPSTATUSCODE.Success) {
            const distance = this.getDistanceInMeters(
              userLocation.latitude,
              userLocation.longitude,
              res.data?.latitude,
              res.data?.longitude
            );
            wx.hideLoading();
            if (distance <= 100 && distance >= 0) {
              // 下发急停
              this.stop(vehicleName);
            } else {
              this.showPositionModal(vehicleName);
            }
          } else {
            wx.hideLoading();
            wx.showToast({
              title: '车辆位置获取失败',
              icon: 'none'
            });
            this.setData({
              startBtnDisabled: false
            });
          }
        } catch (err) {
          this.setData({
            startBtnDisabled: false
          });
          console.error(err);
        }
      },
      getAngle(x: any) {
        this.setData({
          angle: calculateAngle(x)
        });
      },
      getSpeed(y: any) {
        this.setData({
          speed: calculateSpeed(y)
        });
      },
      handlepanAngle(evt: any) {
        'worklet';
        const getAngle = this.getAngle.bind(this);
        const startWsListener = this.startWsListener.bind(this);
        const removeWsListener = this.removeWsListener.bind(this);
        if (evt.state === GestureState.POSSIBLE) {
          this.pressed.value = true;
          this.scale.value = spring(1.2, {}, () => {});
          runOnJS(removeWsListener)();
        } else if (
          evt.state === GestureState.END ||
          evt.state === GestureState.CANCELLED
        ) {
          this.pressed.value = false;
          this.scale.value = spring(1, {}, () => {});
          this.x.value = 0;
          runOnJS(startWsListener)();
        } else if (evt.state === GestureState.ACTIVE) {
          this.x.value += evt.deltaX;
          // 左负右正
          if (Math.ceil(evt.absoluteX) < 40) {
            this.x.value = -130;
          }
          if (Math.ceil(evt.absoluteX) > 310) {
            this.x.value = 130;
          }
        }
        runOnJS(getAngle)(this.x.value);
      },
      handlepanSpeed(evt: any) {
        'worklet';
        const getSpeed = this.getSpeed.bind(this);
        const brake = this.brake.bind(this);
        const startWsListener = this.startWsListener.bind(this);
        const removeWsListener = this.removeWsListener.bind(this);
        if (evt.state === GestureState.POSSIBLE) {
          this.pressed1.value = true;
          this.scale1.value = spring(1.2, {}, () => {});
          runOnJS(removeWsListener)();
        } else if (
          evt.state === GestureState.END ||
          evt.state === GestureState.CANCELLED
        ) {
          this.pressed1.value = false;
          this.scale1.value = spring(1, {}, () => {});
          this.y.value = 0;
          runOnJS(brake)();
          runOnJS(startWsListener)();
        } else if (evt.state === GestureState.ACTIVE) {
          // 上负下正
          this.y.value += evt.deltaY;
          if (Math.ceil(evt.absoluteY) < 90) {
            this.y.value = -130;
          }
          if (Math.ceil(evt.absoluteY) > 350) {
            this.y.value = 130;
          }
        }
        runOnJS(getSpeed)(this.y.value);
      },
      handleViewVibrate() {
        if (this.isControlStart) {
          wx.vibrateShort({
            type: 'heavy',
            success: () => {
              console.log('震动成功');
            },
            fail: () => {
              console.log('震动失败');
            }
          });
        }
      },
      sendCommand(e: any) {
        this.commanApi.sendCommand({
          vehicleName: this.vehicleName,
          commandType: e.detail.command,
          operateTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
        });
      }
    }
  });
</script>

<script name="json">
  module.exports = {
    usingComponents: {
      'control-settings': '../../widgets/remoteControl/controlSettings.mpx',
      'common-switch': '../../shared/ui/commonSwitch.mpx',
      'command-btn': '../../widgets/remoteControl/commandBtn.mpx',
      'delay-info': '../../widgets/remoteControl/delayInfo.mpx',
      'select-vehicle': '../../widgets/remoteControl/selectVehicle.mpx'
    },

    renderer: 'skyline',
    componentFramework: 'glass-easel',
    navigationStyle: 'custom',
    pageOrientation: 'landscape'
  };
</script>

<style lang="scss">
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
  }
  .navi {
    width: 100%;
    height: 54px;
    position: absolute;
    z-index: 999;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 32px;
    .back {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 80px;
      height: 44px;
    }
    .title-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 6.5%;
    }
  }
  .index {
    height: 100%;
    width: 100%;
    padding: 32px 29px;
    background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 226, 226, 1) 21.81836037654774%,
      rgba(159, 215, 255, 1) 100%
    );
    .content {
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding: 0px 0px 0px 16px;
      .command-area {
        height: 80%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .custom-btn {
          border: 1.5px solid #3c6ef0;
          border-radius: 125px;
          color: #3c6ef0;
          font-size: 14px;
          width: 134px;
          height: 44px;
          margin-top: 16px;
          transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
          .custom-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            opacity: 1;
            transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
          }
        }
        .custom-btn-hover {
          border: 1.5px solid #8ca8f0;
          border-radius: 125px;
          color: #8ca8f0;
          font-size: 14px;
          width: 134px;
          height: 44px;
          margin-top: 16px;
          transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
          .custom-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            opacity: 0.5;
            transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
          }
        }
        .command-btn-area {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .left-wheel {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 260px;
        height: 260px;
        border: 1px solid rgba(242, 242, 242, 1);
        border-radius: 130px;
        box-shadow: 0px 0px 20px 3px rgb(125 122 122 / 18%);
        .left-joystick {
          width: 70px;
          height: 70px;
        }
      }
      .right-wheel {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 260px;
        height: 260px;
        border: 1px solid rgba(242, 242, 242, 1);
        border-radius: 130px;
        box-shadow: 0px 0px 20px 3px rgb(125 122 122 / 18%);
        .right-joystick {
          width: 70px;
          height: 70px;
        }
      }
    }
  }
</style>
