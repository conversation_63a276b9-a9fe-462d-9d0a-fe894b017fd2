<template>
  <view class="vehicle-item" bindtap="selectVehicle">
    <view
      class="info"
      wx:class="{{ {offline : (!itemInfo.systemStatus || itemInfo.systemStatus === 'OFFLINE') ? true:false}}}"
    >
      <text class="vehicle-name">{{ itemInfo.vehicleName }}</text>
      <text class="description" wx:if="{{itemInfo.takeoverStatus}}">{{
        itemInfo.description
      }}</text>
    </view>
    <image
      class="arrow"
      wx:if="{{itemInfo.vehicleName === selectedVehicleName}}"
      src="{{RemoteControlUrl.SelectedIcon}}"
    />
  </view>
</template>
<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { RemoteControlUrl } from 'shared/assets/imageUrl';
  import { TakeOverType } from 'shared/utils/constant';

  createComponent({
    data: {
      RemoteControlUrl: RemoteControlUrl
    },
    properties: {
      itemInfo: {
        type: Object,
        value: {}
      },
      selectedVehicleName: {
        type: String,
        value: ''
      }
    },
    methods: {
      selectVehicle() {
        this.triggerEvent('handleSelectItem', {
          val: this.itemInfo.vehicleName
        });
      }
    }
  });
</script>
<style lang="scss">
  .vehicle-item {
    margin-left: 16px;
    margin-right: 16px;
    height: 52px;
    line-height: 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;

    .info {
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(26, 26, 26, 1);
      &.offline {
        color: rgba(204, 204, 204, 1);
      }
      .vehicle-name {
        font-size: 16px;
      }

      .description {
        margin-left: 12px;
        font-size: 12px;
      }
    }

    .arrow {
      width: 16px;
      height: 16px;
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {}
  }
</script>
