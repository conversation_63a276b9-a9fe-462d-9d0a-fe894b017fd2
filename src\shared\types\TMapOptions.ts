/* eslint-disable camelcase */

/** 地点搜索 */
export interface TMapSearchOptions {
  /**
   * POI搜索关键字（默认周边搜索，若需要使用指定地区名称和矩形搜索，请使用region和rectangle参数，不能同时使用）
   */
  keyword: string;
  /**
   * 位置坐标，
   * ①String格式：lat<纬度>,lng<经度>（例：location: ‘39.984060,116.307520’）
   * ②Object格式：
   * {
   *  latitude: 纬度,
   *  longitude: 经度
   * }
   * 默认是当前位置
   */
  location?:
    | string
    | {
        latitude: number;
        longitude: number;
      };
  /**
   * 短地址，缺省时返回长地址，可选值：‘short’
   */
  address_format?: string;
  /**
   * 每页条目数，最大限制为20条，默认值10
   */
  page_size?: number;
  /**
   * 第x页，默认第1页
   */
  page_index?: number;
  /**
   * 指定地区名称，不自动扩大范围，如北京市,（使用该功能，若涉及到行政区划，建议将auto_extend设置为0）
   * 当用户使用泛关键词搜索时（如酒店、超市），这类搜索多为了查找附近， 使用location参数，搜索结果以location坐标为中心，返回就近地点，体验更优(默认为用户当前位置坐标)
   * 不与rectangle同时使用
   */
  region?: string;
  /**
   * 矩形区域范围，不与region同时使用
   * 格式：lat,lng<左下/西南>, lat,lng<右上/东北>(示例：rectangle:‘40.984061,116.307520,39.984060,116.507520’)
   */
  rectangle?: string;
  /**
   * 取值1：[默认]自动扩大范围；
   * 取值0：不扩大。 仅适用于默认周边搜索以及制定地区名称搜索。
   */
  auto_extend?: string;
  /**
   * 最多支持五个分类
   * 搜索指定分类
   * category=公交站
   * 搜索多个分类
   * category=大学,中学
   * 排除指定分类
   * category<>商务楼宇
   * （注意参数值要进行url编码）
   */
  filter?: string;
  /**
   * 签名校验
   * 开启WebServiceAPI签名校验的必传参数，只需要传入生成的SK字符串即可，不需要进行MD5加密操作
   */
  sig?: string;
}

/** 关键词输入提示 */
export interface TMapSuggestionOptions {
  /**
   * POI搜索关键字（默认周边搜索，若需要使用指定地区名称和矩形搜索，请使用region和rectangle参数，不能同时使用）
   */
  keyword: string;
  /**
   * 检索策略，目前支持：
   * policy=0：默认，常规策略
   * policy=1：本策略主要用于收货地址、上门服务地址的填写，
   * 提高了小区类、商务楼宇、大学等分类的排序，过滤行政区、
   * 道路等分类（如海淀大街、朝阳区等），排序策略引入真实用户对输入提示的点击热度，
   * 使之更为符合此类应用场景，体验更为舒适
   */
  policy?: number;
  /**
   * 是否返回子地点，如大厦停车场、出入口等取值：
   * 0 [默认]不返回
   * 1 返回
   */
  get_subpois?: number;
  /**
   * 位置坐标，
   * ①String格式：lat<纬度>,lng<经度>（例：location: ‘39.984060,116.307520’）
   * ②Object格式：
   * {
   *  latitude: 纬度,
   *  longitude: 经度
   * }
   * 默认是当前位置
   */
  location?:
    | string
    | {
        latitude: number;
        longitude: number;
      };

  /**
   * 指定地区名称，不自动扩大范围，如北京市,（使用该功能，若涉及到行政区划，建议将auto_extend设置为0）
   * 当用户使用泛关键词搜索时（如酒店、超市），这类搜索多为了查找附近， 使用location参数，搜索结果以location坐标为中心，返回就近地点，体验更优(默认为用户当前位置坐标)
   * 不与rectangle同时使用
   */
  region?: string;
  /**
   * 取值： 0：[默认]当前城市无结果时，自动扩大范围到全国匹配 1：固定在当前城市
   */
  region_fix?: number;
  /**
   * 短地址，缺省时返回长地址，可选值：‘short’
   */
  address_format?: string;
  /**
   * 每页条目数，最大限制为20条，默认值10
   */
  page_size?: number;
  /**
   * 第x页，默认第1页
   */
  page_index?: number;
  /**
   * 最多支持五个分类
   * 搜索指定分类
   * category=公交站
   * 搜索多个分类
   * category=大学,中学
   * 排除指定分类
   * category<>商务楼宇
   * （注意参数值要进行url编码）
   */
  filter?: string;
  /**
   * 签名校验
   * 开启WebServiceAPI签名校验的必传参数，只需要传入生成的SK字符串即可，不需要进行MD5加密操作
   */
  sig?: string;
}

/** 逆地址解析（坐标转地址） */
export interface TMapReverseGeocoderOptions {
  /**
   * 位置坐标，
   * ①String格式：lat<纬度>,lng<经度>（例：location: ‘39.984060,116.307520’）
   * ②Object格式：
   * {
   *  latitude: 纬度,
   *  longitude: 经度
   * }
   * 默认是当前位置
   */
  location?:
    | string
    | {
        latitude: number;
        longitude: number;
      };
  /**
   * 输入的locations的坐标类型，可选值为[1,6]之间的整数，每个数字代表的类型说明：
   * 1 GPS坐标
   * 2 sogou经纬度
   * 3 baidu经纬度
   * 4 mapbar经纬度
   * 5 [默认]腾讯、google、高德坐标
   * 6 sogou墨卡托
   */
  coord_type?: number;
  /**
   * 是否返回周边POI列表：
   * 1.返回；0不返回(默认)
   */
  get_poi?: number;
  /**
   * 用于控制Poi列表：
   * 1 poi_options=address_format=short
   * 返回短地址，缺省时返回长地址
   * 2 poi_options=radius=5000
   * 半径，取值范围 1-5000（米）
   * 3 poi_options=policy=1/2/3
   * 控制返回场景，
   * policy=1[默认] 以地标+主要的路+近距离poi为主，着力描述当前位置；
   * policy=2 到家场景：筛选合适收货的poi，并会细化收货地址，精确到楼栋；
   * policy=3 出行场景：过滤掉车辆不易到达的POI(如一些景区内POI)，增加道路出路口、交叉口、大区域出入口类POI，排序会根据真实API大用户的用户点击自动优化。
   */
  poi_options?: string;
  /**
   * 签名校验
   * 开启WebServiceAPI签名校验的必传参数，只需要传入生成的SK字符串即可，不需要进行MD5加密操作
   */
  sig?: string;
}

/** 地址解析（地址转坐标） */
export interface TMapGeocoderOptions {
  /**
   * 地址（注：地址中请包含城市名称，否则会影响解析效果），如：‘北京市海淀区彩和坊路海淀西大街74号’
   */
  address: string;
  /**
   * 指定地区名称，不自动扩大范围，如北京市,（使用该功能，若涉及到行政区划，建议将auto_extend设置为0）
   * 当用户使用泛关键词搜索时（如酒店、超市），这类搜索多为了查找附近， 使用location参数，搜索结果以location坐标为中心，返回就近地点，体验更优(默认为用户当前位置坐标)
   * 不与rectangle同时使用
   */
  region?: string;
  /**
   * 签名校验
   * 开启WebServiceAPI签名校验的必传参数，只需要传入生成的SK字符串即可，不需要进行MD5加密操作
   */
  sig?: string;
}

export interface TMapBaseResponse {
  /**
   * 状态码，0为正常,
   * 310请求参数信息有误，
   * 311Key格式错误,
   * 306请求有护持信息请检查字符串,
   * 110请求来源未被授权
   */
  status: number;
  /**
   * 状态说明，即对状态码status进行说明，
   * 如：
   * status为0,message为"query ok",为正常,
   * status为310,message为"请求参数信息有误",
   * status为311,message为"key格式错误",
   * status为306,message为"请求有护持信息请检查字符串",
   * status为110,message为"请求来源未被授权"
   */
  message: string;
  /**
   * TMap.search()、TMap.getSuggestion()方法返回
   * 本次搜索结果总数
   */
  count?: number;
  /**
   * TMap.search()、TMap.getSuggestion()方法返回
   * 搜索结果POI数组，每项为一个POI对象
   */
  data?: any;
  /**
   * TMap.geocoder()、TMap.reverseGeocoder()方法返回
   * 地址解析结果 || 逆地址解析结果
   */
  result?: any;
}
