<template>
  <button
    class="command-btn-wrapper"
    style="{{size === 'small' ? 'width: 46px;height: 58px; margin-top: 83px;' : 'width: 64px;height: 60px; margin-top: 24px;'}}"
    hover-class="command-btn-wrapper-hover"
    bindtap="onTapOpen"
    wx:if="{{!isOpen}}"
  >
    <view
      class="command-icon"
      style="background: url({{iconSrc}}) no-repeat center/100%"
    ></view>
    <view class="command-label">{{ openLabel }}</view>
  </button>
  <button
    class="command-btn-wrapper"
    style="{{size === 'small' ? 'width: 46px;height: 58px; margin-top: 83px;' : 'width: 64px;height: 60px; margin-top: 24px;'}}"
    hover-class="command-btn-wrapper-hover"
    bindtap="onTapClose"
    wx:else
  >
    <view
      class="command-icon"
      style="background: url({{closeIconSrc}}) no-repeat center/100%"
    ></view>
    <view class="command-label">{{ closeLabel }}</view>
  </button>
</template>

<script lang='ts'>
  import { createComponent } from '@mpxjs/core';

  createComponent({
    properties: {
      openCommand: {
        type: String,
        value: 'FLASH_LIGHT_OPEN'
      },
      openLabel: {
        type: String,
        value: '开双闪'
      },
      closeCommand: {
        type: String,
        value: 'FLASH_LIGHT_CLOSE'
      },
      closeLabel: {
        type: String,
        value: '关双闪'
      },
      iconSrc: {
        type: String,
        value:
          'https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/remoteControl/flash-light.png?Expires=3868669484&AccessKey=n828WHAXD584pTvi&Signature=NRWYE4714%2BgJMLKZsHFt1A079As%3D'
      },
      closeIconSrc: {
        type: String
      },
      size: {
        type: String,
        value: 'small'
      }
    },
    data: {
      isOpen: false
    },
    methods: {
      onTapOpen() {
        this.triggerEvent('sendcommand', {
          command: this.openCommand
        });
        this.setData({
          isOpen: true
        });
      },
      onTapClose() {
        this.triggerEvent('sendcommand', {
          command: this.closeCommand
        });
        this.setData({
          isOpen: false
        });
      }
    }
  });
</script>

<style lang="scss">
  .command-btn-wrapper,
  .command-btn-wrapper-hover {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    color: rgba(100, 100, 100, 1);
    padding: 8px;
    .command-icon {
      width: 20px;
      height: 17px;
    }
    .command-label {
      font-size: 10px;
      font-family: PingFang SC;
      font-weight: 500;
    }
    margin-right: 4px;
  }
  .command-btn-wrapper-hover {
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
    background: #7093ed;
    color: white;
    border: 1px solid transparent;
  }
</style>
<script name="json">
  module.exports = {
    component: true
  };
</script>
