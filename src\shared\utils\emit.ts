/* eslint-disable require-jsdoc */
class Emitter {
  all: any = {};
  listenerConfigsForEvent(event: string) {
    let listeners = this.all[event];
    if (!listeners) {
      listeners = [];
      this.all[event] = listeners;
    }
    return listeners;
  }

  on(event: string, listener: AnyFunc) {
    const listeners = this.listenerConfigsForEvent(event);
    if (listeners.indexOf(listener) === -1) {
      listeners.push({
        listener
      });
    }
  }

  off(event: string, listener: AnyFunc) {
    const listeners = this.all[event];
    let findIndex = -1;
    const found = listeners.some((item: any, index: number) => {
      if (item.listener === listener) {
        findIndex = index;
        return true;
      }
      return false;
    });
    if (found) {
      listeners.splice(findIndex, 1);
    }
  }

  emit(event: string | number, args: any) {
    if (this.all[event]) {
      this.all[event].forEach((cb: any) => {
        cb.listener(args);
      });
    }
  }
}

export const emitter = new Emitter();
export function addGlobalEventListener(event: string, cb: AnyFunc) {
  emitter.on(event, cb);
}

export function sendGlobalEvent(event: string, args?: any) {
  emitter.emit(event, args);
}

export function removeGlobalEventListener(event: string, cb: AnyFunc) {
  emitter.off(event, cb);
}
