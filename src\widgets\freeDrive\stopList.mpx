<template>
  <view class="top-tab-wrapper" wx:if="{{getTabPermission}}">
    <view
      data-key="stop"
      bindtap="changeTab"
      wx:class="{{{'selected-tab': activeKey === 'stop', 'unselected-tab': activeKey !== 'stop'}}}"
      >去往停靠点</view
    >
    <view class="divider" />
    <view
      data-key="anyStop"
      bindtap="changeTab"
      wx:class="{{{'selected-tab': activeKey !== 'stop', 'unselected-tab': activeKey === 'stop'}}}"
      >去往任何点</view
    >
  </view>
  <view class="choose-stop" wx:if="{{activeKey === 'stop'}}">
    <search-bar
      placeholder="搜索停靠点名称"
      model:search-value="{{searchValue}}"
      bindonsearch="onSearch"
      bindonclear="onClear"
    ></search-bar>
    <scroll-view
      wx:if="{{showFilter}}"
      scroll-y
      type="list"
      class="scroll-conatiner"
    >
      <view
        class="rich-text"
        wx:for="{{ filteredStopList}}"
        wx:key="stopId"
        bindtap="handleSelectStop(item)"
      >
        <text wx:if="{{item.start}}">{{ item.start }}</text>
        <rich-text nodes="{{item.richTextNodes}}"> </rich-text>
        <text wx:if="{{item.end}}">{{ item.end }}</text>
      </view>
    </scroll-view>

    <scroll-view wx:else scroll-y type="list" class="scroll-conatiner">
      <text
        class="stop-item"
        wx:for="{{ stopList}}"
        wx:key="stopId"
        bindtap="handleSelectStop(item)"
        >{{ item.stopName }}</text
      >
    </scroll-view>
  </view>
  <view class="choose-any-stop" wx:else>
    <view
      class="any-stop-wrapper"
      style="background: url({{FreeDriveUrl.AnyDriveBg}}) no-repeat center/120%"
    >
      <view class="label-stopname" wx:if="{{anyDriveInfo.lat}}"
        >停靠点名称：{{ anyDriveInfo.stopName }}</view
      >
      <view class="anyDrive-info" wx:if="{{anyDriveInfo.lat}}">
        <view class="label-lon"
          >经度：<view class="value">{{ getFixedLon }}</view></view
        >
        <view class="label-lat"
          >纬度：<view class="value">{{ getFixedLat }}</view></view
        >
        <view class="label-head"
          >朝向：<view class="value">{{ getFixedHead }}</view></view
        >
      </view>
      <view class="empty-status" wx:else>暂未选点，选点后可发车</view>
      <view class="choose-point-btn" bindtap="goAnyDrive">图上选点</view>
    </view>
    <view
      wx:class="{{{'anydrive-btn': anyDriveInfo.lat, 'anydrive-btn-disabled': !anyDriveInfo.lat}}}"
      bindtap="handleAnyDrive"
    >
      去发车
    </view>
  </view>
  <t-dialog
    visible="{{showDialog}}"
    title="是否确定去往此地"
    confirm-btn="确定出发"
    cancel-btn="取消"
    bind:confirm="confirmDialog"
    bind:cancel="closeDialog"
  >
    <view slot="content" class="dialog-content">
      <view
        >去往停车点：{{
          selectedStopInfo.stopName
            ? selectedStopInfo.stopName
            : anyDriveInfo.stopName
        }}</view
      >
      <view class="vehicle-num"
        >已选车辆：<text style="color: rgba(250, 44, 25, 1)">{{
          selectedVehicle.length
        }}</text
        >辆</view
      >
      <scroll-view class="vehicle-container" scroll-y type="list">
        <view class="row-conatiner">
          <view class="vehicle" wx:for="{{selectedVehicle}}" wx:key="item">{{
            item
          }}</view>
        </view>
      </scroll-view>
    </view>
  </t-dialog>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, FreeDriveUrl, VehicleUrl } from 'shared/assets/imageUrl';
  import { PermissionKey, SystemStatus } from 'shared/utils/constant';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { FreeDriveApi } from 'shared/api/freeDrive';
  import { getPermissionById } from 'shared/utils/utils';
  interface Data {
    WorkBenchUrl: any;
    FreeDriveUrl: any;
    VehicleUrl: any;
    SystemStatus: any;
    fetchApi: any;
    searchMode: any;
    vehicleList: any[];
  }
  enum OpenType {
    ANY_DRIVE = 'anyDrive',
    STOP_POINT = 'stopPoint'
  }
  createComponent<any>({
    properties: {
      selectedVehicle: {
        type: Array,
        value: []
      },
      backUrl: {
        type: String
      },
      anyDriveInfo: {
        type: Object,
        value: {
          lat: 0,
          lon: 0,
          head: 0,
          stopName: ''
        }
      }
    },
    data: {
      fetchApi: new FreeDriveApi(),
      stopList: [],
      FreeDriveUrl,
      filteredStopList: [],
      searchValue: '',
      showDialog: false,
      selectedStopInfo: {},
      activeKey: 'stop',
      openType: OpenType.STOP_POINT
    },
    computed: {
      showFilter() {
        return this.searchValue && this.searchValue.length > 0;
      },
      getFixedLon() {
        return this.anyDriveInfo.lon.toFixed(4);
      },
      getFixedLat() {
        return this.anyDriveInfo.lat.toFixed(4);
      },
      getFixedHead() {
        return this.anyDriveInfo.head.toFixed(4);
      },
      getTabPermission() {
        return (
          getPermissionById(PermissionKey.stopPointBtn) &&
          getPermissionById(PermissionKey.anyDriveBtn)
        );
      }
    },
    methods: {
      changeTab(e: any) {
        this.setData({
          activeKey: e.target.dataset.key
        });
      },
      goAnyDrive() {
        wx.navigateTo({
          url: '/pages/workbench/anyDrive/index'
        });
      },
      async getStopList() {
        const res = await this.fetchApi.getStopList(this.selectedVehicle);
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            stopList: res.data
          });
        }
      },
      onSearch(e: any) {
        const keyword = e.detail.value;
        const list: any[] = [];
        const Regex = new RegExp(keyword, 'gi');
        this.stopList.forEach((v: any) => {
          if (Regex.test(v.stopName)) {
            const nodes = v.stopName.replace(Regex, '+').split('+');
            list.push({
              ...v,
              start: nodes[0],
              richTextNodes: [
                {
                  name: 'p',
                  attrs: {
                    style: 'color: #fa2c19;'
                  },
                  children: [
                    {
                      type: 'text',
                      text: keyword
                    }
                  ]
                }
              ],
              end: nodes[1]
            });
          }
        });
        this.setData({
          searchValue: keyword,
          filteredStopList: list
        });
      },
      onClear(e: any) {
        this.setData({
          searchValue: '',
          filteredStopList: []
        });
      },
      handleSelectStop(item: any) {
        this.setData({
          showDialog: true,
          selectedStopInfo: item,
          openType: OpenType.STOP_POINT
        });
      },
      handleAnyDrive() {
        if (this.anyDriveInfo.lat) {
          this.setData({
            showDialog: true,
            openType: OpenType.ANY_DRIVE
          });
        }
      },
      closeDialog() {
        if (this.openType == OpenType.STOP_POINT) {
          this.setData({
            showDialog: false,
            selectedStopInfo: {}
          });
        } else {
          this.setData({
            showDialog: false
          });
        }
      },
      confirmDialog() {
        if (this.openType === OpenType.STOP_POINT) {
          const stopInfo = this.selectedStopInfo;
          this.setData({
            selectedStopInfo: {},
            showDialog: false
          });
          wx.navigateTo({
            url: '/pages/workbench/resultPage',
            events: {
              resultPageChannel: (data: any) => {
                console.log(data);
              }
            },
            success: (res: any) => {
              res.eventChannel.emit('resultPageChannel', {
                selectedStopInfo: stopInfo,
                selectedVehicle: this.selectedVehicle,
                backUrl: this.backUrl,
                openType: OpenType.STOP_POINT
              });
            }
          });
        } else {
          const anyDriveInfo = this.anyDriveInfo;
          getApp().globalData.anyDriveInfo = null;
          this.setData({
            selectedStopInfo: {},
            showDialog: false
          });
          wx.navigateTo({
            url: '/pages/workbench/resultPage',
            events: {
              resultPageChannel: (data: any) => {
                console.log(data);
              }
            },
            success: (res: any) => {
              res.eventChannel.emit('resultPageChannel', {
                anyDriveInfo: anyDriveInfo,
                selectedVehicle: this.selectedVehicle,
                backUrl: this.backUrl,
                openType: OpenType.ANY_DRIVE
              });
            }
          });
        }
      }
    },
    lifetimes: {
      created: function () {
        const stopPointBtn = getPermissionById(PermissionKey.stopPointBtn);
        const anyDriveBtn = getPermissionById(PermissionKey.anyDriveBtn);
        if ((stopPointBtn && anyDriveBtn) || (stopPointBtn && !anyDriveBtn)) {
          this.getStopList();
          this.setData({
            activeKey: 'stop'
          });
        } else if (!stopPointBtn && anyDriveBtn) {
          this.setData({
            activeKey: 'anyDrive'
          });
        }
      },
      detached: function () {}
    }
  });
</script>

<style lang="scss" scoped>
  .top-tab-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    .divider {
      width: 1px;
      height: 16px;
      background: rgba(216, 216, 216, 1);
      margin: 0 16px 0 16px;
    }
    .selected-tab {
      width: 80px;
      height: 20px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(250, 44, 25, 1);
    }
    .unselected-tab {
      width: 80px;
      height: 20px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(128, 128, 128, 1);
    }
  }
  .choose-stop {
    .scroll-conatiner {
      height: 70vh;
    }
    .stop-item {
      display: block;
      height: 52px;
      line-height: 52px;
      margin-left: 16px;
      margin-right: 16px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      border-bottom: 1px solid rgba(234, 235, 244, 1);
      color: #23252b;
    }
    .rich-text {
      display: flex;
      height: 36px;
      line-height: 36px;
      margin-left: 16px;
      margin-right: 16px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      border-bottom: 1px solid rgba(234, 235, 244, 1);
      color: #23252b;
    }
  }
  .choose-any-stop {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 420px;
    padding-top: 18px;
    .anydrive-btn,
    .anydrive-btn-disabled {
      width: 343px;
      height: 40px;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      border-radius: 20px;
      color: white;
      text-align: center;
      line-height: 40px;
      margin-top: 22px;
    }
    .anydrive-btn-disabled {
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 0.465) 0%,
        rgba(250, 89, 25, 0.523) 16.59259259%,
        rgba(250, 62, 25, 0.515) 55.40740741%,
        rgba(250, 44, 25, 0.508) 100%
      );
    }
    .any-stop-wrapper {
      height: 90px;
      padding: 0 12px 0 10px;
      display: flex;
      width: 90%;
      justify-content: center;
      align-items: center;
      .empty-status {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(128, 128, 128, 1);
      }
      .choose-point-btn {
        width: 64px;
        height: 24px;
        border: 1px solid rgba(250, 44, 25, 1);
        border-radius: 20px;
        position: absolute;
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(250, 44, 25, 1);
        text-align: center;
        line-height: 24px;
        top: 10px;
        right: 8px;
      }
      .label-stopname {
        height: 16px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(35, 37, 43, 1);
        position: absolute;
        top: 10px;
        left: 10px;
      }
      .anyDrive-info {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        height: 80px;
        padding-bottom: 5px;
        .label-lon,
        .label-lat,
        .label-head {
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: normal;
          color: rgba(128, 128, 128, 1);
          display: flex;
          flex-direction: column;
        }
        .bottom-btns {
          display: flex;
          width: 100%;
          margin-top: 40px;
        }
      }
    }
  }
  .dialog-content {
    .vehicle-num {
      display: flex;
    }
    .vehicle-container {
      background: rgba(245, 245, 245, 1);
      border-radius: 16px;
      padding-left: 8px;
      padding-top: 8px;
      height: 116px;
      .row-conatiner {
        display: flex;
        flex-wrap: wrap;
      }
      .vehicle {
        margin-right: 8px;
        margin-bottom: 8px;
        height: 28px;
        width: 82px;
        line-height: 28px;
        text-align: center;
        background: rgba(255, 255, 255, 1);
        border-radius: 16px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "search-bar": "shared/ui/searchBar.mpx",
      "t-dialog": "tdesign-miniprogram/dialog/dialog",
      "t-divider": "tdesign-miniprogram/divider/divider",
      "t-button": "tdesign-miniprogram/button/button"
    }
  }
</script>
