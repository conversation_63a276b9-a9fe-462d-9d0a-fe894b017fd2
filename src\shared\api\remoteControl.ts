import * as dayjs from 'dayjs';
import { doRequest } from './fetch';

class RemoteControlApi {
  public async openControl(vehicleName: string) {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/remoteControl/open',
      method: 'POST',
      data: {
        vehicleName,
        timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
    };
    return doRequest(requestOptions);
  }

  public async closeControl(vehicleName: string) {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/remoteControl/quit',
      method: 'POST',
      data: {
        vehicleName,
        timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
    };
    return doRequest(requestOptions);
  }

  public async brake(vehicleName: string) {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/remoteControl/brake',
      method: 'POST',
      data: {
        vehicleName,
        timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
    };
    return doRequest(requestOptions);
  }

  /**
   * 声音鸣笛
   */
  public async whistle(vehicleName: string) {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/command/whistle',
      method: 'POST',
      data: {
        vehicleName
      }
    };
    return doRequest(requestOptions);
  }

  /**
   * 声音播报
   */
  public async broadcast(content: string, vehicleName: string) {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/command/broadCast',
      method: 'POST',
      data: {
        voiceMsg: content,
        rate: 1,
        vehicleName
      }
    };
    return doRequest(requestOptions);
  }

  public async getVehiclePosition(vehicleName: string) {
    const requestOptions: RequestOptions = {
      url: '/mobile/applet/common/getVehicleInfo',
      method: 'POST',
      data: {
        vehicleName
      }
    };
    return doRequest(requestOptions);
  }
}

// 两点之间的距离，返回距离，单位为米
const getDistanceInMeters = (lat1: any, lon1: any, lat2: any, lon2: any) => {
  const R = 6371; // 地球平均半径，单位为公里
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c;
  return d * 1000;
};
// 将角度转换为弧度
const deg2rad = (deg: number) => {
  return deg * (Math.PI / 180);
};
export { getDistanceInMeters };
export default RemoteControlApi;
