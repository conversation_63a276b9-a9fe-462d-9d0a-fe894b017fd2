import { doRequest } from 'shared/api/fetch';
export class SoftWareIssue {
  async issueReport(params: {
    vehicleName: string;
    startTime: string;
    endTime: string;
    description: string;
    attachmentList: {
      type: string;
      fileKey: string;
    }[];
  }) {
    const requestOptions = {
      method: 'POST',
      url: '/ticket/operate/bug-report',
      data: params
    };
    return doRequest(requestOptions);
  }
}
