<template>
  <view class="grid-list">
    <view wx:for="{{colList}}" wx:key="index">
      <view class="row_{{index}}">
        <view
          class="case-row"
          wx:for="{{item}}"
          wx:for-item="col"
          wx:key="gridNo"
          wx:class="{{{select: col.select}}}"
          style="width:{{col.screenW}}px;height:{{col.screenH}}px;top:{{col.top}}px;left:{{col.left}}px;"
          bindtap="handleOpenGrid(col)"
        >
          {{ col.gridNo }}
          <image wx:if="{{col.select}}" src="{{WorkBenchUrl.GridSelect}}" />
        </view>
      </view>
    </view>
  </view>
</template>
<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { cloneDeep } from 'lodash';
  import { WorkBenchUrl } from 'shared/assets/imageUrl';
  createComponent<any>({
    data: { WorkBenchUrl, colList: [] },
    properties: {
      gridList: {
        type: Array,
        value: []
      },
      boxColumnCount: {
        type: Number,
        value: 0
      },
      caseSize: {
        type: Object,
        value: {}
      },
      selectGrid: {
        type: Array,
        value: []
      }
    },
    watch: {
      gridList: {
        handler(val, old) {
          if (val?.length > 0) {
            const { w, h } = this.formatScreenWH();
            const arr = this.formatGridDataToScreen(w, h);
            this.setData({
              colList: arr
            });
          }
        },
        immediate: true
      },
      selectGrid: {
        handler(val, old) {
          if (val?.length > 0) {
            const list = cloneDeep(this.colList);
            this.setData({
              colList: list.map((l: any) => {
                return l.map((v: any) => {
                  return {
                    ...v,
                    select: val.includes(v.realNo)
                  };
                });
              })
            });
          }
        },
        immediate: true
      }
    },
    methods: {
      formatScreenWH() {
        let w: number = 0; // 所有列的总宽度
        let h: number = 0; // 所有货箱真实高度
        h = this.gridList.reduce((p: any, c: any) => {
          return p + c.height;
        }, 0);
        w = this.gridList[0].width * this.boxColumnCount;
        return {
          w,
          h
        };
      },
      formatGridDataToScreen(realW: number, realH: number) {
        if (!this.boxColumnCount) {
          return [];
        }
        const _gridList = cloneDeep(this.gridList);
        const colRealH = Math.ceil(realH / this.boxColumnCount); // 每一列真实高度
        const _screenW = Number(
          (this.caseSize?.width / this.boxColumnCount)?.toFixed(2)
        );
        const allColData: any[] = [];
        let curColData = [];
        let curH = 0;
        for (let v of _gridList) {
          if (curH + v.height <= colRealH) {
            curH += v.height;
            const topGrid: any =
              curColData.length > 0 && curColData[curColData.length - 1];

            const gridInfo = {
              ...v,
              gridNo: String(v.gridNo).length < 2 ? '0' + v.gridNo : v.gridNo,
              realNo: v.gridNo,
              screenH: Number(
                ((v.height * this.caseSize?.height) / colRealH)?.toFixed(2)
              ),
              screenW: _screenW,
              left: topGrid ? topGrid.left : 0,
              top: topGrid ? topGrid.screenH + topGrid.top + 4 : 0
            };
            curColData.push(gridInfo);
          } else {
            allColData.push(curColData);
            curH = v.height;
            const leftCol: any =
              allColData[allColData.length - 1] &&
              allColData[allColData.length - 1][0];

            const gridInfo = {
              ...v,
              gridNo: String(v.gridNo).length < 2 ? '0' + v.gridNo : v.gridNo,
              realNo: v.gridNo,
              screenH: Number(
                ((v.height * this.caseSize?.height) / colRealH)?.toFixed(2)
              ),
              screenW: _screenW,
              left: leftCol ? leftCol.screenW + leftCol.left + 4 : 0,
              top: 0
            };
            curColData = [gridInfo];
          }
        }
        allColData.push(curColData);
        return allColData;
      },
      handleOpenGrid(val: any) {
        this.triggerEvent('openGrid', { gridNo: val.realNo });
      }
    }
  });
</script>
<style lang="scss" scoped>
  .grid-list {
    position: relative;
    width: 340px;
    height: 222px;
    margin: 0 auto;
    display: flex;
    margin-bottom: 16px;
    .case-row {
      position: absolute;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      font-family: JDLangZhengTi;
      font-weight: bold;
      color: rgba(26, 26, 26, 1);
      &.select {
        background: rgba(255, 241, 240, 1);
        border: 2px solid rgba(250, 44, 25, 1);
        border-radius: 8px;
        box-shadow: 0 0 8px 0 rgba(250, 44, 25, 0.3);
      }

      image {
        width: 20px;
        height: 20px;
        position: absolute;
        right: -1px;
        top: -1px;
      }
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true
  };
</script>
