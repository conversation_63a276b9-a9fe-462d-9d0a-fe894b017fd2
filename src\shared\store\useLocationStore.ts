import { defineStore } from '@mpxjs/pinia';
import { ref, computed } from '@mpxjs/core';
const useLocationStore = defineStore('location', () => {
  const initLocation = wx.getStorageSync('userLocation');
  const initAddress = wx.getStorageSync('userAddress');
  const userLocation = ref<any>(initLocation);
  const userAddress = ref<string>(initAddress);
  const setLocation = (newLocation: any) => {
    userLocation.value = newLocation;
  };
  const setAddress = (newAddress: string) => {
    userAddress.value = newAddress;
  };
  const getLocation = computed(() => {
    return userLocation.value;
  });
  const getAddress = computed(() => {
    return userAddress.value;
  });
  return {
    userLocation,
    userAddress,
    setLocation,
    setAddress,
    getLocation,
    getAddress
  };
});
export default useLocationStore;
