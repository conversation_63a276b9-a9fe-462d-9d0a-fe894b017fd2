<block wx:for="{{nodes.children}}" wx:for-index="i" wx:for-item="item" wx:key="i"><block wx:if="{{item.tag===undefined}}">{{item.text}}</block><block wx:if="{{item.tag==='view'}}"><block wx:if="{{item.rely}}"><view data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></view></block><block wx:else><view class="h2w__viewParent"><view data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></view></view></block></block><block wx:if="{{item.tag==='table'}}"><table data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='todogroup'}}"><todogroup data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='img'}}"><img data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block></block>