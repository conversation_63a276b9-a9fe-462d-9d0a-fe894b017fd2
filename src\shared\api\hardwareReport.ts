import { doRequest } from 'shared/api/fetch';
export class HardWareReport {
  async getHardwareType() {
    const requestOptions = {
      method: 'POST',
      url: '/k2/management/mobile/require/get_hardware_type'
    };
    return doRequest(requestOptions);
  }
  async addVehicleRequire(param: {
    vehicleName: string;
    requireHardwareTypeIds: string;
    title: string;
    isInfluenceOperation: number;
    description: string;
    pictureList: any[];
    video: any;
  }) {
    const requestOptions = {
      method: 'POST',
      url: '/k2/management/mobile/require/add_vehicle_require',
      data: param
    };
    return doRequest(requestOptions);
  }
}
