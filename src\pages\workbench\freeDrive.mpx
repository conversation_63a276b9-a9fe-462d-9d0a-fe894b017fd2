<template>
  <navi-bar
    show-address="{{true}}"
    title="自由跑行"
    showReturn="true"
    bindsearchlocation="openSearchLocation"
    backUrl="{{backUrl}}"
  ></navi-bar>
  <map
    id="myMap"
    scale="{{scale}}"
    latitude="{{mapCenter.latitude}}"
    longitude="{{mapCenter.longitude}}"
    markers="{{markers}}"
    bindmarkertap="handleClickMarker"
    bindregionchange="changeRegion"
    bindlabeltap="handleClickMarker"
    show-location="{{true}}"
  ></map>

  <view
    class="vehicle-list-container"
    style="height: {{height}}px; background: url({{VehicleUrl.PanSheetBg}}) no-repeat top/100%"
  >
    <!-- 顶部不参与手势协商，单独控制 -->
    <pan-gesture-handler worklet:ongesture="handlePan" style="flex-shrink: 0">
      <view class="header-container" bind:touchend="handleTouchEnd">
        <view
          class="position"
          bindtap="moveToUser"
          style="background: url({{SingleVehicle.PositionIcon}}) no-repeat center/100%; width: 40px; height: 40px;"
        ></view>
        <view class="handler"></view>

        <view
          wx:if="{{curSection === 'vehicleList'}}"
          wx:class="header vehicle-list-header"
        >
          <view class="header-title"
            >已选择车辆({{ selectedVehicle.length }})</view
          >
          <image
            wx:if="{{selectedVehicle.length > 0}}"
            src="{{WorkBenchUrl.DelIcon}}"
            bindtap="clear"
          >
          </image
        ></view>

        <view
          wx:if="{{curSection === 'stopList'}}"
          wx:class="header stop-list-header"
        >
          <image src="{{FreeDriveUrl.MiniNavBack}}" bindtap="back"> </image>
          <view class="header-title" bindtap="back">选择目的地</view>
        </view>
      </view>
    </pan-gesture-handler>
    <!-- 滚动区要与 pan 手势协商 -->
    <pan-gesture-handler
      id="pan"
      worklet:should-response-on-move="shouldPanResponse"
      simultaneousHandlers="{{['scroll']}}"
      worklet:ongesture="handlePan"
    >
      <vertical-drag-gesture-handler
        id="scroll"
        native-view="scroll-view"
        worklet:should-response-on-move="shouldScrollViewResponse"
        simultaneousHandlers="{{['pan']}}"
      >
        <scroll-view
          class="vehicle-list"
          scroll-y
          worklet:adjust-deceleration-velocity="adjustDecelerationVelocity"
          worklet:onscrollupdate="handleScroll"
          type="list"
          show-scrollbar="{{false}}"
        >
          <vehicle-list
            wx:if="{{curSection === 'vehicleList'}}"
            selectedVehicle="{{selectedVehicle}}"
            location="{{mapCenter.latitude}},{{mapCenter.longitude}}"
            bind:clickVehicleItem="handleClickVehicle"
            bind:changeSection="changeSection"
            bind:clickSearch="handleShowSearchVehicle"
          />

          <stop-list
            wx:if="{{curSection === 'stopList'}}"
            backUrl="{{backUrl}}"
            selectedVehicle="{{selectedVehicle}}"
            anyDriveInfo="{{anyDriveInfo}}"
          />
          <view class="safe-area-inset-bottom"></view>
        </scroll-view>
      </vertical-drag-gesture-handler>
      <view class="select-stop" wx:if="{{curSection === 'vehicleList'}}"
        ><view
          class="btn"
          bindtap="changeSection('stopList')"
          wx:class="{{ {enable: selectedVehicle.length > 0} }}"
          >选择目的地</view
        ></view
      >
    </pan-gesture-handler>
  </view>
  <search-vehicle
    visible="{{showSearchVehicle}}"
    bindclosepopup="closeSearchVehicle"
    bindselectvehicle="handleSelectSearchVehicle"
  >
  </search-vehicle>
  <search-location
    visible="{{showSearchLocation}}"
    bindclosepopup="closeSearchLocation"
  ></search-location>
</template>

<script>
  import { createPage } from '@mpxjs/core';
  import { FreeDriveApi } from 'shared/api/freeDrive';
  import { HTTPSTATUSCODE } from '../../shared/api/fetch';
  import {
    WorkBenchUrl,
    SingleVehicle,
    RemoteControlUrl,
    FreeDriveUrl,
    VehicleUrl
  } from 'shared/assets/imageUrl';
  import useLocationStore from 'shared/store/useLocationStore';
  import { mapStores } from '@mpxjs/pinia';
  import { CheckVehicle, CheckType } from 'shared/utils/checkVehicle';
  import { SystemStatus } from 'shared/utils/constant';

  function clamp(val, min, max) {
    'worklet';
    return Math.min(Math.max(val, min), max);
  }

  const { shared, timing } = wx.worklet;

  const GestureState = {
    POSSIBLE: 0, // 0 此时手势未识别，如 panDown等
    BEGIN: 1, // 1 手势已识别
    ACTIVE: 2, // 2 连续手势活跃状态
    END: 3, // 3 手势终止
    CANCELLED: 4 // 4 手势取消，
  };

  const { screenHeight, statusBarHeight, safeArea } = wx.getSystemInfoSync();
  const colorMap = {
    [SystemStatus.NORMAL]: '#52B066',
    [SystemStatus.ABNORMAL]: '#E6432E',
    [SystemStatus.OFFLINE]: '#808080'
  };
  const deviceInfo = wx.getDeviceInfo();
  const isIOS = deviceInfo.system.includes('iOS');
  const VehicleCluster = require('shared/assets/vehicle-cluster.png');
  createPage({
    data: {
      WorkBenchUrl,
      SingleVehicle,
      VehicleUrl,
      RemoteControlUrl,
      FreeDriveUrl,
      fetchApi: new FreeDriveApi(),
      scale: 16,
      selectedVehicle: [],
      mapVehicleList: [],
      curSection: 'vehicleList', //  'vehicleList'  'stopList'
      latitude: null,
      longitude: null,
      showSearchVehicle: false,
      markers: [],
      backUrl: null,
      showSearchLocation: false,
      anyDriveInfo: {
        lat: 0,
        lon: 0,
        head: 0,
        stopName: ''
      }
    },
    computed: {
      ...mapStores(useLocationStore),
      mapCenter() {
        if (this.locationStore.getLocation) {
          return this.locationStore.getLocation;
        } else {
          const initLocation = wx.getStorageSync('userLocation');
          return initLocation;
        }
      }
    },
    lifetimes: {
      created() {
        this.transY = shared(1000);
        this.scrollTop = shared(0);
        this.startPan = shared(true);
        this.initTransY = shared(0); // 留言半屏的初始位置
        this.upward = shared(false);
      },
      attached() {
        this.setData({
          height: screenHeight - statusBarHeight
        });
      },
      ready() {
        const query = this.createSelectorQuery();
        // ready 生命周期里才能获取到首屏的布局信息
        query.select('.header-container').boundingClientRect();
        query.exec(res => {
          this.transY.value = statusBarHeight + 50;
          this.initTransY.value =
            screenHeight - res[0].height - (screenHeight - safeArea.bottom);
        });
        // 通过 transY 一个 SharedValue 控制半屏的位置
        this.applyAnimatedStyle('.vehicle-list-container', () => {
          'worklet';
          return { transform: `translateY(${this.transY.value}px)` };
        });
      }
    },
    onLoad(options) {
      console.log(options);
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
      this.setData({
        backUrl: options.backUrl
      });
      if (options.vehicleName) {
        this.setData({
          selectedVehicle: [options.vehicleName]
        });
      }
      this.mapCtx = wx.createMapContext('myMap');
      this.bindEvent();
    },
    onShow() {
      this.getMapVehicleList();
      if (getApp().globalData.anyDriveInfo) {
        // console.log(getApp().globalData.anyDriveInfo);
        // console.log(this.selectedVehicle);
        this.setData({
          anyDriveInfo: getApp().globalData.anyDriveInfo
        });
      }
    },
    methods: {
      bindEvent() {
        this.mapCtx.initMarkerCluster({
          enableDefaultStyle: false,
          zoomOnClick: true,
          gridSize: 30
        });

        // enableDefaultStyle 为 true 时不会触发改事件
        this.mapCtx.on('markerClusterCreate', res => {
          console.log('clusterCreate', res);
          const clusters = res.clusters;
          const markers = clusters.map(cluster => {
            const { center, clusterId, markerIds } = cluster;
            return {
              ...center,
              width: 60,
              height: 60,
              clusterId, // 必须
              iconPath: VehicleCluster,
              alpha: 1,
              joinCluster: true,
              label: {
                content: markerIds.length + '',
                fontSize: 15,
                width: 20,
                height: 20,
                color: '#FA2C19FF',
                bgColor: '#fff',
                borderRadius: 10,
                textAlign: 'center',
                anchorX: isIOS ? 0 : -10,
                anchorY: -40
              }
            };
          });
          this.mapCtx.addMarkers({
            markers,
            clear: false
          });
        });
      },
      openSearchLocation(e) {
        this.setData({
          showSearchLocation: true
        });
      },
      closeSearchLocation() {
        this.setData({
          showSearchLocation: false
        });
      },
      moveToUser() {
        this.mapCtx.moveToLocation();
      },
      async getMapVehicleList() {
        let res;
        if (this.longitude && this.latitude) {
          res = await this.fetchApi.getMapVehicle(this.longitude, this.latitude);
        } else {
          res = await this.fetchApi.getMapVehicle(
            this.mapCenter.longitude,
            this.mapCenter.latitude
          );
        }
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            mapVehicleList: res.data
          });
          this.formatMarkers(res.data);
        }
      },
      handleClickVehicle(e) {
        const vehicleName = e.detail.vehicleName;
        if (this.selectedVehicle.includes(vehicleName)) {
          this.setData({
            selectedVehicle: this.selectedVehicle.filter(v => v !== vehicleName)
          });
          this.formatMarkers(this.mapVehicleList);
          return;
        }
        this.changeSelectVehicle(
          vehicleName,
          this.formatMarkers.bind(this, this.mapVehicleList)
        );
      },
      handleSelectSearchVehicle(e) {
        const vehicleName = e.detail.vehicleName;
        const check = new CheckVehicle(
          [{ checkType: CheckType.TaskRepairOrder }],
          vehicleName
        );
        check.checkVehicleFunc().then(res => {
          if (res) {
            if (!this.selectedVehicle.includes(vehicleName)) {
              this.setData({
                selectedVehicle: [vehicleName].concat(this.selectedVehicle)
              });
            }
            this.closeSearchVehicle();
            this.formatMarkers(this.mapVehicleList);
          }
        });
      },
      handleClickMarker(e) {
        const vehicleName = e.detail.markerId;
        if (this.selectedVehicle.includes(vehicleName)) {
          this.setData({
            selectedVehicle: this.selectedVehicle.filter(v => v !== vehicleName)
          });
          this.formatMarkers(this.mapVehicleList);
          return;
        }
        this.changeSelectVehicle(
          vehicleName,
          this.formatMarkers.bind(this, this.mapVehicleList)
        );
      },
      changeSelectVehicle(vehicleName, selectCB) {
        const check = new CheckVehicle(
          [{ checkType: CheckType.TaskRepairOrder }],
          vehicleName
        );
        check.checkVehicleFunc().then(res => {
          if (res) {
            this.setData({
              selectedVehicle: [vehicleName].concat(this.selectedVehicle)
            });
            selectCB && selectCB();
          }
        });
      },
      changeSection(e) {
        if (this.selectedVehicle.length <= 0) {
          return;
        }
        this.setData({
          curSection: e.detail ? e.detail.val : e
        });
      },
      handleShowSearchVehicle() {
        this.setData({
          showSearchVehicle: true
        });
      },
      closeSearchVehicle(e) {
        this.setData({
          showSearchVehicle: false
        });
      },
      clear() {
        wx.showModal({
          content: '是否确定清空已选车辆？',
          confirmText: '确定清空',
          success: res => {
            if (res.confirm) {
              this.setData({
                selectedVehicle: []
              });
              this.formatMarkers(this.mapVehicleList);
            } else if (res.cancel) {
              //
            }
          }
        });
      },
      back() {
        this.setData({
          curSection: 'vehicleList',
          anyDriveInfo: {
            lat: 0,
            lon: 0,
            head: 0,
            stopName: ''
          }
        });
      },
      changeRegion(e) {
        if (e.type === 'end' && e.causedBy === 'drag') {
          this.mapCtx.getCenterLocation({
            success: res => {
              this.getMapVehicleList();
              this.setData({
                latitude: res.latitude,
                longitude: res.longitude
              });
            }
          });
        }
      },
      getVehicleIcon(vehicleInfo) {
        if (vehicleInfo.systemStatus === SystemStatus.OFFLINE) {
          if (this.selectedVehicle.indexOf(vehicleInfo.vehicleName) > -1) {
            return this.FreeDriveUrl.OfflineSelectedVehicleIcon;
          } else {
            return this.FreeDriveUrl.OfflineNotSelect;
          }
        } else {
          if (this.selectedVehicle.indexOf(vehicleInfo.vehicleName) > -1) {
            return this.FreeDriveUrl.SelectVehicleIcon;
          } else {
            return this.FreeDriveUrl.OnlineNotSelect;
          }
        }
      },
      formatMarkers(list) {
        this.setData({
          markers: list.map(v => {
            return {
              id: v.vehicleName,
              label: {
                content: v.vehicleName,
                padding: 4,
                borderRadius: 4,
                display: 'ALWAYS',
                color: colorMap[v.systemStatus],
                fontSize: 14,
                borderColor: colorMap[v.systemStatus],
                borderWidth: 2,
                textAlign: 'center',
                anchorX: isIOS ? 0 : -30,
                anchorY:
                  this.selectedVehicle.indexOf(v.vehicleName) > -1 ? -75 : -52,
                bgColor: '#FFFFFF'
              },
              joinCluster: true,
              latitude: v.latitude,
              longitude: v.longitude,
              iconPath: this.getVehicleIcon(v),
              width:
                this.selectedVehicle.indexOf(v.vehicleName) > -1
                  ? '52px'
                  : '38px',
              height:
                this.selectedVehicle.indexOf(v.vehicleName) > -1
                  ? '56px'
                  : '30px',
              alpha: 1
            };
          })
        });
      },
      scrollTo(toValue) {
        'worklet';

        this.transY.value = timing(toValue, { duration: 200 });
      },
      // shouldPanResponse 和 shouldScrollViewResponse 用于 pan 手势和 scroll-view 滚动手势的协商
      shouldPanResponse() {
        'worklet';
        return this.startPan.value;
      },
      shouldScrollViewResponse(pointerEvent) {
        'worklet';
        // transY > 0 说明 pan 手势在移动半屏，此时滚动不应生效
        if (this.transY.value > statusBarHeight) return false;

        const scrollTop = this.scrollTop.value;
        const { deltaY } = pointerEvent;
        // deltaY > 0 是往上滚动，scrollTop <= 0 是滚动到顶部边界，此时 pan 开始生效，滚动不生效
        const result = scrollTop <= 0 && deltaY > 0;
        this.startPan.value = result;
        return !result;
      },
      // 处理拖动半屏的手势
      handlePan(gestureEvent) {
        'worklet';
        // 滚动半屏的位置
        if (gestureEvent.state === GestureState.ACTIVE) {
          // deltaY < 0，往上滑动
          this.upward.value = gestureEvent.deltaY < 0;
          // 当前半屏位置
          const curPosition = this.transY.value;
          // 只能在 [statusBarHeight, screenHeight] 之间移动
          const destination = clamp(
            curPosition + gestureEvent.deltaY,
            statusBarHeight,
            screenHeight
          );
          if (curPosition === destination) return;
          // 改变 transY，来改变半屏的位置
          this.transY.value = destination;
        }

        if (
          gestureEvent.state === GestureState.END ||
          gestureEvent.state === GestureState.CANCELLED
        ) {
          if (this.transY.value <= screenHeight / 2) {
            // 在上面的位置
            if (this.upward.value) {
              this.scrollTo(statusBarHeight + 50);
            } else if (this.transY.value !== statusBarHeight + 50) {
              this.scrollTo(screenHeight / 2);
            }
          } else if (
            this.transY.value > screenHeight / 2 &&
            this.transY.value <= this.initTransY.value
          ) {
            // 在中间位置的时候
            if (this.upward.value) {
              this.scrollTo(screenHeight / 2);
            } else {
              this.scrollTo(this.initTransY.value);
            }
          } else {
            // 在最下面的位置
            this.scrollTo(this.initTransY.value);
          }
        }
      },
      adjustDecelerationVelocity(velocity) {
        'worklet';
        const scrollTop = this.scrollTop.value;
        return scrollTop <= 0 ? 0 : velocity;
      },
      handleScroll(evt) {
        'worklet';
        this.scrollTop.value = evt.detail.scrollTop;
      },
      // 简单兼容 WebView
      handleTouchEnd() {
        if (this.renderer === 'skyline') {
          return;
        }
        if (this.transY.value === statusBarHeight) {
          this.lastTransY = statusBarHeight;
          this.scrollTo(screenHeight / 2);
        } else if (
          this.transY.value === screenHeight / 2 &&
          this.lastTransY === statusBarHeight
        ) {
          this.lastTransY = screenHeight / 2;
          this.scrollTo(this.initTransY.value);
        } else if (this.transY.value === this.initTransY.value) {
          this.lastTransY = this.initTransY.value;
          this.scrollTo(screenHeight / 2);
        } else if (
          this.transY.value === screenHeight / 2 &&
          this.lastTransY === this.initTransY.value
        ) {
          this.scrollTo(statusBarHeight);
        }
      }
    }
  });
</script>

<script name="json">
  module.exports = {
    usingComponents: {
      'navi-bar': '../../shared/ui/naviBar.mpx',
      'vehicle-list': 'widgets/freeDrive/vehicleList.mpx',
      'stop-list': 'widgets/freeDrive/stopList.mpx',
      'search-vehicle': 'widgets/vehicle/searchVehicle.mpx',
      'search-location': 'widgets/searchLocation.mpx'
    },
    renderer: 'skyline',
    componentFramework: 'glass-easel',
    disableScroll: true,
    navigationStyle: 'custom'
  };
</script>

<style lang="scss">
  page {
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100vh;
    color: #1a191e;
  }
  page,
  view {
    box-sizing: border-box;
  }
  pan-gesture-handler,
  vertical-drag-gesture-handler {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .container {
    flex: 1;
    width: 100vw;
    overflow: hidden;
  }
  .container image {
    width: 100vw;
  }

  #myMap {
    width: 100vw;
    height: 100vh;
  }

  .open-comment {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;
    background-color: white;
  }
  .open-comment-wording {
    height: 66px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .safe-area-inset-bottom {
    height: env(safe-area-inset-bottom);
  }

  .vehicle-list-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    z-index: 999;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  }
  .header-container {
    font-size: 16px;
    text-align: center;
    padding: 15px 16px;
    .position {
      position: absolute;
      top: -50px;
      right: 10px;
    }
    .header {
      display: flex;
      &.vehicle-list-header {
        justify-content: space-between;
        .header-title {
          font-weight: 500;
        }
        image {
          height: 20px;
          width: 20px;
        }
      }
      align-items: center;

      &.stop-list-header {
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(26, 26, 26, 1);
        image {
          width: 18px;
          height: 18px;
          margin-right: 8px;
        }
      }
    }
  }
  .handler {
    width: 40px;
    height: 4px;
    margin: 0 auto 10px;
    background: rgba(186, 190, 199, 1);
    border-radius: 4px;
  }
  .vehicle-list {
    flex: 1;
    overflow: hidden;
  }
  .comment-item {
    padding: 0 20px 20px;
    font-size: 13px;
    line-height: 1.4;
  }
  .main-comment,
  .sub-comment {
    display: flex;
    flex-direction: row;
  }
  .sub-comment {
    padding: 10px 22px 0;
  }

  .user-head-img {
    width: 33px;
    height: 33px;
    border-radius: 50%;
    margin-top: 5px;
  }
  .others {
    flex: 1;
    margin-left: 10px;
  }
  .user-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .content {
    margin-top: 2px;
  }

  .select-stop {
    width: 100%;
    height: 150px;
    padding: 8px 18px 9px 18px;
    background-color: white;
    .btn {
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      border-radius: 20px;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 0.5) 0%,
        rgba(250, 89, 25, 0.5) 16.59259259%,
        rgba(250, 63, 25, 0.5) 55.40740741%,
        rgba(250, 44, 25, 0.5) 100%
      );
      &.enable {
        background: linear-gradient(
          135deg,
          rgba(250, 100, 25, 1) 0%,
          rgba(250, 89, 25, 1) 16.59259259%,
          rgba(250, 63, 25, 1) 55.40740741%,
          rgba(250, 44, 25, 1) 100%
        );
      }
    }
  }
</style>
