
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="呼叫远驾"
    backUrl="/pages/workbench/index"
  ></navi-bar>

  <vehicle-list
    selectedVehicleName="{{selectedVehicleName}}"
    tip="选择车辆后，可呼叫远驾"
    bind:handleSelect="handleSelect"
  ></vehicle-list>

  <call-driver-operate
    vehicleName="{{selectedVehicleName}}"
    visible="{{modalVisible}}"
    bind:onVisibleChange="onVisibleChange"
  ></call-driver-operate>
</template>

<script  lang="ts">
  import { createPage } from '@mpxjs/core';

  createPage<any>({
    data: {
      selectedVehicleName: null,
      modalVisible: false
    },
    methods: {
      handleSelect(e: any) {
        this.selectedVehicleName = e.detail.val;
        if (e.detail.val) {
          this.setData({
            modalVisible: true
          });
        }
      },
      onVisibleChange() {
        this.setData({
          modalVisible: false
        });
      }
    },
    onLoad: function (options) {
      if (options.vehicleName) {
        this.setData({
          modalVisible: true,
          selectedVehicleName: options.vehicleName
        });
      }
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx",
      "call-driver-operate": "widgets/commandOperate/callDriverOperate"
    },
    "navigationStyle": "custom"
  }
</script>
