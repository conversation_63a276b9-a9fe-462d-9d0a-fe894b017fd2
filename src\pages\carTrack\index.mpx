<template>
  <navi-bar
    show-address="{{false}}"
    title="跟车模式"
    show-return="{{true}}"
    backUrl="{{backUrl}}"
  ></navi-bar>
  <view class="car-track-wrapper">
    <view class="vehicle-name"> {{ vehicleName }}</view>
    <view class="shadow-record-wrapper">
      <view class="title">提报记录</view>

      <scroll-view
        scroll-y="{{true}}"
        type="list"
        class="shadow-record-list"
        wx:if="{{shadowEventList.length > 0}}"
        ><view
          class="shadow-record-item"
          wx:for="{{shadowEventList}}"
          wx:key="id"
        >
          <view class="top">
            <text class="report-time">{{ item.reportTime }}</text>
            <text class="content">{{ item.content }}</text>
          </view>
          <view class="bottom">
            <text class="remark">{{ item.remark }}</text>
            <view class="border"></view>
          </view> </view
      ></scroll-view>
      <view wx:else class="empty-status">暂无影子记录</view>
      <view class="footer">
        <view
          wx:class="{{{'report-btn': !isControlStart, 'report-btn-disabled': isControlStart}}}"
          bindtap="openDialog"
          >上报车辆影子</view
        >
      </view>
    </view>
    <view class="command-area">
      <view class="contro-btn-area">
        <button
          plain="{{true}}"
          class="custom-btn"
          hover-class="custom-btn-hover"
          wx:if="{{!isControlStart}}"
          bindtap="startControl"
          disabled="{{startBtnDisabled}}"
        >
          <view
            style="
              background: url({{RemoteControlUrl.ControlStart}}) no-repeat
                center/100%;
            "
            class="custom-icon"
          ></view>
          开启遥控器
        </button>
        <button
          plain="{{true}}"
          class="custom-btn"
          hover-class="custom-btn-hover"
          wx:if="{{isControlStart}}"
          bindtap="endControl"
          disabled="{{endBtnDisabled}}"
        >
          <view
            style="
              background: url({{RemoteControlUrl.ControlEnd}}) no-repeat
                center/100%;
            "
            class="custom-icon"
          ></view>
          关闭遥控器
        </button>
      </view>
      <view class="command-btn-area">
        <common-switch
          control-start="{{isControlStart}}"
          checked="{{isForceControl}}"
          bindchangechecked="onChangeForceControl"
          bindopenforcecontrol="openForceControl"
        >
        </common-switch>
      </view>
    </view>
    <view class="delay-info-block">
      <delay-info
        wx:if="{{isControlStart}}"
        delay-info="{{responseData}}"
      ></delay-info
    ></view>
    <single-joystick
      angle="{{angle}}"
      speed="{{speed}}"
      isControlStart="{{isControlStart}}"
      bindgetangleandspeed="getAngleAndSpeed"
      bindstartwslistener="startWsListener"
      bindremovewslistener="removeWsListener"
      bindbrake="brake"
    >
    </single-joystick>
  </view>
  <report-modal
    show-dialog="{{showDialog}}"
    bindclosedialog="closeDialog"
    vehicle-name="{{vehicleName}}"
    bindrecordevent="onRecordEvent"
  >
  </report-modal>
</template>

<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import { RemoteControlUrl } from 'shared/assets/imageUrl';
  import { CommandApi } from 'shared/api/command';
  import * as dayjs from 'dayjs';
  import {
    REMOTE_COMMAND,
    REMOTE_CONTROL,
    WSReadyState,
    COMMAND_TYPE
  } from 'shared/utils/constant';
  import WebSocketClient from 'shared/api/websocket';
  import RemoteControlApi, {
    getDistanceInMeters
  } from 'shared/api/remoteControl';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { getRemoteControlUrl } from 'shared/utils/config';
  let requestId = 0;
  const coefficient = 0.04;
  createPage({
    data: {
      RemoteControlUrl,
      fetchApi: new RemoteControlApi(),
      commanApi: new CommandApi(),
      vehicleName: '',
      backUrl: '',
      showDialog: false,
      shadowEventList: [] as any[],
      isControlStart: false,
      isForceControl: false,
      WSReadyState,
      timeUpdater: null as any,
      wsClient: null as any,
      wsTimer: null as any,
      wsListener: null as any,
      responseData: null as any,
      commandType: COMMAND_TYPE.MOBILE,
      getDistanceInMeters,
      angle: 0,
      speed: 0,
      cv: 0,
      startBtnDisabled: false,
      endBtnDisabled: false
    },
    onLoad(query: any) {
      console.log(query);
      if (query?.vehicleName) {
        this.setData({
          vehicleName: query.vehicleName,
          backUrl: query.backUrl
        });
      }
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    onHide() {
      this.removeWsListener({});
      if (this.vehicleName && this.wsClient) {
        this.brake();
        this.recovery(this.vehicleName);
      }
    },
    onUnload() {
      this.removeWsListener({});
      if (this.vehicleName && this.wsClient) {
        this.brake();
        this.recovery(this.vehicleName);
      }
    },
    methods: {
      // ws 60s无操作监听
      startWsListener(e: any) {
        this.wsListener && clearTimeout(this.wsListener);
        if (this.wsClient) {
          this.wsListener = setTimeout(() => {
            this.endControl();
          }, 60000);
        }
      },
      removeWsListener(e: any) {
        this.wsListener && clearTimeout(this.wsListener);
      },
      getAngleAndSpeed(e: any) {
        this.setData({
          angle: e.detail.angle,
          speed: e.detail.speed
        });
      },
      openDialog(e: any) {
        if (!this.isControlStart) {
          this.setData({ showDialog: true });
        }
      },

      closeDialog() {
        this.setData({ showDialog: false });
      },
      onRecordEvent(e: any) {
        console.log(e);
        this.setData({
          shadowEventList: [e.detail.eventInfo, ...this.shadowEventList]
        });
      },
      // main方法：下发角度、速度
      formatSendMessage() {
        requestId += 1;
        const tv = this.speed;
        if (tv === 0 || (tv > 0 && this.cv < 0) || (tv < 0 && this.cv > 0)) {
          this.setData({
            cv: 0
          });
          const data = {
            eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
            data: {
              id: requestId,
              vehicleName: this.vehicleName,
              commandType: this.commandType,
              moduleName: 'supervisor',
              timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              targetVelocity: 0,
              targetAngle: this.angle
            }
          };
          this.wsClient.sendMessage(data);
        } else {
          if (tv - this.cv > 0) {
            const speed = this.cv + coefficient;

            if (speed <= tv) {
              this.setData({
                cv: speed
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: speed,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            } else {
              this.setData({
                cv: tv
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: tv,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            }
          } else if (tv - this.cv < 0) {
            const speed = this.cv - coefficient;
            if (speed >= tv) {
              this.setData({
                cv: speed
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: speed,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            } else {
              this.setData({
                cv: tv
              });
              const data = {
                eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
                data: {
                  id: requestId,
                  vehicleName: this.vehicleName,
                  commandType: this.commandType,
                  moduleName: 'supervisor',
                  timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                  targetVelocity: tv,
                  targetAngle: this.angle
                }
              };
              this.wsClient.sendMessage(data);
            }
          } else {
            this.setData({
              cv: tv
            });
            const data = {
              eventType: REMOTE_CONTROL.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL,
              data: {
                id: requestId,
                vehicleName: this.vehicleName,
                commandType: this.commandType,
                moduleName: 'supervisor',
                timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                targetVelocity: tv,
                targetAngle: this.angle
              }
            };
            this.wsClient.sendMessage(data);
          }
        }
      },
      // 监听ws创建成功
      handleOnOpen() {
        this.handleBroadcast('进入移动端摇车', this.vehicleName);
        this.wsTimer = setInterval(() => {
          this.formatSendMessage();
        }, 100);
      },
      // 远程语音播报
      handleBroadcast(value: string, vehicleName: string) {
        this.fetchApi.broadcast(value, this.vehicleName);
      },
      // 遥控超时，下发一次RESET
      handleRestControl() {
        requestId += 1;
        const data = {
          eventType: REMOTE_CONTROL.REMOTE_REQUEST_RESET_ABNORMAL_CONTROL,
          data: {
            id: requestId,
            vehicleName: this.vehicleName,
            commandType: 'MOBILE',
            moduleName: 'supervisor',
            timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
        };
        this.wsClient.sendMessage(data);
      },
      // 急停
      async stop(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        try {
          const res: any = await this.fetchApi.openControl(vehicleName);
          if (res.code === HTTPSTATUSCODE.Success) {
            const userName = wx.getStorageSync('userName');
            // 建立长链
            if (!this.wsClient && userName) {
              const wsUrl = `${getRemoteControlUrl()}/minimonitor/client/ws/mobile/control?userName=${userName}`;
              const token = wx.getStorageSync('JD_AUTH_TOKEN');
              this.wsClient = new WebSocketClient(
                wsUrl,
                {
                  onOpen: (data: any) => {
                    this.handleOnOpen();
                    console.log('ws opened');
                  },
                  onClose: this.onClose,
                  onError: this.onError,
                  onMessage: (data: any) => {
                    this.onMessage(data);
                  },
                  initMessage: {
                    eventType: 'REMOTE_REQUEST_ENTER_CONTROL',
                    data: {
                      vehicleName: this.vehicleName,
                      timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
                    }
                  }
                },
                token
              );
            }
            this.setData({
              isControlStart: true,
              startBtnDisabled: false
            });
            wx.hideLoading();
          } else {
            wx.hideLoading();
            wx.showToast({
              title: res.message,
              icon: 'none',
              duration: 3000
            });
            this.setData({
              startBtnDisabled: false
            });
          }
        } catch (err) {
          console.error(err);
          this.setData({
            startBtnDisabled: false
          });
        }
      },
      // 恢复
      async recovery(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        this.setData({
          endBtnDisabled: true
        });
        try {
          this.wsTimer && clearInterval(this.wsTimer);
          const res: any = await this.fetchApi.closeControl(vehicleName);
          // 断开长链
          this.wsClient &&
            this.wsClient.sendMessage({
              eventType: 'REMOTE_REQUEST_QUIT_CONTROL',
              data: {
                vehicleName: this.vehicleName,
                timeStamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
              }
            });
          this.wsClient && this.wsClient.closeSocket();
          this.wsClient = null;
          this.wsTimer = null;
          this.setData({
            isControlStart: false,
            commandType: COMMAND_TYPE.MOBILE,
            isForceControl: false,
            endBtnDisabled: false
          });
          if (res.code === HTTPSTATUSCODE.Success) {
            this.handleBroadcast('退出移动端摇车', this.vehicleName);
            wx.hideLoading();
          } else {
            wx.hideLoading();
            wx.showToast({
              title: res.message,
              icon: 'none',
              duration: 3000
            });
          }
        } catch (err) {
          this.setData({
            endBtnDisabled: false
          });
          console.error(err);
        }
      },
      // 急刹
      async brake() {
        try {
          if (this.wsClient) {
            const res = await this.fetchApi.brake(this.vehicleName);
            console.log(res);
          }
        } catch (err) {
          console.error(err);
        }
      },
      // 强制遥控
      openForceControl() {
        this.setData({
          isForceControl: true,
          commandType: COMMAND_TYPE.SUPER
        });
      },
      startControl() {
        this.vehicleName && this.getVehiclePosition(this.vehicleName);
      },
      endControl() {
        this.removeWsListener({});
        this.vehicleName && this.recovery(this.vehicleName);
      },
      onMessage(data: any) {
        const res = JSON.parse(data.data);
        if (res) {
          // console.log(JSON.parse(data.data));
          const { eventType } = res;
          if (eventType === 'REMOTE_REQUEST_VEHICLE_RESPONSE') {
            if (res.data.state === 'TIME_EXCEPTION') {
              this.handleRestControl();
            }
            const responseData = res.data;
            const { delayInfo } = res.data;
            if (delayInfo) {
              const supervisorModule = delayInfo.find(
                (item: any) => item.moduleName === 'supervisor'
              );
              const responseTimeStamp = Date.now();
              // 除2取单行通路的延时
              const commandDelay =
                (responseTimeStamp - supervisorModule?.transitTime) / 2 / 1000;
              responseData.commandDelay = commandDelay;
              responseData.responseTimeStamp = responseTimeStamp;
              this.setData({
                responseData
              });
            } else {
              this.setData({
                responseData: null
              });
            }
          }
        }
      },
      onError(data: any) {
        console.log(data);
        console.log('ws error');
      },
      onClose(data: any) {
        console.log(data);
        console.log('ws closed');
      },
      // 用户位置与无人车 距离弹窗
      showPositionModal(vehicleName: string) {
        const that = this;
        wx.showModal({
          content: `当前您与${vehicleName}的距离超过100m。请核对车号，避免接管错车辆。`,
          cancelText: '取消',
          confirmText: '核对无误',
          confirmColor: '#FA4219',
          success: function (res) {
            if (res.confirm) {
              // 下发急停
              that.stop(vehicleName);
            } else if (res.cancel) {
              console.log('用户点击取消');
              that.setData({
                startBtnDisabled: false
              });
            }
          },
          fail(res) {
            console.log(res);
            that.setData({
              startBtnDisabled: false
            });
          }
        });
      },
      // 校验用户位置
      async getVehiclePosition(vehicleName: string) {
        wx.showLoading({
          title: '加载中',
          mask: true
        });
        this.setData({
          startBtnDisabled: true
        });
        try {
          const res: any = await this.fetchApi.getVehiclePosition(vehicleName);
          const userLocation = wx.getStorageSync('userLocation');
          if (res.code === HTTPSTATUSCODE.Success) {
            const distance = this.getDistanceInMeters(
              userLocation.latitude,
              userLocation.longitude,
              res.data?.latitude,
              res.data?.longitude
            );
            wx.hideLoading();
            if (distance <= 100 && distance >= 0) {
              // 下发急停
              this.stop(vehicleName);
            } else {
              this.showPositionModal(vehicleName);
            }
          } else {
            wx.hideLoading();
            wx.showToast({
              title: '车辆位置获取失败',
              icon: 'none'
            });
            this.setData({
              startBtnDisabled: false
            });
          }
        } catch (err) {
          this.setData({
            startBtnDisabled: false
          });
          console.error(err);
        }
      },
      onChangeForceControl() {
        this.setData({
          isForceControl: !this.isForceControl
        });
      }
    }
  });
</script>

<style lang="scss">
  .car-track-wrapper {
    width: 100%;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(159, 215, 255, 0.5) 0%,
      rgba(255, 226, 226, 0.5) 84.2514518123494%,
      rgba(255, 255, 255, 0.5) 100%
    );
    display: flex;
    flex-direction: column;
    align-items: center;
    .vehicle-name {
      width: 343px;
      height: 38px;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      margin-top: 16px;
      color: rgba(51, 51, 51, 1);
      font-size: 14px;
      font-weight: 500;
      text-align: center;
      line-height: 38px;
    }
    .shadow-record-wrapper {
      width: 343px;
      background: rgba(255, 255, 255, 1);
      margin-top: 8px;
      .title {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
        padding-left: 16px;
        border-bottom: 1px solid rgba(245, 245, 245, 1);
      }
      .shadow-record-list {
        width: 100%;
        height: 120px;
        .shadow-record-item {
          width: 100%;
          height: 57px;
          padding: 6px 16px;

          .top {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            .report-time {
              color: rgba(128, 128, 128, 1);
            }
            .content {
              color: rgba(26, 26, 26, 1);
            }
          }
          .bottom {
            margin-top: 4px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 14px;
            font-family: PingFang SC;
            font-weight: normal;
            color: rgba(128, 128, 128, 1);
            .remark {
              width: 100%;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
            .border {
              width: 100%;
              margin-top: 6px;
              border-bottom: 1px solid rgba(245, 245, 245, 1);
            }
          }
        }
      }
      .empty-status {
        width: 100%;
        height: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
      }
      .footer {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        height: 52px;
        border-radius: 0 0 4px 4px;
        box-shadow: 0 -1px 8px 0 rgba(0, 0, 0, 0.05);
        .report-btn,
        .report-btn-disabled {
          width: 225px;
          height: 32px;
          background: linear-gradient(
            135deg,
            rgba(250, 100, 25, 1) 0%,
            rgba(250, 89, 25, 1) 16.59259259%,
            rgba(250, 63, 25, 1) 55.40740741%,
            rgba(250, 44, 25, 1) 100%
          );
          color: white;
          font-size: 14px;
          line-height: 32px;
          text-align: center;
          border-radius: 20px;
        }
        .report-btn-disabled {
          background: linear-gradient(
            135deg,
            rgba(250, 100, 25, 0.3) 0%,
            rgba(250, 89, 25, 0.3) 16.59259259%,
            rgba(250, 63, 25, 0.3) 55.40740741%,
            rgba(250, 44, 25, 0.3) 100%
          );
        }
      }
    }
    .command-area {
      height: 8%;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      width: 343px;
      padding-top: 16px;
      .custom-btn {
        border: 1.5px solid #3c6ef0;
        border-radius: 125px;
        color: #3c6ef0;
        font-size: 14px;
        width: 130px;
        height: 32px;
        transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
        .custom-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          opacity: 1;
          transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
        }
      }
      .custom-btn-hover {
        border: 1.5px solid #8ca8f0;
        border-radius: 125px;
        color: #8ca8f0;
        font-size: 14px;
        width: 130px;
        height: 32px;
        transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
        .custom-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          opacity: 0.5;
          transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
        }
      }
      .command-btn-area {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .delay-info-block {
      width: 100%;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>

<script name="json">
  module.exports = {
    usingComponents: {
      'navi-bar': 'shared/ui/naviBar.mpx',
      'report-modal': 'widgets/carTrack/reportModal.mpx',
      'single-joystick': 'widgets/remoteControl/singlehandJoystick.mpx',
      'delay-info': 'widgets/remoteControl/delayInfo.mpx',
      'common-switch': 'shared/ui/commonSwitch.mpx'
    },
    renderer: 'skyline',
    componentFramework: 'glass-easel',
    navigationStyle: 'custom',
    disableScroll: true
  };
</script>
