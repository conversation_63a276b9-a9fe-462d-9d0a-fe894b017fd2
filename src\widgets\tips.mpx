<template>
  <view class="tips">
    <image class="tips-icon" src="{{IndexPageUrl.LeftArrowGroup}}"></image>
    <view class="tips-content"
      ><text>您还没添加关注<text style="color: #fa2c19">车辆和区域</text></text>
      <text style="margin-top: 16px">设置后可控车、接收消息</text>
    </view>
    <image class="tips-icon" src="{{IndexPageUrl.RightArrowGroup}}"></image>
  </view>
  <view class="operation">
    <view class="operation-btn" bindtap="onOpenVehicle">添加关注车辆</view>
    <view class="operation-btn">添加关注区域</view>
  </view>
</template>
<script  lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { IndexPageUrl } from 'shared/assets/imageUrl';
  createComponent({
    data: {
      IndexPageUrl: IndexPageUrl
    },
    methods: {
      onOpenVehicle() {
        const eventOptions = {
          composed: true
        };
        this.triggerEvent('openvehicle', {}, eventOptions);
        this.triggerEvent('openarea', {}, eventOptions);
        this.triggerEvent('test', {}, eventOptions);
      }
    }
  });
</script>
<style lang="scss">
  .tips {
    position: absolute;
    bottom: 350px;
    left: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .tips-icon {
    width: 36px;
    height: 14px;
  }
  .tips-content {
    margin: 0 16px 0 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 225px;
    height: 24px;
    font-size: 18px;
    font-family: JDLangZhengTi;
    color: rgba(26, 26, 26, 1);
  }
  .operation {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 163px;
    width: 100%;
  }
  .operation-btn {
    margin-bottom: 23px;
    width: 226px;
    height: 40px;
    background: linear-gradient(
      135deg,
      rgba(250, 100, 25, 1) 0%,
      rgba(250, 89, 25, 1) 16.59259259%,
      rgba(250, 63, 25, 1) 55.40740741%,
      rgba(250, 44, 25, 1) 100%
    );
    box-shadow: 0px 1px 8px rgba(250, 100, 25, 1);
    border-radius: 20px;
    text-align: center;
    line-height: 40px;
    color: white;
  }
</style>
<script name="json">
  module.exports = {
    component: true
  };
</script>
