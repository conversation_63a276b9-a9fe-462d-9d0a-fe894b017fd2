/**
 *
 * WARNING: 请谨慎修改，若导致登录业务不可用，业务自行承担风险
 *
 */
import util from '../util.js';
const plugin = requirePlugin('loginPlugin');

/**
 * 风控验证完成，服务端固定路径地址 pages/login/web-view/web-view，请勿修改，
 *   否则会导致无法验证写入原生登录态
 */
Page({
  onLoad(options = {}) {
    /**
     * token 是服务端接口返回固定字段，请勿更改
     * islogin 是服务端接口返回固定字段，请勿更改
     */
    const { token, islogin } = options;
    if (Number(islogin) === 0) {
      util.redirectPage('/pages/login/index/index?riskFail=1');
      return;
    }
    util.setCustomNavigation();

    // 风控验证完成写入原生登录态
    this.handleBackFromH5(token);
  },
  // 写入原生小程序登录态
  handleBackFromH5(token) {
    plugin
      .tokenLogin({ token })
      .then((res = {}) => {
        let { goback, err_msg } = res;
        if (err_msg) {
          wx.showModal({
            title: '提示',
            content: err_msg || '登录失败，请稍后重试',
            success: res => {
              if (res.confirm) {
                this.handleBackFromH5(token);
              }
            },
          });
        } else if (goback) {
          plugin.gobackLog({ route: 7 });
          util.goBack();
        } else {
          // TODO: 一般不会产生未知错误类型，业务可自行兜底操作
          wx.showModal({
            title: '提示',
            content: '登录失败，请稍后重试～',
          });
        }
      })
      .catch(res => console.jdLoginLog(res));
  },
});
