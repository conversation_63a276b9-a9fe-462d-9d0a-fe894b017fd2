function calculateSpeed(y: number): number {
  // 定义sigmoid函数的斜率参数k，这里需要调整以确保y=±130时speed接近±1.2
  const k: number = Math.log(2) / 130; // 调整k值以使得e^(-k*130)接近0.5

  // 实现调整后的sigmoid函数来计算速度
  let speed: number = -2.4 / (1 + Math.exp(-k * y)) + 1.2;
  speed *= 3;
  // 保留两位小数
  speed = parseFloat(speed.toFixed(2));

  return speed;
}
function calculateAngle(x: number): number {
  // 定义函数的斜率参数k
  const k: number = 1 / 130;
  // 定义角度的最大值C
  const C = 33;

  // 实现调整后的sigmoid函数来计算转向角度
  let angle: number = C * (2 / (1 + Math.exp(-k * x)) - 1);

  angle *= (1.95 * Math.PI) / 180;
  angle *= -1;
  return angle;
}
// 辅助函数：将一个值从一个范围映射到另一个范围
function mapRange(
  value: number,
  fromLow: number,
  fromHigh: number,
  toLow: number,
  toHigh: number
) {
  return toLow + ((toHigh - toLow) * (value - fromLow)) / (fromHigh - fromLow);
}
function singlehandAngleAndSpeed(x: number, y: number) {
  const circleRadius = 130; // 圆的半径
  let speed, angle;

  // 计算摇杆位置与圆心的连线长度
  const distanceFromCenter = Math.sqrt(x * x + y * y);

  // 如果摇杆在圆上，速度为1.2
  if (distanceFromCenter === circleRadius) {
    if (y > 0) {
      speed = -1.2;
    } else {
      speed = 1.2;
    }
  } else {
    // 根据摇杆位置计算速度
    const normalizedDistance = distanceFromCenter / circleRadius;
    speed = normalizedDistance * 1.2;
    // 如果摇杆在下半圆，速度取负值
    if (y > 0) {
      speed = -speed;
    }
  }
  // 计算原始角度（弧度）与Y轴的夹角
  let rawAngle = Math.atan2(x, y);

  // 将原始角度转换为度数，并调整到[0, 360)范围
  rawAngle = ((rawAngle * 180) / Math.PI + 360) % 360;

  // 根据摇杆所在象限调整角度范围
  if (rawAngle <= 90) {
    // 第一象限
    angle = (mapRange(rawAngle, 0, 90, 0, -33) * Math.PI) / 180;
  } else if (rawAngle <= 180) {
    // 第二象限
    angle = (mapRange(rawAngle, 90, 180, 33, 0) * -1 * Math.PI) / 180;
  } else if (rawAngle <= 270) {
    // 第三象限
    angle = (mapRange(rawAngle, 180, 270, 0, 33) * Math.PI) / 180;
  } else {
    // 第四象限
    angle = (mapRange(rawAngle, 270, 360, -33, 0) * -1 * Math.PI) / 180;
  }

  return { speed, angle };
}

export { calculateSpeed, calculateAngle, singlehandAngleAndSpeed };
