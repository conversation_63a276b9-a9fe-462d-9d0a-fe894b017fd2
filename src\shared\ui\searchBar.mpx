<template>
  <view class="search-bar">
    <view class="icon-prefix-wrapper">
      <t-icon name="{{VehicleUrl.SearchIcon}}" size="16px" color="#868D9F" />
    </view>
    <view class="input-wrapper">
      <input
        placeholder="{{placeholder}}"
        placeholderStyle="font-size: 14px"
        confirm-type="search"
        class="search-input"
        cursor-color="#FA2C19"
        cursor="{{searchValue.length}}"
        model:value="{{searchValue}}"
        maxlength="{{maxLength}}"
        model:focus="{{isSearchFocused}}"
        always-embed="{{true}}"
        bindconfirm="onConfirm"
        bindfocus="onFocus"
        bindinput="onInput"
      />
    </view>
    <view class="icon-suffix-wrapper" bindtap="onClear">
      <t-icon name="close-circle-filled" size="16px" color="#868D9F" />
    </view>
  </view>
</template>
<script lang='ts'>
  import { createComponent } from '@mpxjs/core';
  import { VehicleUrl } from 'shared/assets/imageUrl';
  createComponent({
    properties: {
      searchValue: {
        type: String,
        value: ''
      },
      placeholder: {
        type: String,
        value: '请输入'
      },
      isSearchFocused: {
        type: Boolean,
        value: false
      },
      maxLength: {
        type: Number,
        value: 10
      }
    },
    data: {
      VehicleUrl
    },
    methods: {
      onConfirm(e: any) {
        this.triggerEvent('onsearch', e.detail);
      },
      onClear(e: any) {
        this.triggerEvent('onclear');
      },
      onFocus(e: any) {
        this.triggerEvent('onfocus');
      },
      onInput(e: any) {
        this.triggerEvent('oninput', e.detail);
      },
      onBlur(e: any) {
        wx.hideKeyboard();
      }
    }
  });
</script>
<style lang="scss">
  .search-bar {
    width: 90%;
    height: 32px;
    background: rgba(245, 245, 246, 1);
    border-radius: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 12px;
    padding-right: 12px;
    margin-left: 16px;
    margin-top: 6px;
    .input-wrapper {
      width: 100%;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      padding: 0 6px;
      input {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-icon': 'tdesign-miniprogram/icon/icon'
    }
  };
</script>
