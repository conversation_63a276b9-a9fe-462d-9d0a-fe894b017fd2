const globalEvent: any = {};

const $on = function (params: any) {
  if (!params) {
    return false;
  }
  if (!params.name) {
    console.error('事件监听未设置名称 属性key=name');
    return false;
  }
  if (!params.success) {
    console.error('事件监听未设置回调函数 属性key=success');
    return false;
  }
  if (!params.tg) {
    console.error('事件监听未设置目标对象   属性key=tg');
    return false;
  }
  if (globalEvent[params.name]) {
    const list = globalEvent[params.name];
    list.push([params.tg, params.success]);
  } else {
    globalEvent[params.name] = [[params.tg, params.success]];
  }
  pageStatus(params.tg);
};

const $emit = function (params: any) {
  if (!params) {
    return false;
  }
  if (!params.name) {
    console.error('事件发送未设置名称 属性key=name');
    return false;
  }
  if (globalEvent[params.name]) {
    const list = globalEvent[params.name];
    list.forEach((item: any) => {
      item[1].call(item[0], params.data);
    });
  }
};

const $remove = function (params: any) {
  if (!params) {
    return false;
  }
  if (!params.tg) {
    console.error('事件监听未设置目标对象   属性key=tg');
    return false;
  }

  if (params.name && globalEvent[params.name]) {
    globalEvent[params.name] = globalEvent[params.name].filter((a: any) => {
      return a[0] !== params.tg;
    });
  } else {
    for (let key in globalEvent) {
      globalEvent[key] = globalEvent[key].filter((a: any) => {
        return a[0] !== params.tg;
      });
    }
  }
};

const pageStatus = function (self: any) {
  if (self.onUnload) {
    const s = self.onUnload;
    self.onUnload = function (a: any) {
      s.call(this, a);
      $remove({
        tg: this
      });
    };
  } else {
    self.onUnload = function () {
      $remove({
        tg: this
      });
    };
  }
};

exports.$on = $on;
exports.$emit = $emit;
exports.$remove = $remove;
