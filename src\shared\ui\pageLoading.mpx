<template>
  <view wx:if="{{visible}}" class="page-loading" style="{{customStyle}}">
    <view class="loading-content" style="width: {{width}}; height: {{height}};">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{text}}</text>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';

  createComponent({
    properties: {
      visible: {
        type: Boolean,
        value: false
      },
      text: {
        type: String,
        value: '加载中...'
      },
      width: {
        type: String,
        value: 'auto'
      },
      height: {
        type: String,
        value: 'auto'
      },
      customStyle: {
        type: String,
        value: ''
      }
    }
  });
</script>

<style lang="scss" scoped>
  .page-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32rpx;
      background-color: rgba(0, 0, 0, 0.8);
      border-radius: 16rpx;
      min-width: 200rpx;
      min-height: 120rpx;

      .loading-spinner {
        width: 60rpx;
        height: 60rpx;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
        border-top: 4rpx solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16rpx;
      }

      .loading-text {
        color: #ffffff;
        font-size: 28rpx;
        text-align: center;
      }
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>

<script type="application/json">
  {
    "component": true
  }
</script>
