
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="导航找车"
    backUrl="/pages/workbench/index"
  ></navi-bar>

  <vehicle-list
    selectedVehicleName="{{selectedVehicleName}}"
    tip="选择车辆后，进入导航找车"
    bind:handleSelect="handleSelect"
  />

  <map style="width: 100rpx; height: 100rpx; display: none" id="map"></map>
</template>

<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  import { CommonApi } from 'shared/api/common';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { FindCar } from 'widgets/commandOperate/findcarOperate';

  createPage<any>({
    data: {
      selectedVehicleName: null,
      visible: false,
      fetchApi: new CommonApi()
    },
    methods: {
      handleSelect(e: any) {
        console.log(wx.getSystemSetting());
        console.log(wx.getAppAuthorizeSetting());
        const appAuthorize = wx.getAppAuthorizeSetting();
        const systemSetting = wx.getSystemSetting();
        if (appAuthorize.locationAuthorized !== 'authorized') {
          wx.showToast({
            title: '请打开手机「设置-微信-位置」开启定位！',
            icon: 'none',
            duration: 3000
          });
        }
        if (!systemSetting.locationEnabled) {
          wx.showToast({
            title: '请打开手机定位设置！',
            icon: 'none',
            duration: 3000
          });
        }

        this.setData({
          selectedVehicleName: e.detail.val
        });
        if (!e.detail.val) {
          return;
        }
        if (this.findCarOperate) {
          this.findCarOperate.handleNavigation(e.detail.val, () => {
            this.setData({
              selectedVehicleName: null
            });
          });
        }
      },
      onVisibleChange() {
        this.selectedVehicleName = null;
      }
    },
    onReady: function () {
      this.mapContext = wx.createMapContext('map', this);
      this.findCarOperate = new FindCar(this.mapContext);
      if (this.selectedVehicleName && this.findCarOperate) {
        this.findCarOperate.handleNavigation(this.selectedVehicleName, () => {
          this.setData({
            selectedVehicleName: null
          });
        });
      }
    },
    onLoad: function (options) {
      this.selectedVehicleName = options.vehicleName;
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx",
      "restart-operate": "widgets/commandOperate/restartOperate.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
