<template>
  <view class="map-update-info" wx:if="isInit" bindtap="showDetailModal">
    <image class="info-icon" src="{{SingleVehicle.MapUpdateInfo}}"></image>
    <view class="info-text">详情</view>
  </view>
  <t-popup
    visible="{{show}}"
    placement="{{'center'}}"
    close-on-overlay-click="{{false}}"
    t-class="popup"
  >
    <view class="container">
      <view class="body" wx:if="{{isDownlading}}">
        <view class="title">地图下载中({{ updateDetail.version }})</view>
        <view class="content">
          <view class="progress">
            <view class="icon">
              <image src="{{SingleVehicle.DownloadingIcon}}"></image>
            </view>
            <view class="progress-wrapper">
              <view class="time-value">
                <view class="time"
                  ><view>预计剩余时间</view>
                  <view style="color: rgba(51, 51, 51, 1); margin-left: 8px">
                    {{ updateDetail.estimatedCompletionTime || '--' }}</view
                  ></view
                >
                <view class="value"> {{ progress }}%</view>
              </view>
              <t-progress
                color="{{ {  from: '#FA6419', to: '#FA2C19' } }}"
                percentage="{{progress}}"
                status="active"
                label="{{false}}"
              />
            </view>
          </view>
        </view>
        <view class="bottom-btns">
          <view bindtap="handleCloseDetailModal" class="b-button cancel-btn">
            知道了
          </view>
          <view bindtap="showSilentConfirmModal" class="b-button ok-btn">
            转为静默下载
          </view>
        </view>
      </view>
      <view class="body" wx:else>
        <view class="title">地图升级中({{ updateDetail.version }})</view>
        <view class="content">
          <view class="progress">
            <view class="icon">
              <image src="{{SingleVehicle.UpdatingIcon}}"></image>
            </view>
            <view class="progress-wrapper">
              <view class="time-value">
                <view class="time"
                  ><view>预计剩余时间</view>
                  <view style="color: rgba(51, 51, 51, 1); margin-left: 8px">
                    {{ updateDetail.estimatedCompletionTime || '--' }}</view
                  ></view
                >
                <view class="value"> {{ progress }}%</view>
              </view>
              <t-progress
                color="{{ {  from: '#FA6419', to: '#FA2C19' } }}"
                percentage="{{progress}}"
                status="active"
                label="{{false}}"
              />
            </view>
          </view>
        </view>
        <view class="bottom-btns">
          <view bindtap="handleCloseDetailModal" class="b-button ok-btn">
            确认
          </view>
        </view>
      </view>
    </view>
  </t-popup>
  <t-popup
    visible="{{showSilent}}"
    close-on-overlay-click="false"
    placement="{{'center'}}"
    t-class="popup"
  >
    <view class="container">
      <view class="body">
        <view class="content">
          <view class="silent-info">
            静默下载即后台下载，车辆启动后将使用原地图版本运行，请确认是否转为静默下载。
          </view>
        </view>
        <view class="bottom-btns">
          <view bindtap="handleCancelSilent" class="b-button cancel-btn">
            关闭
          </view>
          <view bindtap="handleConfirmSilent" class="b-button ok-btn">
            确定
          </view>
        </view>
      </view>
    </view>
  </t-popup>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { SingleVehicle } from 'shared/assets/imageUrl';
  import { MapUpdaterStatusEnum } from 'shared/utils/constant';
  createComponent({
    properties: {
      isInit: {
        type: Boolean,
        value: false
      },
      vehicleName: {
        type: String,
        value: ''
      },
      updateDetail: {
        type: Object,
        value: {}
      },
      show: {
        type: Boolean,
        value: false
      },
      showSilent: {
        type: Boolean,
        value: false
      }
    },
    data: {
      SingleVehicle,
      MapUpdaterStatusEnum
    },
    computed: {
      isDownlading() {
        return (
          this.updateDetail.progressType ===
          this.MapUpdaterStatusEnum.DOWNLOADING.key
        );
      },
      progress() {
        return this.updateDetail.progressType ===
          this.MapUpdaterStatusEnum.DOWNLOADING.key
          ? Number(this.updateDetail.downloadStep) || '--'
          : this.updateDetail.progressType ===
            this.MapUpdaterStatusEnum.UPGRADE.key
          ? Number(this.updateDetail.upgradeStep) || '--'
          : 0;
      }
    },
    lifetimes: {},
    methods: {
      showDetailModal(e: any) {
        this.triggerEvent('show');
      },
      showSilentConfirmModal(e: any) {
        this.triggerEvent('showsilentmodal');
      },
      handleCancelSilent(e: any) {
        this.triggerEvent('cancelsilent');
      },
      handleCloseDetailModal(e: any) {
        this.triggerEvent('close');
      },
      handleConfirmSilent(e: any) {
        this.triggerEvent('confirmsilent');
      }
    }
  });
</script>

<style lang="scss">
  .popup {
    width: 320px;
    height: 168px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    .container {
      width: 100%;
      height: 168px;
      .body {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .title {
          width: 100%;
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 500;
          color: rgba(51, 51, 51, 1);
          padding: 16px;
          border-bottom: 1px solid rgba(245, 245, 245, 1);
        }
        .content {
          .progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            .icon {
              width: 30px;
              height: 30px;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 12px;

              image {
                width: 30px;
                height: 30px;
              }
            }
            .progress-wrapper {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              flex: 1;
              .time-value {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
                font-family: PingFang SC;
                font-weight: normal;
                margin-bottom: 7px;
                .time {
                  display: flex;
                  color: rgba(153, 153, 153, 1);
                }
                .value {
                  color: #fa2c19;
                }
              }
            }
          }
          .silent-info {
            padding: 24px;
            font-size: 16px;
            font-family: PingFang SC;
            font-weight: 500;
            color: rgba(53, 53, 53, 1);
            text-align: center;
          }
        }
        .bottom-btns {
          height: 56px;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          align-items: center;
          width: 100%;
          border-top: 1px solid #ededed;
          .b-button {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            height: 50px;
            padding: 16px;
            width: 100%;
          }

          .cancel-btn {
            color: #000000;
            border-right: 1px solid #ededed;
          }

          .ok-btn {
            color: #fa2c19;
          }
        }
      }
    }
  }
  .map-update-info {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .info-icon {
      width: 12px;
      height: 12px;
      margin-right: 3px;
    }
    .info-text {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(250, 44, 25, 1);
    }
  }
</style>

<script name="json">
  module.exports = {
    usingComponents: {
      't-popup': 'tdesign-miniprogram/popup/popup',
      't-progress': 'tdesign-miniprogram/progress/progress'
    },
    component: true
  };
</script>
