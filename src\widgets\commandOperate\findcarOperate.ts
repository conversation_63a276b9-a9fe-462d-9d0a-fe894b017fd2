import { CommonApi } from 'shared/api/common';
import { HTTPSTATUSCODE } from 'shared/api/fetch';

export class FindCar {
  fetchApi: any;
  mapContext: any;
  constructor(mapContext: any) {
    this.fetchApi = new CommonApi();
    this.mapContext = mapContext;
  }

  async handleNavigation(vehicleName: string, cb?: AnyFunc) {
    const res = await this.fetchApi.getVehicleInfo(vehicleName);
    if (
      res.code === HTTPSTATUSCODE.Success &&
      res.data.latitude &&
      res.data.longitude
    ) {
      this.mapContext.openMapApp({
        latitude: res.data.latitude,
        longitude: res.data.longitude,
        destination: vehicleName,
        success: (res: any) => {
          if (cb) {
            const deviceInfo = wx.getDeviceInfo();
            if (deviceInfo.system.includes('iOS')) {
              cb();
            }
          }
        },
        fail: (res: any) => {
          const systemSetting = wx.getSystemSetting();
          if (!systemSetting.locationEnabled) {
            wx.showToast({
              title: '请打开手机定位设置！',
              icon: 'none',
              duration: 3000
            });
          }
        }
      });
    } else {
      wx.showToast({
        title: '获取车辆位置失败',
        icon: 'none',
        duration: 3000
      });
    }
  }
}
