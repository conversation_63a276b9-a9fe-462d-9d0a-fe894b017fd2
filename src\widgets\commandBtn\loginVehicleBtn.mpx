
<template>
  <view
    class="login-vehicle-btn"
    wx:class="{{styleInfo.otherClassName}}"
    bindtap="handleClick"
  >
    <t-image
      src="{{styleInfo.imageSrc}}"
      mode="aspectFill"
      width="{{styleInfo.imageWidth}}"
      height="{{styleInfo.imageHeight}}"
    />
    <text class="label" wx:class="{{styleInfo.otherClassName}}">登录车端</text>
  </view>
</template>

<script  lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, SingleVehicle } from 'shared/assets/imageUrl';
  import { CheckVehicle, CheckType } from 'shared/utils/checkVehicle';
  import { CommonApi } from 'shared/api/common';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { CallFrontApi } from 'shared/api/callFront';

  createComponent({
    properties: {
      // 'workBench'  ||  'singelVehicle'
      sourceType: {
        type: String,
        value: ''
      },
      vehicleName: {
        type: String,
        value: ''
      }
    },
    data: {
      fetchApi: new CommonApi(),
      callFrontFetchApi: new CallFrontApi(),
      WorkBenchUrl: WorkBenchUrl,
      SingleVehicle: SingleVehicle,
      styleInfo: {}
    },
    methods: {
      formatStyleInfo() {
        if (this.sourceType === 'workBench') {
          this.styleInfo = {
            imageSrc: this.WorkBenchUrl.LoginCar,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'workBench'
          };
        } else {
          this.styleInfo = {
            imageSrc: this.SingleVehicle.LoginCarBtn,
            imageWidth: '28px',
            imageHeight: '28px',
            otherClassName: 'singelVehicle'
          };
        }
      },
      handleClick() {
        if (this.sourceType === 'workBench') {
          wx.navigateTo({ url: '/pages/selectVehiclePages/loginVehiclePage' });
        } else if (this.sourceType === 'singleVehicle') {
          const check = new CheckVehicle(
            [
              { checkType: CheckType.VehicleStatus },
              { checkType: CheckType.Distance }
            ],
            this.vehicleName
          );
          check.checkVehicleFunc().then(res => {
            if (res) {
              this.handleLogin();
            }
          });
        }
      },
      async handleLogin() {
        const userName = wx.getStorageSync('userName');
        const phoneRes: any = await this.callFrontFetchApi.getUserPhone(userName);
        if (phoneRes.code === HTTPSTATUSCODE.Success) {
          this.fetchApi
            .loginVehicle(this.vehicleName, phoneRes.data.phone)
            .then((res: any) => {
              wx.showToast({
                icon: 'none',
                title:
                  res.code === HTTPSTATUSCODE.Success ? '登录成功' : res.message
              });
            });
        }
      }
    },
    lifetimes: {
      created: function () {
        this.formatStyleInfo();
      }
    }
  });
</script>

<style lang="scss">
  .login-vehicle-btn {
    display: flex;
    flex-direction: column;
    align-items: center;

    .label {
      text-align: center;
      font-family: PingFang SC;
      font-weight: normal;
      &.workBench {
        font-size: 12px;
        bottom: 10px;
        color: rgba(51, 51, 51, 1);
        margin-top: 6px;
      }
      &.singelVehicle {
        color: rgba(51, 51, 51, 1);
        font-size: 11px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-image": "tdesign-miniprogram/image/image"
    }
  }
</script>
