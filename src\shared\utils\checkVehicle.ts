import { SystemStatus, TakeOverType, TakeOverSourceType } from './constant';
import { CommonApi } from 'shared/api/common';
import { HTTPSTATUSCODE } from 'shared/api/fetch';
import useLocationStore from 'shared/store/useLocationStore';
import mpx from '@mpxjs/core';
import mpxFetch from '@mpxjs/fetch';
mpx.use(mpxFetch);

export enum CheckType {
  VehicleStatus = 'VehicleStatus',
  TakeOver = 'TakeOver',
  TaskRepairOrder = 'TaskRepairOrder',
  Distance = 'Distance',
  VehiclePeriod = 'VehiclePeriod',
  VehiclePermission = 'VehiclePermission'
}

export const showMessage = (message: string, duration = 3000) => {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: duration
  });
};

export class CheckVehicle {
  fetchApi: any;
  vehicleName: string;
  checkList: any[];
  vehicleInfo: any;
  constructor(checkList: any[], vehicleName: string) {
    this.vehicleName = vehicleName;
    this.checkList = checkList;
    this.fetchApi = new CommonApi();
  }

  checkVehicleStatus(msg?: string) {
    if (this.vehicleInfo.systemStatus === SystemStatus.OFFLINE) {
      showMessage(msg || '当前车辆离线！');
      return Promise.resolve(false);
    }
    return Promise.resolve(true);
  }

  checkTakeOver(takeOverMsg?: string, temporaryMsg?: string) {
    if (this.vehicleInfo.takeOverStatus === TakeOverType.TAKEOVER) {
      showMessage(takeOverMsg || '车辆接管中不允许操作！');
      return Promise.resolve(false);
    }
    if (this.vehicleInfo.takeOverStatus === TakeOverType.TEMPORARY) {
      showMessage(temporaryMsg || '请先解除停车！');
      return Promise.resolve(false);
    }
    return Promise.resolve(true);
  }

  async checkTaskRepairOrder(msg?: string) {
    const res = await this.fetchApi.checkVehicleTaskRepairOrder(
      this.vehicleName
    );
    if (res.code === HTTPSTATUSCODE.Success) {
      return true;
    } else if (res.code === 'B031306') {
      showMessage(msg || '车辆有任务，暂不支持自由跑行！');
      return false;
    } else if (res.code === 'B031305') {
      showMessage(msg || '车辆离线，暂不支持自由跑行！');
      return false;
    } else if (res.code === 'B031307') {
      return new Promise((resolve, reject) => {
        wx.showModal({
          title: msg || `${this.vehicleName}存在影响运营的维修单，请确认添加？`,
          success(res) {
            if (res.confirm) {
              resolve(true);
            } else if (res.cancel) {
              resolve(false);
            }
          }
        });
      });
    } else {
      showMessage(res.message);
    }
    return false;
  }

  checkDistance(cannotMsg?: string, canMsg?: string) {
    // 人的当前位置
    const userLocation = wx.getStorageSync('userLocation');
    return mpx.xfetch
      .fetch({
        url: 'https://apis.map.qq.com/ws/distance/v1',
        method: 'GET',
        params: {
          mode: 'driving',
          from: `${this.vehicleInfo.latitude},${this.vehicleInfo.longitude}`,
          to: `${userLocation.latitude},${userLocation.longitude}`,
          key: '67ABZ-HLPRQ-XZM53-GNCSR-GNSXF-DSBDK'
        }
      })
      .then((res: any) => {
        if (res.status === 200 && res.data.status === 0) {
          console.log(res.data.result.elements[0].distance);
          if (res.data.result.elements[0].distance > 100) {
            return new Promise((resolve, reject) => {
              wx.showModal({
                title:
                  cannotMsg ||
                  `当前您与${this.vehicleName}的距离超过100米，请核对车号，确认远程登录车端吗？`,
                success(res) {
                  if (res.confirm) {
                    resolve(true);
                  } else if (res.cancel) {
                    resolve(false);
                  }
                }
              });
            });
          } else {
            return new Promise((resolve, reject) => {
              wx.showModal({
                title: canMsg || '确认远程登录车端吗？',
                success(res) {
                  if (res.confirm) {
                    resolve(true);
                  } else if (res.cancel) {
                    resolve(false);
                  }
                }
              });
            });
          }
        } else {
          wx.showToast({
            title: res.message,
            icon: 'none'
          });
        }
      });
  }

  async checkVehiclePeriod(msg?: string) {
    const res = await this.fetchApi.checkCanRepair(this.vehicleName);
    if (res.code === HTTPSTATUSCODE.Success) {
      if (res.data) {
        return true;
      } else {
        showMessage(msg || '车辆生命周期待交付、已交付、维修中才可提报维修');
        return false;
      }
    } else {
      return false;
    }
  }

  checkVehiclePermission(msg: string) {
    if (this.vehicleInfo.coverPermission) {
      return Promise.resolve(true);
    }
    msg && showMessage(msg);
    return Promise.resolve(false);
  }

  async checkVehicleFunc() {
    if (
      this.checkList.filter(
        (item: any) =>
          [
            CheckType.VehicleStatus,
            CheckType.TakeOver,
            CheckType.Distance,
            CheckType.VehiclePermission
          ].indexOf(item.checkType) > -1
      ).length > 0
    ) {
      const vehicleInfoRes = await this.fetchApi.getVehicleInfo(
        this.vehicleName
      );
      if (vehicleInfoRes.code === HTTPSTATUSCODE.Success) {
        this.vehicleInfo = vehicleInfoRes.data;
      }
    }
    let res: any = false;
    for (const v of this.checkList) {
      if (v.checkType === CheckType.VehiclePermission) {
        const _res = await this.checkVehiclePermission(v.msg);
        res = _res;
        if (_res) {
          continue;
        } else {
          break;
        }
      }
      if (v.checkType === CheckType.VehicleStatus) {
        const _res = await this.checkVehicleStatus(v.msg);
        res = _res;
        if (_res) {
          continue;
        } else {
          break;
        }
      }
      if (v.checkType === CheckType.TakeOver) {
        const _res = await this.checkTakeOver(v.takeOverMsg, v.temporaryMsg);
        res = _res;
        if (_res) {
          continue;
        } else {
          break;
        }
      }
      if (v.checkType === CheckType.TaskRepairOrder) {
        const _res = await this.checkTaskRepairOrder(v.msg);
        res = _res;
        if (_res) {
          continue;
        } else {
          break;
        }
      }
      if (v.checkType === CheckType.Distance) {
        const _res = await this.checkDistance(v.cannotMsg, v.canMs);
        res = _res;
        if (_res) {
          continue;
        } else {
          break;
        }
      }
      if (v.checkType === CheckType.VehiclePeriod) {
        const _res = await this.checkVehiclePeriod(v.msg);
        res = _res;
        if (_res) {
          continue;
        } else {
          break;
        }
      }
    }
    return res;
  }
}
