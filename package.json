{"name": "minimonitor-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "mpx-cli-service serve --env beta", "serve:online": "mpx-cli-service serve --env production", "serve:test1": "mpx-cli-service serve --env test1", "serve:test2": "mpx-cli-service serve --env test2", "build:beta": "mpx-cli-service build --env beta", "build:online": "mpx-cli-service build --env production", "build:test1": "mpx-cli-service build --env test1", "build:test2": "mpx-cli-service build --env test2", "test:e2e": "npx e2e-runner j---config=./jest-e2e.config.js", "lint": "eslint --ext .js,.ts,.mpx src/", "build:e2e": "npm run build && npm run test:e2e", "test": "jest test/components"}, "dependencies": {"@jd/sgm-mp": "^3.1.1", "@mpxjs/api-proxy": "^2.9.0", "@mpxjs/core": "^2.9.0", "@mpxjs/fetch": "^2.9.0", "@mpxjs/pinia": "^2.9.0", "@mpxjs/store": "^2.9.0", "@mpxjs/utils": "^2.9.0", "@mpxjs/webview-bridge": "^2.9.45", "dayjs": "^1.11.11", "lodash": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.0.14", "tdesign-miniprogram": "^1.4.4", "vue": "^2.7.0", "vue-demi": "^0.14.6", "vue-i18n": "^8.27.2", "vue-i18n-bridge": "^9.2.2", "vue-router": "^3.1.3"}, "devDependencies": {"@babel/core": "7.24.8", "@babel/plugin-transform-runtime": "7.24.7", "@babel/preset-env": "7.24.8", "@babel/preset-typescript": "^7.24.7", "@babel/runtime-corejs3": "7.24.8", "@mpxjs/babel-plugin-inject-page-events": "^2.9.0", "@mpxjs/eslint-config-ts": "^1.0.5", "@mpxjs/miniprogram-simulate": "^1.4.17", "@mpxjs/mpx-cli-service": "^2.0.0", "@mpxjs/mpx-jest": "^0.0.24", "@mpxjs/size-report": "^2.9.0", "@mpxjs/vue-cli-plugin-mpx": "^2.0.0", "@mpxjs/vue-cli-plugin-mpx-e2e-test": "^2.0.0", "@mpxjs/vue-cli-plugin-mpx-eslint": "^2.0.0", "@mpxjs/vue-cli-plugin-mpx-typescript": "^2.0.0", "@mpxjs/vue-cli-plugin-mpx-unit-test": "^2.0.0", "@mpxjs/webpack-plugin": "^2.9.0", "@types/jest": "^27.5.1", "@types/lodash": "^4.17.7", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.2.4", "babel-jest": "^27.4.5", "css-loader": "^7.1.2", "eslint": "^7.0.0", "jest": "^27.4.5", "postcss": "^8.2.6", "sass": "1.77.8", "sass-loader": "14.2.1", "terser-webpack-plugin": "^5.3.10", "ts-jest": "^27.1.2", "typescript": "^4.1.3", "webpack": "^5.43.0"}, "browserslist": ["ios >= 8", "chrome >= 47"], "description": "node version: 18.17.0", "main": ".e2erc.js", "directories": {"test": "test"}, "keywords": [], "author": "", "license": "ISC"}