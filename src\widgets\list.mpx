<template>
  <view class="section-item">
    <pan-gesture-handler onGestureEvent="handlepan1">
      <view class="circle1" bindtouchstart="handleViewVibrate"
        >simple drag</view
      >
    </pan-gesture-handler>
    <pan-gesture-handler onGestureEvent="handlepan">
      <view
        class="circle"
        bindtouchstart="handleViewVibrate"
        bindtouchmove="handleTouchMove"
        >simple drag-main</view
      >
    </pan-gesture-handler>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  const GestureState = {
    POSSIBLE: 0,
    BEGIN: 1,
    ACTIVE: 2,
    END: 3,
    CANCELLED: 4
  };

  const { shared, spring } = wx.worklet;
  createComponent({
    data: {
      listData: ['手机', '电视', '电脑']
    },
    lifetimes: {
      attached() {
        const x = shared(0);
        const y = shared(0);
        const scale = shared(1);
        const pressed = shared(false);
        this.applyAnimatedStyle('.circle', () => {
          'worklet';
          return {
            backgroundColor: pressed.value ? '#5f9ea0' : '#adff2f',
            transform: `translate(${x.value}px, 0px)`
          };
        });

        const x1 = shared(0);
        const y1 = shared(0);
        const scale1 = shared(1);
        const pressed1 = shared(false);
        this.applyAnimatedStyle('.circle1', () => {
          'worklet';
          return {
            backgroundColor: pressed1.value ? '#5f9ea0' : '#adff2f',
            transform: `translate(0px, ${y1.value}px)`
          };
        });
        this.x = x;
        this.y = y;
        this.scale = scale;
        this.pressed = pressed;

        this.x1 = x1;
        this.y1 = y1;
        this.scale1 = scale1;
        this.pressed1 = pressed1;
      }
    },
    methods: {
      handlepan(evt: any) {
        'worklet';
        if (evt.state === GestureState.POSSIBLE) {
          this.pressed.value = true;
          this.scale.value = spring(1.2, {}, () => {});
        } else if (
          evt.state === GestureState.END ||
          evt.state === GestureState.CANCELLED
        ) {
          this.pressed.value = false;
          this.scale.value = spring(1, {}, () => {});
          this.x.value = 0;
        } else if (evt.state === GestureState.ACTIVE) {
          this.x.value += evt.deltaX;
        }
      },
      handlepan1(evt: any) {
        'worklet';
        if (evt.state === GestureState.POSSIBLE) {
          this.pressed1.value = true;
          this.scale1.value = spring(1.2, {}, () => {});
        } else if (
          evt.state === GestureState.END ||
          evt.state === GestureState.CANCELLED
        ) {
          this.pressed1.value = false;
          this.scale1.value = spring(1, {}, () => {});
          this.y1.value = 0;
        } else if (evt.state === GestureState.ACTIVE) {
          this.y1.value += evt.deltaY;
        }
      },
      handleViewVibrate() {
        wx.vibrateShort({
          type: 'heavy',
          success: () => {
            console.log('震动成功');
          },
          fail: () => {
            console.log('震动失败');
          }
        });
      },
      handleTouchMove(e: any) {
        if (e.touches[e.touches.length - 1].clientY - this.y.value <= 300) {
        }
      }
    }
  });
</script>

<style lang="scss">
  .section-item {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
  }
  .circle {
    width: 100px;
    height: 100px;
    background-color: #adff2f;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 180px;
  }
  .circle1 {
    width: 100px;
    height: 100px;
    background-color: #adff2f;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>

<script name="json">
  module.exports = {
    component: true
  };
</script>
