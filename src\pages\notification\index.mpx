
<template>
  <view class="message-container">
    <naviBar
      show-address="{{true}}"
      backgroundColor="#fff"
      bindsearchlocation="openSearchLocation"
    ></naviBar>

    <view class="menu-container">
      <view class="header">
        <view
          class="tab"
          wx:class="{{ {active: activeTabKey === item.key} }}"
          wx:for="{{tabsList}}"
          wx:key="key"
          bindtap="selectTab(item.key)"
        >
          {{ item.name + '(' + item.count + ')' }}
        </view>
      </view>
      <t-tabs
        class="menu"
        value="{{activeMenuKey}}"
        space-evenly="{{false}}"
        bind:change="onTabsChange"
      >
        <t-tab-panel
          class="menu-item"
          wx:for="{{menuItemList}}"
          wx:key="key"
          label="{{item.name}}"
          value="{{item.key}}"
          badge-props="{{ { count: item.count, offset: ['4px', '4px'] } }}"
        />
      </t-tabs>
    </view>

    <scroll-view
      class="content-container"
      scroll-y
      type="list"
      show-scrollbar="{{false}}"
    >
      <abnormalVehicle
        wx:if="{{activeTabKey === 'AbnormalVehicle'}}"
        wx:for="{{contentList}}"
        wx:key="index"
        props="{{item}}"
        bindtap="handleAbnormalVehicle(item)"
      />
      <todoCard
        wx:if="{{activeTabKey === 'TodoList'}}"
        wx:for="{{contentList}}"
        wx:key="messageId"
        props="{{item}}"
        bindtap="handleClickTodo(item)"
      />
    </scroll-view>
  </view>
  <search-location
    visible="{{showSearchLocation}}"
    bindclosepopup="closeSearchLocation"
  ></search-location>
  <real-tab-bar />
</template>

<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import {
    TodoListMenu,
    AbnormalVehicleType,
    TodoListType,
    AbnormalVehicleMenu
  } from 'shared/utils/constant';
  import { NotificationApi } from 'shared/api/notification';
  import { HTTPSTATUSCODE } from '../../shared/api/fetch';
  import { cloneDeep } from 'lodash';
  import { IndexPageUrl } from 'shared/assets/imageUrl';
  import { sendGlobalEvent } from 'shared/utils/emit';
  interface Data {
    fetchApi: any;
    activeTabKey: 'TodoList' | 'AbnormalVehicle';
    activeMenuKey: AbnormalVehicleType | TodoListType | 'All';
    abnormalVehicleMap: any;
    todoListMap: any;
    tabs: any;
    menuItems: any;
    timer: any;
    showSearchLocation: boolean;
    IndexPageUrl: any;
    selectedTabKey: string;
    noticeNum: any;
  }

  createPage<Data>({
    data: {
      fetchApi: new NotificationApi(),
      IndexPageUrl,
      activeTabKey: 'TodoList',
      activeMenuKey: 'All',
      abnormalVehicleMap: {},
      todoListMap: {},
      tabs: {
        TodoList: { name: '待办事项', key: 'TodoList', count: 0 },
        AbnormalVehicle: {
          name: '异常车辆',
          key: 'AbnormalVehicle',
          count: 0
        }
      },
      noticeNum: null,
      menuItems: TodoListMenu,
      timer: null,
      showSearchLocation: false,
      selectedTabKey: 'notification'
    },
    computed: {
      tabsList() {
        return Object.values(this.tabs);
      },
      menuItemList() {
        return Object.values(this.menuItems);
      },
      contentList() {
        if (this.activeTabKey === 'AbnormalVehicle') {
          return this.abnormalVehicleMap[this.activeMenuKey];
        } else if (this.activeTabKey === 'TodoList') {
          return this.todoListMap[this.activeMenuKey];
        } else {
          return [];
        }
      }
    },
    onShow() {
      if (this.activeTabKey === 'TodoList') {
        this.getTodoList();
        this.timer && clearInterval(this.timer);
      } else if (this.activeTabKey === 'AbnormalVehicle') {
        this.getAbnormalVehicle();
        this.timer = setInterval(() => {
          this.getAbnormalVehicle();
        }, 5000);
      }
    },
    onLoad() {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    onUnload: function () {
      this.timer && clearInterval(this.timer);
    },
    onHide: function () {
      this.timer && clearInterval(this.timer);
    },
    methods: {
      selectTab(key: any) {
        if (this.activeTabKey === key) {
          return;
        }
        this.setData({
          activeTabKey: key,
          activeMenuKey: 'All'
        });
        if (key === 'TodoList') {
          this.getTodoList();
          this.timer && clearInterval(this.timer);
        } else if (key === 'AbnormalVehicle') {
          this.getAbnormalVehicle();
          this.timer = setInterval(() => {
            this.getAbnormalVehicle();
          }, 5000);
        }
      },
      onTabsChange(event: any) {
        const key = event.detail.value;
        if (this.activeMenuKey === key) {
          return;
        }
        this.setData({
          activeMenuKey: key
        });
      },
      async getTodoList() {
        const res = await this.fetchApi.getUserTodoList();
        if (res.code === HTTPSTATUSCODE.Success) {
          sendGlobalEvent('update_notice_num', {
            val: res.data.todoSize ?? null
          });
          const _todoListMap: any = {};
          const _menuItems: any = cloneDeep(TodoListMenu);

          _todoListMap['All'] = res.data.todoList;
          Object.keys(_menuItems).forEach((item: any) => {
            if (item !== 'ALL') {
              _menuItems[item].count = res.data.todoList.filter(
                (todos: any) => todos.module === item
              ).length;
            }
          });
          _menuItems.All.count = res.data.todoSize;
          res?.data?.todoList?.forEach((item: any) => {
            // 将数据分类
            const arr = _todoListMap[item.module];
            if (arr) {
              arr.push(item);
              _todoListMap[item.module] = arr;
            } else {
              _todoListMap[item.module] = [item];
            }
          });

          this.setData({
            todoListMap: _todoListMap,
            menuItems: _menuItems,
            noticeNum: res.data.todoSize,
            tabs: {
              TodoList: {
                name: '待办事项',
                key: 'TodoList',
                count: res.data.todoSize
              },
              AbnormalVehicle: {
                name: '异常车辆',
                key: 'AbnormalVehicle',
                count: res.data.errorSize
              }
            }
          });
        }
      },
      async getAbnormalVehicle() {
        const res = await this.fetchApi.getAbnormalVehicle();

        if (res.code === HTTPSTATUSCODE.Success) {
          const _abnormalVehicleMap: any = {};
          const _menuItems: any = cloneDeep(AbnormalVehicleMenu);

          let all: any[] = [];
          Object.keys(_menuItems).forEach((key: any) => {
            if (key !== 'ALL') {
              _menuItems[key].count = null;
            }
          });
          res?.data?.errorVehicleList?.forEach((item: any, index: number) => {
            // 计算每类的数量
            let num = _menuItems[item.category]?.count;
            if (num) {
              _menuItems[item.category].count = num + item.total;
            } else {
              _menuItems[item.category].count = item.total;
            }

            // 将数据分类
            _abnormalVehicleMap[item.category] = item.list;
            all = all.concat(item.list);
            if (index === res.data.errorVehicleList.length - 1) {
              _abnormalVehicleMap['All'] = all;
            }
          });
          _menuItems.All.count = res.data.errorSize;
          this.setData({
            abnormalVehicleMap: _abnormalVehicleMap,
            menuItems: _menuItems,
            tabs: {
              TodoList: this.tabs.TodoList,
              AbnormalVehicle: {
                name: '异常车辆',
                key: 'AbnormalVehicle',
                count: res.data.errorSize
              }
            }
          });
        }
      },
      handleClickTodo(item: any) {
        if (item.module === 'REPAIR') {
          wx.navigateTo({
            url: `/pages/notification/repairIssue/index?messageId=${item.messageId}&vehicleName=${item.vehicleName}`
          });
        } else if (item.module === 'ACCIDENT') {
          wx.navigateTo({
            url: `/pages/notification/bumpDetail/index?messageId=${item.messageId}&vehicleName=${item.vehicleName}`
          });
        }
      },
      handleAbnormalVehicle(item: any) {
        getApp().globalData.singleVehicle = item.vehicleName;
        wx.switchTab({
          url: '/pages/vehicle/index'
        });
      },
      openSearchLocation(e: any) {
        this.setData({
          showSearchLocation: true
        });
      },
      closeSearchLocation(e: any) {
        this.setData({
          showSearchLocation: false
        });
      },
      handleClickTab(e: any) {
        wx.vibrateShort({
          type: 'heavy',
          success: () => {
            console.log('震动成功');
          },
          fail: () => {
            console.log('震动失败');
          }
        });
        const data = e.currentTarget.dataset;
        const url = data.path;
        this.setData({
          selectedTabKey: data.key
        });
        wx.switchTab({ url });
      }
    }
  });
</script>

<style lang="scss">
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
  }
  .message-container {
    height: 100%;
    width: 100%;
    background: rgba(245, 245, 245, 1);
  }

  .menu-container {
    display: flex;
    flex-direction: column;
  }
  .header {
    height: 40px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
    .tab {
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(26, 26, 26, 1);
      &.active {
        color: rgba(250, 44, 25, 1);
      }
    }
  }
  .menu {
    --td-tab-item-active-color: rgba(250, 44, 25, 1);
    --td-badge-bg-color: rgba(252, 55, 55, 1);
    .t-tabs__item-inner--active {
      --td-badge-content-text-color: rgba(250, 44, 25, 1);
    }
  }

  .content-container {
    width: 94%;
    height: calc(100% - 280px);
    padding: 8px 12px;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "naviBar": "../../shared/ui/naviBar.mpx",
      "abnormalVehicle": "widgets/abnormalVehicle.mpx",
      "todoCard": "widgets/todoCard.mpx",
      "t-tabs": "tdesign-miniprogram/tabs/tabs",
      "t-tab-panel": "tdesign-miniprogram/tab-panel/tab-panel",
      "search-location": "widgets/searchLocation.mpx",
      "real-tab-bar": "shared/ui/realTabBar.mpx"
    },
    "navigationStyle": "custom",
    "renderer": "webview"
  }
</script>
