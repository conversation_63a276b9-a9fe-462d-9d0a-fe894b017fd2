
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="硬件报修"
    backUrl="/pages/workbench/index"
  ></navi-bar>

  <vehicle-list
    selectedVehicleName="{{selectedVehicleName}}"
    checkContent="{{checkContent}}"
    tip="选择车辆后，进入硬件报修"
    whichPageToGo="/pages/notification/hardwareReport/index"
    bind:handleSelect="handleSelect"
  />
</template>

<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  import { CheckType } from 'shared/utils/checkVehicle';
  interface Data {
    CheckType: any;
    selectedVehicleName: any;
    checkContent: any[];
  }
  createPage<Data>({
    data: {
      CheckType: CheckType,
      selectedVehicleName: null,
      checkContent: [{ checkType: CheckType.VehiclePeriod }]
    },
    onLoad() {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    methods: {
      handleSelect(e: any) {
        this.selectedVehicleName = e.detail.val;
        if (e.detail.val) {
          wx.navigateTo({
            url: `/pages/notification/hardwareReport/index?vehicleName=${e.detail.val}`
          });
        }
      }
    },
    onShow: function () {
      if (getApp().globalData.operateSelectVehicle) {
        this.selectedVehicleName = getApp().globalData.operateSelectVehicle;
      }
    },
    onHide: function () {
      getApp().globalData.operateSelectVehicle = null;
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx",
      "restart-operate": "widgets/commandOperate/restartOperate.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
