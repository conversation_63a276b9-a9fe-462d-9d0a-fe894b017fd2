<template>
  <view class="switch-container">
    <view class="tip" wx:if="{{selectedVehicle.length <= 0}}"
      >地图上选中车辆，可设置去往停靠点~</view
    >

    <scroll-view
      class="selected"
      wx:if="{{selectedVehicle.length > 0}}"
      scroll-x
      type="list"
    >
      <view wx:for="{{selectedVehicle}}" wx:key="item" class="vehicle-name">
        {{ item }}
        <image src="{{FreeDriveUrl.DelIcon}}" bindtap="handleDel(item)" />
      </view>
    </scroll-view>

    <view class="search-container">
      <view style="display: flex; align-items: center">
        <view class="title">全部车辆</view>
        <image src="{{FreeDriveUrl.SwitchIcon}}" bindtap="changeSort"></image>
        <view class="search-mode" bindtap="changeSort">{{
          searchMode.name
        }}</view>
      </view>
      <view
        class="search-btn"
        style="background: url({{VehicleUrl.SearchIconRed}}) no-repeat center/100%"
        bindtap="hanleClickSearch"
      ></view>
    </view>
  </view>

  <scroll-view
    class="distance-list"
    wx:if="{{searchMode.nextMode === 'station'}}"
    scroll-y
    type="list"
    style="height:{{scrollHeight}}px"
  >
    <vehicle-item
      wx:for="{{vehicleList}}"
      wx:key="vehicleName"
      itemInfo="{{item}}"
      bind:handleClick="handleSelectItem"
    >
    </vehicle-item>
  </scroll-view>

  <scroll-view
    class="station-list"
    wx:if="{{searchMode.nextMode === 'distance'}}"
    scroll-y
    type="list"
    style="height:{{scrollHeight}}px"
  >
    <view
      class="list-item"
      wx:for="{{stationVehicleList}}"
      wx:key="stationName"
    >
      <view class="station-name">
        {{ item.stationName }}({{ item.list.length }})
      </view>
      <vehicle-item
        wx:for="{{item.list}}"
        wx:key="vehicleName"
        itemInfo="{{item}}"
        bind:handleClick="handleSelectItem"
      >
      </vehicle-item>
    </view>
  </scroll-view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { WorkBenchUrl, FreeDriveUrl, VehicleUrl } from 'shared/assets/imageUrl';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { CheckVehicle } from 'shared/utils/checkVehicle';
  import { FreeDriveApi } from 'shared/api/freeDrive';
  import { SystemStatus } from 'shared/utils/constant';
  interface Data {
    WorkBenchUrl: any;
    FreeDriveUrl: any;
    VehicleUrl: any;
    SystemStatus: any;
    fetchApi: any;
    searchMode: any;
    vehicleList: any[];
  }

  const { screenHeight, statusBarHeight, safeArea } = wx.getSystemInfoSync();
  createComponent<any>({
    properties: {
      selectedVehicle: {
        type: Array,
        value: []
      },
      location: {
        type: String
      }
    },
    data: {
      WorkBenchUrl,
      FreeDriveUrl,
      VehicleUrl,
      SystemStatus,
      fetchApi: new FreeDriveApi(),
      searchMode: {
        name: '按站点排序',
        nextMode: 'station'
      },
      vehicleList: [],
      stationVehicleList: []
    },
    computed: {
      scrollHeight() {
        return screenHeight - statusBarHeight - 150 - 88 - 66;
      }
    },
    watch: {
      selectedVehicle: {
        handler(val, old) {
          this.formatSelectedVehicle(val, this.vehicleList);
        }
      },
      location: {
        handler(val, old) {
          const list = this.location.split(',');
          this.getVehicleList(list[0], list[1]);
        }
      }
    },
    methods: {
      changeSort() {
        if (this.searchMode.nextMode === 'station') {
          this.searchMode = {
            name: '按距离排序',
            nextMode: 'distance'
          };
          if (!this.stationVehicleList || this.stationVehicleList?.length <= 0) {
            this.formatStationVehicle();
          }
        } else if (this.searchMode.nextMode === 'distance') {
          this.searchMode = {
            name: '按站点排序',
            nextMode: 'station'
          };
        }
      },
      handleSelectItem(e: any) {
        this.triggerEvent('clickVehicleItem', {
          vehicleName: e.detail.val
        });
      },
      handleDel(val: string) {
        this.triggerEvent('clickVehicleItem', {
          vehicleName: val
        });
      },
      hanleClickSearch() {
        this.triggerEvent('clickSearch');
      },
      handleSelectStop() {
        this.triggerEvent('changeSection', {
          val: 'stopList'
        });
      },
      formatStationVehicle() {
        const obj: any = {};
        this.vehicleList.forEach((v: any) => {
          if (obj[v.stationName]) {
            obj[v.stationName].list.push(v);
          } else {
            obj[v.stationName] = { stationName: v.stationName, list: [v] };
          }
        });
        this.setData({
          stationVehicleList: Object.values(obj)
        });
      },
      formatSelectedVehicle(val: any, vehicleList: any[]) {
        if (this.searchMode.nextMode === 'station') {
          const curList: any[] = [];
          this.vehicleList.forEach((item: any) => {
            curList.push({
              ...item,
              checked: val.indexOf(item.vehicleName) > -1
            });
          });
          this.setData({
            vehicleList: curList
          });
        } else if (this.searchMode.nextMode === 'distance') {
          const obj: any = {};
          this.vehicleList.forEach((v: any) => {
            const vehcileChecked = val.indexOf(v.vehicleName) > -1;
            if (obj[v.stationName]) {
              obj[v.stationName].list.push({ ...v, checked: vehcileChecked });
              obj[v.stationName].stationCheckNum += 1;
            } else {
              obj[v.stationName] = {
                stationName: v.stationName,
                stationCheckNum: vehcileChecked ? 1 : 0,
                list: [{ ...v, checked: vehcileChecked }]
              };
            }
          });
          this.setData({
            stationVehicleList: Object.values(obj)
          });
        }
      },
      async getVehicleList(latitude: any, longitude: any) {
        const res = await this.fetchApi.getVehicleList(longitude, latitude);
        if (res.code === HTTPSTATUSCODE.Success) {
          this.setData({
            vehicleList: res.data
          });
          this.formatSelectedVehicle(this.selectedVehicle, res.data);
        }
      }
    },
    lifetimes: {
      created: function () {
        const list = this.location.split(',');
        this.getVehicleList(list[0], list[1]);
      },
      detached: function () {}
    }
  });
</script>

<style lang="scss" scoped>
  .rich-text-container {
    width: 50vw;
    overflow: auto;
    white-space: pre-wrap;
    border: 1px solid #ccc;
    padding: 16px;
  }
  .switch-container {
    .tip {
      text-align: center;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(150, 150, 150, 1);
    }
    .selected {
      display: flex;
      flex-wrap: wrap;
      padding-left: 16px;
      padding-right: 12px;
      height: 28px;
      width: 100%;
      .vehicle-name {
        min-width: 114px;
        height: 28px;
        background: rgba(255, 255, 255, 1);
        border-radius: 16px;
        padding: 6px 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 8px;
        margin-right: 4px;
        image {
          height: 16px;
          width: 16px;
          margin-left: 12px;
        }
      }
    }
    .search-container {
      height: 52px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 16px;
      padding-right: 16px;
      .title {
        margin-right: 18px;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(26, 26, 26, 1);
      }
      image {
        width: 16px;
        height: 16px;
      }
      .search-mode {
        margin-left: 2px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(128, 128, 128, 1);
      }
      .search-btn {
        width: 18px;
        height: 18px;
        margin-left: 8px;
      }
    }
  }

  .station-list {
    .list-item {
      .station-name {
        height: 32px;
        line-height: 32px;
        margin: 6px 8px;
        padding-left: 8px;
        padding-right: 8px;
        background: rgba(134, 141, 159, 0.1);
        border-radius: 6px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(128, 128, 128, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        overflow: hidden; /* 隐藏溢出内容 */
        white-space: nowrap; /* 避免文本换行 */
        text-overflow: ellipsis; /* 溢出文本显示省略号 */
        image {
          height: 18px;
          width: 18px;
          margin-left: 4px;
        }
      }
    }
  }

  .select-stop {
    width: 100%;
    height: 90px;
    padding: 8px 18px 9px 18px;
    position: relative;
    bottom: 0px;
    background-color: white;
    .btn {
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      border-radius: 20px;
      background: linear-gradient(
        135deg,
        rgba(250, 100, 25, 1) 0%,
        rgba(250, 89, 25, 1) 16.59259259%,
        rgba(250, 63, 25, 1) 55.40740741%,
        rgba(250, 44, 25, 1) 100%
      );
      &.enable {
        background: linear-gradient(
          135deg,
          rgba(250, 100, 25, 1) 0%,
          rgba(250, 89, 25, 1) 16.59259259%,
          rgba(250, 63, 25, 1) 55.40740741%,
          rgba(250, 44, 25, 1) 100%
        );
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "vehicle-item": "./vehicleItem.mpx"
    }
  }
</script>
