<template></template>

<script>
  import { createPage } from '@mpxjs/core';
  createPage({
    data: {},
    methods: {
      show(e) {
        console.log(e);
      }
    },
    onLoad(query) {
      const eventType = getApp().globalData.eventType;
      if (eventType === 'redirect') {
        const redirectUrl = getApp().globalData.redirectUrl;
        getApp().globalData.eventType = 'navigate';
        wx.redirectTo({
          url: redirectUrl
        });
      }
    }
  });
</script>

<style ></style>

<script name="json">
  module.exports = {
    usingComponents: {},
    navigationStyle: 'custom'
  };
</script>
