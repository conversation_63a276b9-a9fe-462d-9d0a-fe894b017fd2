
<template>
  <navi-bar
    show-address="{{false}}"
    showReturn="true"
    title="搜索车辆"
  ></navi-bar>

  <view class="search-container">
    <t-search
      t-class="search"
      t-class-input-container="input-container"
      placeholder="输入车号"
      bind:focus="handleFocus"
      bind:change="searchValChange"
    />
  </view>

  <view class="history-container" wx:if="{{!searchVal}}">
    <view class="del">
      <view>搜索记录</view>
      <view
        style="color: rgba(128, 128, 128, 1); display: flex"
        bindtap="handleDel"
      >
        <t-image
          src="{{WorkBenchUrl.DelIcon}}"
          mode="aspectFill"
          width="20px"
          height="20px"
        />清空
      </view>
    </view>

    <view class="his-vehicle-container">
      <view
        class="his-vehicle"
        wx:for="{{historyVehicleList}}"
        wx:key="item"
        bindtap="handleSelect(item)"
      >
        {{ item }}
      </view></view
    >
  </view>

  <scroll-view
    class="vehicle-container"
    wx:if="{{searchVal}}"
    scroll-y
    type="list"
  >
    <view
      class="vehicle-item"
      wx:for="{{matchedVehicleList}}"
      bindtap="handleSelect(item.vehicleName)"
      wx:key="vehicleName"
    >
      <rich-text class="vehicle-name" nodes="{{item.nodes}}"> </rich-text>
      <text class="description" wx:if="{{item.description}}">{{
        item.description
      }}</text>
    </view></scroll-view
  >
</template>

<script  lang="ts">
  import { createPage } from '@mpxjs/core';
  import { isEmpty } from 'shared/utils/utils';
  import { WorkBenchUrl } from 'shared/assets/imageUrl';
  import { CommonApi } from 'shared/api/common';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import {
    TakeOverTypeNameMap,
    TakeOverSourceNameMap,
    TakeOverType
  } from 'shared/utils/constant';
  import { CheckVehicle } from 'shared/utils/checkVehicle';
  import useLocationStore from 'shared/store/useLocationStore';
  interface Data {
    fetchApi: any;
    WorkBenchUrl: any;
    searchVal: any;
    vehicleList: any[];
    matchedVehicleList: any[];
    historyVehicleList: any[];
    whichPageToGo: any;
    backUrl: any;
    eventChannel: any;
    checkContent: any[];
  }
  createPage<Data>({
    data: {
      fetchApi: new CommonApi(),
      WorkBenchUrl: WorkBenchUrl,
      searchVal: null,
      vehicleList: [],
      matchedVehicleList: [],
      historyVehicleList: [],
      whichPageToGo: null,
      backUrl: null,
      eventChannel: null,
      checkContent: []
    },
    onLoad: function (option) {
      console.log('搜车页面', getCurrentPages());
      this.getHisVehicleList();
      this.getUserVehicleList();
      this.eventChannel = this.getOpenerEventChannel();
      this.eventChannel.on('searchVehiclePageChannel', (data: any) => {
        this.whichPageToGo = data.whichPageToGo;
        this.checkContent = data.checkContent;
      });
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });
    },
    onHide: function () {
      this.eventChannel = null;
    },
    methods: {
      searchValChange(e: any) {
        const Val = e.detail.value;
        this.searchVal = Val;
        const Regex = new RegExp(Val, 'ig');
        const list: any[] = [];
        this.vehicleList?.forEach((v: any) => {
          const matchInfo =
            v.vehicleName.match(Regex)?.length > 0
              ? v.vehicleName.match(Regex)[0]
              : null;
          if (Regex.test(v.vehicleName)) {
            list.push({
              ...v,
              nodes: v.vehicleName.replace(
                Regex,
                `<span class="highlight">${matchInfo}</span>`
              )
            });
          }
        });
        this.setData({
          matchedVehicleList: list
        });
      },
      handleDel() {
        this.clearHisVehicleList();
      },
      handleSelect(val: string) {
        this.addHisVehicleList(val);
        if (!this.checkContent || this.checkContent.length <= 0) {
          if (this.whichPageToGo) {
            wx.redirectTo({
              url: this.whichPageToGo + `?vehicleName=${val}`
            });
            getApp().globalData.operateSelectVehicle = val;
          } else {
            wx.navigateBack({
              delta: 1
            });
            this.eventChannel.emit('searchVehiclePageChannel', {
              vehicleName: val
            });
          }
          return;
        }

        const ItemInfo = this.vehicleList.find((v: any) => v.vehicleName === val);
        const check = new CheckVehicle(this.checkContent, val);
        check.checkVehicleFunc().then(res => {
          if (res) {
            if (this.whichPageToGo) {
              wx.redirectTo({
                url: this.whichPageToGo + `?vehicleName=${val}`
              });
              getApp().globalData.operateSelectVehicle = val;
            } else {
              wx.navigateBack({
                delta: 1
              });
              this.eventChannel.emit('searchVehiclePageChannel', {
                vehicleName: val
              });
            }
          }
        });
      },
      async getUserVehicleList() {
        const { userLocation } = useLocationStore();
        const res = await this.fetchApi.getUserVehicleList(
          userLocation.longitude,
          userLocation.latitude
        );
        if (res.code === HTTPSTATUSCODE.Success) {
          this.vehicleList = res.data.map((v: any) => {
            return {
              ...v,
              description: v.takeoverStatus
                ? `${TakeOverTypeNameMap.get(
                    v.takeoverStatus
                  )}-${TakeOverSourceNameMap.get(v.takeoverSource)}-${
                    v.takeoverUserName
                  }`
                : null
            };
          });
        }
      },
      async getHisVehicleList() {
        const res = await this.fetchApi.getHistoryVehicle();
        if (res.code === HTTPSTATUSCODE.Success) {
          this.historyVehicleList = res.data;
        }
      },
      async addHisVehicleList(vehicleName: string) {
        const res = await this.fetchApi.addHistoryVehicle(vehicleName);
        if (res.code === HTTPSTATUSCODE.Success) {
          this.getHisVehicleList();
        }
      },
      async clearHisVehicleList() {
        const res = await this.fetchApi.clearHistoryVehicle();
        if (res.code === HTTPSTATUSCODE.Success) {
          this.getHisVehicleList();
        }
      }
    }
  });
</script>

<style lang="scss">
  page {
    height: 100%;
    width: 100%;
  }
  .search-container {
    padding: 12px 16px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
    .search {
      height: 36px;
      width: 100%;
      background: rgba(245, 245, 245, 1);
      border-radius: 4px;
      .input-container {
        height: 36px;
      }
    }
  }
  .history-container {
    padding-top: 16px;
    padding-right: 16px;
    .del {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
      padding-left: 16px;
    }

    .his-vehicle-container {
      display: flex;
      flex-wrap: wrap;
      padding-left: 4px;
    }
    .his-vehicle {
      height: 18px;
      line-height: 18px;
      background: rgba(245, 245, 245, 1);
      border-radius: 32px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(51, 51, 51, 1);
      padding: 6px 12px;
      margin-top: 12px;
      text-align: center;
      margin-left: 8px;
    }
  }

  .vehicle-container {
    margin-left: 16px;
    margin-right: 16px;
    .vehicle-item {
      border-bottom: 1px solid #f0f0f0;
      width: 100%;
      display: flex;
      align-items: center;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      .vehicle-name {
        height: 52px;
        line-height: 52px;
        color: #1a1a1a;
        .highlight {
          color: #fa2c19;
        }
      }
      .description {
        margin-left: 12px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "t-row": "tdesign-miniprogram/row/row",
      "t-col": "tdesign-miniprogram/col/col",
      "t-image": "tdesign-miniprogram/image/image",
      "t-search": "tdesign-miniprogram/search/search",
      "navi-bar": "../../shared/ui/naviBar.mpx",
      "vehicle-list": "widgets/vehicleList.mpx"
    },
    "navigationStyle": "custom"
  }
</script>
