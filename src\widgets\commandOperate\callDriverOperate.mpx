<template>
  <t-dialog
    visible="{{visible}}"
    title="是否确认呼叫远驾"
    confirm-btn="确定呼叫"
    cancel-btn="取消"
    bind:confirm="confirmDialog"
    bind:cancel="closeDialog"
  >
    <t-input
      class="dialog-input"
      wx:style="background: rgb(245, 245, 245) "
      clearable
      maxlength="30"
      slot="content"
      value="{{inputVal}}"
      placeholder="请填写呼叫原因"
      placeholder-class="placeholder"
      bindblur="handleInputChange"
      bindchange="handleInputChange"
    />
  </t-dialog>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { CallFrontApi } from 'shared/api/callFront';

  createComponent<any>({
    properties: {
      vehicleName: {
        type: String,
        value: ''
      },
      visible: {
        type: Boolean,
        value: false
      }
    },
    data: {
      fetchApi: new CallFrontApi(),
      inputVal: null
    },
    methods: {
      handleInputChange(e: any) {
        this.setData({
          inputVal: e.detail.value
        });
      },
      closeDialog() {
        this.triggerEvent('onVisibleChange');
      },
      confirmDialog(e: any) {
        this.callCockpit();
      },
      async callCockpit() {
        const res = await this.fetchApi.callCockpit(
          this.vehicleName,
          this.inputVal
        );
        if (res.code === HTTPSTATUSCODE.Success) {
          wx.showToast({
            icon: 'none',
            title: '您呼叫的任务已插队远驾坐席！'
          });
          this.closeDialog();
          this.setData({
            inputVal: null
          });
        } else {
          wx.showToast({
            icon: 'none',
            title: res.message
          });
        }
      }
    }
  });
</script>

<style lang="scss" scoped>
  .placeholder {
    color: var(--td-text-color-placeholder);
    line-height: 96rpx;
    height: 96rpx !important;
    display: flex;
    align-items: center;
  }

  .dialog-input {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    margin-top: 32rpx;
    border-radius: 8rpx;
    background-color: rgba(245, 245, 245, 1);
    box-sizing: border-box;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-input": "tdesign-miniprogram/input/input",
      "t-dialog": "tdesign-miniprogram/dialog/dialog"
    }
  }
</script>
