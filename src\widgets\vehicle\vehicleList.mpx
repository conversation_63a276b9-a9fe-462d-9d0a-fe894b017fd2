<template>
  <scroll-view
    scroll-y
    type="list"
    show-scrollbar="{{false}}"
    class="vehicle-list"
    style="height: {{height}}px"
  >
    <view
      wx:for="{{vehicleList}}"
      wx:key="vehicleName"
      class="vehicle-item-wrapper"
      bindtap="onTap(item.vehicleName, $event)"
    >
      <view class="vehicle-info">
        <view
          wx:class="{{{ 'vehicle-name-normal': item.systemStatus === SystemStatus.NORMAL, 'vehicle-name-abnormal': item.systemStatus === SystemStatus.ABNORMAL, 'vehicle-name-offline': item.systemStatus === SystemStatus.OFFLINE}}}"
          >{{ item.vehicleName }}</view
        >
        <view class="business-status">{{
          item.businessStatus ? '有任务' : '无任务'
        }}</view>
        <view
          class="insurance-mark-icon"
          style="background: url({{SingleVehicle.InsuranceMarkIcon}}) no-repeat center/100%; width: 18px; height: 18px; margin-right: 12px;"
          wx:if="{{item.insuranceEffective}}"
        ></view>
        <text class="station-name">{{ item.stationName }}</text>
      </view>
      <common-checkbox
        wx:if="{{selectable}}"
        checked="{{selectedVehicles.includes(item.vehicleName)}}"
        bindonchange="onChange"
      ></common-checkbox>
      <view class="bottom-border"></view>
    </view>
  </scroll-view>
</template>
<script lang='ts'>
  import { createComponent } from '@mpxjs/core';
  import { SystemStatus } from 'shared/utils/constant';
  import { SingleVehicle } from 'shared/assets/imageUrl';
  const { screenHeight, statusBarHeight, safeArea } = wx.getSystemInfoSync();
  createComponent({
    properties: {
      vehicleList: {
        type: Array,
        value: []
      },
      selectable: {
        type: Boolean,
        value: false
      },
      selectedVehicles: {
        type: Array,
        value: []
      }
    },
    data: {
      SystemStatus,
      SingleVehicle,
      height: (screenHeight - statusBarHeight) * 0.75
    },
    methods: {
      onTap(vehicleName: string, e: any) {
        this.triggerEvent('clickvehicleitem', {
          vehicleName: vehicleName
        });
      },
      onChange() {}
    }
  });
</script>
<style lang="scss">
  .vehicle-list {
    flex: 1;
    .vehicle-item-wrapper {
      width: 100%;
      height: 52px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-direction: column;
      padding: 17px 16px 0px 16px;
      .vehicle-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        .vehicle-name-normal,
        .vehicle-name-abnormal,
        .vehicle-name-offline {
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 500;
          margin-right: 12px;
        }
        .vehicle-name-normal {
          color: rgba(18, 179, 93, 1);
        }
        .vehicle-name-abnormal {
          color: rgba(250, 44, 25, 1);
        }
        .vehicle-name-offline {
          color: rgba(137, 147, 175, 1);
        }
        .business-status {
          width: 44px;
          height: 20px;
          line-height: 20px;
          background: rgba(134, 141, 159, 0.1);
          border-radius: 4px;
          font-size: 12px;
          text-align: center;
          color: rgba(134, 141, 159, 1);
          margin-right: 12px;
        }
        .station-name {
          font-size: 14px;
          color: rgba(100, 100, 100, 1);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .bottom-border {
        width: 100%;
        height: 1px;
        background: rgb(224 224 224);
        margin-top: 1px;
      }
    }
  }
</style>
<script name='json'>
  module.exports = {
    component: true,
    usingComponents: {
      'common-checkbox': 'shared/ui/commonCheckbox.mpx'
    }
  };
</script>
