<template>
  <view class="vehicle-info">
    <view class="events">
      <view class="label">报警事件</view>
      <alarm-event
        wx:for="{{vehicleInfo.alarmEventList}}"
        wx:key="alarmEvent"
        props="{{item}}"
        showDuration="{{false}}"
      />
    </view>

    <view class="status-section-one">
      <view class="left">
        <view>
          <text class="label">配送状态</text>
          <text class="value">{{ scheduleStatusName || '无任务' }}</text></view
        >
        <view
          ><text class="label">里程</text>
          <text class="value">{{ mileage }}km</text></view
        >
        <view
          ><text class="label">速度</text>
          <text class="value">{{ speed }}</text></view
        >
        <view
          ><text class="label">电量</text>
          <text class="value">{{ power }}%</text></view
        >
      </view>
      <view class="right">
        <drivable-direction info="{{vehicleInfo.drivableDirection}}" />
      </view>
    </view>

    <view class="status-section-two">
      <view class="field-item">
        <text class="label">GPS</text>
        <view class="gpsValue">
          <view
            class="gps-line"
            wx:for="{{vehicleInfo.gpsSignal}}"
            wx:key="label"
            wx:class="{{[item.label]}}"
            wx:style="{{{background: item.background}}}"
          >
          </view>
        </view>
      </view>
      <view class="field-item"
        ><text class="label">系统状态</text>
        <text
          class="value"
          style="color: {{vehicleInfo.systemState ? vehicleInfo.systemState === 'NORMAL' ? '#12B35D' : '#FA2C19' : '#333333'}}"
          >{{ systemStateName || '-' }}</text
        ></view
      >
      <view class="field-item"
        ><text class="label">定位</text>
        <text class="value">{{ sceneSignal }}</text></view
      >
      <view class="field-item"
        ><text class="label">模式</text>
        <text class="value">{{ driveModeName || '-' }}</text>
        <update-detail-modal
          wx:if="{{isUpdateDetaiBtnVisible}}"
          isInit="{{isInit}}"
          vehicleName="{{vehicleName}}"
          updateDetail="{{updateDetail}}"
          show="{{isShowDetailModal}}"
          showSilent="{{isShowSilent}}"
          bindclose="hideDetailModal"
          bindshow="showDetailModal"
          bindcancelsilent="handleCancelSilent"
          bindshowsilentmodal="showSilentConfirmModal"
          bindconfirmsilent="confirmSilent"
        />
      </view>
    </view>

    <schedule-road-map stopList="{{vehicleInfo.scheduleStopList}}" />
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import {
    getEnumEventName,
    isEmpty,
    isNullObject,
    translateSceneSignal,
    convertSpeedMsToKmh
  } from 'shared/utils/utils';
  import { MapUpdaterStatusEnum } from 'shared/utils/constant';
  import { SingleVehicleApi } from 'shared/api/singleVehicle';
  let timer: any;
  createComponent({
    properties: {
      vehicleInfo: Object
    },
    data: {
      fetchApi: new SingleVehicleApi(),
      updateDetail: {},
      isShowDetailModal: false,
      isShowSilent: false,
      haveClickedSiletBtn: false,
      MapUpdaterStatusEnum
    },
    computed: {
      scheduleStatusName() {
        return getEnumEventName(
          'SCHEDULE_STATE_ENUM',
          this.vehicleInfo.scheduleStatus
        );
      },
      systemStateName() {
        return getEnumEventName(
          'SYSTEM_STATE_ENUM',
          this.vehicleInfo.systemState
        );
      },
      driveModeName() {
        return getEnumEventName('VEHICLE_STATE_ENUM', this.vehicleInfo.driveMode);
      },

      speed() {
        return convertSpeedMsToKmh(this.vehicleInfo.speed, 1);
      },
      power() {
        return Number(this.vehicleInfo.power).toFixed(2);
      },
      mileage() {
        return this.vehicleInfo.totalMileage
          ? `${Number(this.vehicleInfo.finishMileage / 1000).toFixed(2)}/${Number(
              this.vehicleInfo.totalMileage / 1000
            ).toFixed(2)}`
          : '0/0';
      },
      sceneSignal() {
        return this.vehicleInfo.sceneSignal
          ? `${this.vehicleInfo.sceneSignal} ${translateSceneSignal(
              this.vehicleInfo.sceneSignal
            )}`
          : '暂无数据';
      },
      isUpdateDetaiBtnVisible: function () {
        return (
          (this.updateDetail?.progressType ===
            MapUpdaterStatusEnum.DOWNLOADING.key ||
            this.updateDetail?.progressType ===
              MapUpdaterStatusEnum.UPGRADE.key) &&
          this.updateDetail?.downloadMethod === 'FULL_SPEED' &&
          !this.haveClickedSiletBtn
        );
      },
      vehicleName() {
        return this.vehicleInfo.vehicleName;
      },
      isInit() {
        return this.vehicleInfo.driveMode === 'INITIALIZATION';
      }
    },
    watch: {
      updateDetail: {
        handler(val, oldVal) {
          console.log(this.updateDetail);
          console.log(this.isUpdateDetaiBtnVisible);
        }
      },
      vehicleInfo: {
        handler(val, oldVal) {
          if (
            isNullObject(this.updateDetail) &&
            val.driveMode === 'INITIALIZATION'
          ) {
            this.fetchApi
              .getMapUpdateStatus(this.vehicleName)
              .then((res: any) => {
                if (res.code === HTTPSTATUSCODE.Success) {
                  const mapUpdateDetail =
                    !isEmpty(res?.data?.appInfoList) &&
                    res.data.appInfoList.filter(
                      (item: any) => item.appName === 'map'
                    )[0];
                  console.log(mapUpdateDetail);
                  if (!isNullObject(mapUpdateDetail)) {
                    this.setData({
                      updateDetail: mapUpdateDetail
                    });
                  }
                } else {
                  wx.showToast({
                    title: res.message,
                    icon: 'none'
                  });
                }
              })
              .catch(err => {
                wx.showToast({
                  title: '请求失败',
                  icon: 'none'
                });
              });
          }
        }
      },
      isShowDetailModal: {
        handler(val, oldVal) {
          if (val) {
            if (timer) {
              clearInterval(timer);
              timer = null;
            }
            timer = setInterval(() => {
              this.getMapUpdateStatus();
            }, 30000);
          }
        }
      }
    },
    methods: {
      getMapUpdateStatus() {
        this.fetchApi
          .getMapUpdateStatus(this.vehicleName)
          .then((res: any) => {
            if (res.code === HTTPSTATUSCODE.Success) {
              const mapUpdateDetail =
                !isEmpty(res?.data?.appInfoList) &&
                res.data.appInfoList.filter(
                  (item: any) => item.appName === 'map'
                )[0];
              if (!isNullObject(mapUpdateDetail)) {
                if (mapUpdateDetail.downloadMethod === 'LIMIT_SPEED') {
                  wx.showToast({
                    title: '已转为静默下载',
                    icon: 'none'
                  });
                  this.setData({
                    isShowSilent: false,
                    isShowDetailModal: false
                  });
                } else {
                  this.setData({
                    isShowDetailModal: true
                  });
                }
                this.setData({
                  updateDetail: mapUpdateDetail
                });
              }
            } else {
              wx.showToast({
                title: res.message,
                icon: 'none'
              });
            }
          })
          .catch((err: any) => {
            wx.showToast({
              title: '请求失败',
              icon: 'none'
            });
          });
      },
      confirmSilent() {
        this.fetchApi
          .postponeMapUpdate(this.vehicleName)
          .then((res: any) => {
            if (res.code === HTTPSTATUSCODE.Success) {
              this.setData({
                isShowSilent: false,
                isShowDetailModal: false,
                haveClickedSiletBtn: true
              });
              wx.showToast({
                title: '转为静默下载成功',
                icon: 'none'
              });
            } else {
              wx.showToast({
                title: '失败：' + res.message,
                icon: 'none'
              });
            }
          })
          .catch((err: any) => {
            wx.showToast({
              title: '失败：' + err,
              icon: 'none'
            });
          });
      },
      showDetailModal(e: any) {
        this.getMapUpdateStatus();
      },
      hideDetailModal(e: any) {
        this.setData({
          isShowDetailModal: false
        });
        clearInterval(timer);
        timer = null;
      },
      handleCancelSilent(e: any) {
        this.setData({
          isShowSilent: false
        });
      },
      showSilentConfirmModal() {
        this.setData({
          isShowSilent: true
        });
      }
    }
  });
</script>

<style lang="scss" scoped>
  .vehicle-info {
    background-color: #fff;

    .report-buttons {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      .report-button {
        padding: 8px 16px;
        border: 1px solid #d9534f;
        color: #d9534f;
        border-radius: 4px;
        background-color: #fff;
      }
    }
    .events {
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(151, 151, 151, 0.5);
      padding-top: 8px;
      padding-bottom: 8px;
      flex-wrap: wrap;
    }

    .status-section-one {
      border-bottom: 1px solid rgba(151, 151, 151, 0.5);
      display: flex;
      justify-content: space-between;
      padding-bottom: 8px;
      .left {
        view {
          display: flex;
          align-items: center;
          margin-top: 8px;
        }
      }
      .right {
        margin-top: 8px;
      }
    }

    .status-section-two {
      border-bottom: 1px solid rgba(151, 151, 151, 0.5);
      display: flex;
      flex-wrap: wrap;
      padding-bottom: 8px;
      margin-bottom: 8px;
      .field-item {
        display: flex;
        align-items: center;
        width: 50%;
        margin-top: 8px;
      }
    }

    .label {
      width: 56px;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(153, 153, 153, 1);
    }
    .value {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(51, 51, 51, 1);
    }
    .gpsValue {
      height: 16px;
      display: flex;
      align-items: flex-end;
      justify-content: end;
    }

    .gps-line {
      border-radius: 1px;
      &.one {
        height: 4px;
        margin-right: 1px;
        width: 2px;
      }
      &.two {
        height: 7px;
        width: 2px;
        margin-right: 1px;
      }
      &.three {
        height: 10px;
        width: 2px;
        margin-right: 1px;
      }
      &.four {
        width: 2px;
        height: 13px;
        margin-right: 1px;
      }
      &.five {
        width: 2px;
        height: 16px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "alarm-event": "shared/ui/alarmEvent.mpx",
      "drivable-direction": "shared/ui/drivableDirection.mpx",
      "schedule-road-map": "shared/ui/scheduleRoadMap.mpx",
      "update-detail-modal": "shared/ui/updateDetailModal.mpx"
    }
  }
</script>
