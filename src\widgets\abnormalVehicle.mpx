<template>
  <view class="abnormal-vehicle-card">
    <view class="header">
      <view class="left">
        <text class="vehicleName">{{ props.vehicleName }}</text>
        <view class="divider"></view>
        <text class="stationName">{{ props.stationName }}</text>
      </view>
      <view class="right">
        <image class="icon" src="{{WorkBenchUrl.ArrowRight}}" />
      </view>
    </view>

    <view class="alarm-list">
      <alarmEvent
        wx:for="{{formatAlarmEvent}}"
        wx:key="alarmEvent"
        props="{{item}}"
      />
    </view>

    <easyRoadMap
      scheduleState="{{props.scheduleState}}"
      stopList="{{formatStopList}}"
    />

    <view class="footer">
      <iconField imageSrc="{{Notification.SpeedIcon}}" text="{{speed}}" />
      <view class="divider"></view>
      <iconField
        imageSrc="{{Notification.ModeIcon}}"
        text="{{driveModeName}}"
      />
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import * as dayjs from 'dayjs';
  import { WorkBenchUrl, Notification } from 'shared/assets/imageUrl';
  import { StopTravelStatus, StopAction } from 'shared/utils/constant';
  import { formatTimestamp, getEnumEventName, convertSpeedMsToKmh } from 'shared/utils/utils';

  createComponent({
    properties: {
      props: Object
    },
    data: {
      WorkBenchUrl: WorkBenchUrl,
      Notification: Notification,
      formatAlarmEvent: []
    },
    computed: {
      formatStopList() {
        if (this.props.schedule && this.props.schedule.stopList) {
          return this.props.schedule.stopList.map((item: any, index: number) => {
            let percent = 0;
            let nextStop = this.props.schedule.stopList[index + 1];
            if (
              [
                StopTravelStatus.STAY,
                StopTravelStatus.DEPART,
                StopTravelStatus.SKIP
              ].includes(item.travelStatus)
            ) {
              percent = 1;
            } else if (item.travelStatus === StopTravelStatus.START) {
              percent =
                this.props.schedule.currentStopFinishedMileage /
                item.globalMileage;
            } else {
              percent = 0;
            }

            if (percent < 0) {
              percent = 0;
            }

            return {
              ...item,
              percent: percent >= 1 ? 1 : percent,
              estDepartTime:
                item.estDepartTime && dayjs(item.estDepartTime).format('HH:mm'),
              arrivedTime:
                (item.arrivedTime && new Date(item.arrivedTime).getTime()) || 0,
              time:
                item &&
                item.arrivedTime &&
                item.waitingTime &&
                item.waitingTime * 60000 -
                  (new Date().getTime() - new Date(item.arrivedTime).getTime())
            };
          });
        } else {
          return null;
        }
      },
      driveModeName() {
        return getEnumEventName('VEHICLE_STATE_ENUM', this.props.vehicleState);
      },
      speed() {
        return convertSpeedMsToKmh(this.props.speed, 1);
      }
    },
    methods: {
      startTimer() {
        this.timer = setInterval(() => {
          const Now: any = new Date();
          this.formatAlarmEvent = this.props.alarmEventList
            ? this.props.alarmEventList.map((v: any) => {
                const report: any = new Date(v.reportTime.replace(/-/g, '/'));
                return {
                  ...v,
                  duration: formatTimestamp(Now - report, 'xxhxxmin')
                };
              })
            : [
                {
                  duration: formatTimestamp(
                    Now - new Date(this.props.recordTime.replace(/-/g, '/')),
                    'xxhxxmin'
                  )
                }
              ];
        }, 1000);
      },
      stopTimer() {
        console.log('stopTimer')
        if (this.timer) {
          clearInterval(this.timer);
          this.timer = null;
        }
      }
    },
    lifetimes: {
      created: function () {
        this.startTimer();
      },
      detached: function () {
        this.stopTimer();
      }
    }
  });
</script>

<style lang="scss">
  .abnormal-vehicle-card {
    padding: 9px 12px;
    background-color: #fff;
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .divider {
      width: 2px;
      height: 13px;
      background: rgba(224, 224, 224, 1);
      margin-left: 4px;
      margin-right: 4px;
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      display: flex;
      align-items: center;
      .vehicleName {
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
      }
      .stationName {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(128, 128, 128, 1);
      }
    }

    .right {
      .icon {
        width: 16px;
        height: 16px;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
  }
  .alarm-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 6px;
    alarmevent {
      margin-right: 8px;
    }
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "alarmEvent": "shared/ui/alarmEvent.mpx",
      "iconField": "shared/ui/iconField.mpx",
      "easyRoadMap": "shared/ui/easyRoadMap.mpx"
    }
  }
</script>