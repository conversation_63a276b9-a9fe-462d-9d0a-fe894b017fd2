<template>
  <view
    style="width:{{screenWidth}}px;background-color:{{backgroundColor}} "
    class="navi-wrapper"
  >
    <view style="height: {{statusBarHeight}}px"></view>

    <view class="navi" style="height:{{titleBarHeight}}px">
      <view
        wx:if="{{showReturn}}"
        style="background: url({{IndexPageUrl.ArrowLeft}}) no-repeat center/100%; width: 20px; height: 20px"
        bindtap="goback"
      ></view>
      <view
        wx:if="{{showAddress}}"
        class="position"
        bindtap="handleOpenDialog"
        style="margin-right: {{showReturn ? 0 : 24}}px"
      >
        <image class="icon" src="{{IndexPageUrl.Position}}"></image>
        <text class="address">{{ address }}</text>
      </view>

      <view
        class="{{showAddress ? 'title' : 'mid-title'}}"
        style="flex-grow: {{ !showReturn && !showAddress ? 1 : 0.9}}"
        >{{ title }}</view
      >
    </view>
  </view>
  <view style="height:{{placeholderHeight}}px;width:100vw"></view>
</template>

<script  lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { mapStores } from '@mpxjs/pinia';
  import { IndexPageUrl } from 'shared/assets/imageUrl';
  import useLocationStore from '../store/useLocationStore';
  const app = getApp();
  createComponent({
    data: {
      IndexPageUrl: IndexPageUrl,
      statusBarHeight: '',
      titleBarHeight: '',
      screenWidth: ''
    },
    properties: {
      title: {
        type: String,
        value: '京小鸽'
      },
      showAddress: {
        type: Boolean,
        value: true
      },
      showReturn: {
        type: Boolean,
        value: false
      },
      backUrl: {
        type: String,
        value: ''
      },
      backgroundColor: {
        type: String,
        value: 'white'
      },
      isNavigateBump: {
        type: Boolean,
        value: false
      }
    },
    lifetimes: {
      attached() {
        this.setData({
          statusBarHeight: app.globalData.statusBarHeight,
          titleBarHeight: app.globalData.titleBarHeight,
          screenWidth: app.globalData.screenWidth
        });
      }
    },
    computed: {
      ...mapStores(useLocationStore),
      address() {
        if (this.locationStore.getAddress) {
          return this.locationStore.getAddress;
        } else {
          const initAddress = wx.getStorageSync('userAddress');
          return initAddress;
        }
      },
      placeholderHeight() {
        return this.statusBarHeight + this.titleBarHeight;
      }
    },

    methods: {
      handleOpenDialog(e: any) {
        this.triggerEvent('searchlocation');
      },
      goback(e: any) {
        console.log('页面栈', getCurrentPages());
        console.log('指定的backurl', this.backUrl);
        if (this.isNavigateBump) {
          wx.navigateBack({
            delta: 1
          });
          return;
        }
        if (this.backUrl) {
          if (
            [
              '/pages/notification/index',
              '/pages/vehicle/index',
              '/pages/workbench/index',
              '/pages/my/index'
            ].indexOf(this.backUrl) > -1
          ) {
            wx.switchTab({ url: this.backUrl });
            return;
          }
          wx.redirectTo({ url: this.backUrl });
          return;
        }
        wx.navigateBack({
          delta: 1
        });
      }
    }
  });
</script>

<style lang="scss">
  .navi {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 18px;
    padding-right: 18px;
    .position {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 120px;
      font-size: 14px;
      margin-right: 24px;
      .icon {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
      .address {
        width: 100px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .title,
    .mid-title {
      font-family: PingFang SC;
      font-weight: 500;
      color: black;
      font-size: 18px;
    }
    .mid-title {
      text-align: center;
      flex-grow: 0.9;
    }
  }
  .navi-wrapper {
    position: fixed;
    z-index: 999;
  }
</style>

<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-image': 'tdesign-miniprogram/image/image'
    }
  };
</script>
