import { PermissionKey } from './constant';

export interface pageConfig {
  permission: PermissionKey; // 页面所需权限
  [key: string]: any; // 其他可选属性
}
export interface buttonConfig {
  show: boolean; // 是否显示按钮
  [key: string]: any; // 其他可选属性
}

class PermissionManager {
  private permissionCache: Map<string, boolean> = new Map();

  // 检查单个权限
  hasPermission(permissionKey: PermissionKey): boolean {
    const cacheKey = permissionKey.toString();
    if (this.permissionCache.has(cacheKey)) {
      return this.permissionCache.get(cacheKey)!;
    }
    const appResourceInfoList = wx.getStorageSync('appResourceInfoList');
    if (!Array.isArray(appResourceInfoList)) {
      return false;
    }
    const hasPermission = !!(
      appResourceInfoList.findIndex(
        (item: any) => item.code === permissionKey
      ) > -1
    );
    this.permissionCache.set(cacheKey, hasPermission);
    return hasPermission;
  }
  // 批量权限检查
  hasPermissions(permissions: buttonConfig[]): boolean {
    return permissions.every(item => item.show);
  }
  // 获取有权限的页面
  filterByPermission(items: pageConfig[]): pageConfig[] {
    return items.filter(item => this.hasPermission(item.permission));
  }
  // 清空权限缓存
  clearPermissionCache(): void {
    this.permissionCache.clear();
  }
}

const permissionManager = new PermissionManager();

export { PermissionManager };
export default permissionManager;
