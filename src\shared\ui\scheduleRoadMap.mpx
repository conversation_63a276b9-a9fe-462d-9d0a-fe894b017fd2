<template>
  <view class="schedule-roadMap">
    <view class="road-content" wx:for="{{formatStopList}}" wx:key="id">
      <view class="stop-info">
        <view class="stop-icon" wx:class="{{[item.stopIconClass]}}">
          {{ StopTypeTitleMap[item.stopAction] }}
        </view>
        <text
          class="stop-name"
          wx:class="{{{'STAY': item?.travelStatus === 'STAY'}}}"
        >
          {{ item.name }}
        </text>
      </view>

      <view class="line-and-time">
        <view
          wx:if="{{index !== currentGoIndex - 1}}"
          wx:class="{{ {iconActive: item.travelStatus ==='DEPART' || item.travelStatus ==='SKIP',
          icon: item.travelStatus !=='DEPART' && item.travelStatus !=='SKIP'
          } }}"
        >
        </view>
        <view wx:if="{{index === currentGoIndex - 1}}">
          <image src="{{SingleVehicle.MiddleLine}}" class="progress" />
        </view>

        <view
          wx:if="{{item.travelStatus === 'STAY' && stopList.length - 1 !== index}}"
        >
          <stop-item
            status="{{item.travelStatus}}"
            waitingTime="{{item.waitingTime}}"
            arrivedTime="{{item.arrivedTime}}"
            wx:if="{{item.estDepartTime == null}}"
          />
          <view class="origin-card" wx:if="{{item.estDepartTime}}">
            {{ item.estDepartTime }} 返程
          </view>
        </view>
        <view
          class="gray-card"
          wx:if="{{item.stopAction !== 'START' && item.arrivedTime}}"
        >
          到 {{ item.arrivedTime }}
        </view>
        <view class="gray-card" wx:if="{{item.departTime}}">
          发 {{ item.departTime }}
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { formatTimestamp } from 'shared/utils/utils';
  import { SingleVehicle } from 'shared/assets/imageUrl';
  import * as dayjs from 'dayjs';
  createComponent<any>({
    properties: {
      stopList: { type: Array }
    },
    data: {
      SingleVehicle,
      StopTypeClass: {
        START: ['circle-active', 'circle', 'circle-active'],
        HOME: ['circle-active', 'circle', 'circle-active'],
        PICKUP: ['point-red', 'point-gray', 'point-current'],
        LOADING: ['point-red', 'point-gray', 'point-current'],
        UNLOADING: ['point-red', 'point-gray', 'point-current'],
        RETURN: ['circle-active', 'circle', 'circle-active'],
        DROPOFF: ['point-red', 'point-gray', 'point-current'],
        LOAD: ['point-red', 'point-gray', 'point-current'],
        UNLOAD: ['point-red', 'point-gray', 'point-current']
      },
      StopTypeTitleMap: {
        START: '起',
        HOME: '终',
        PICKUP: '',
        LOADING: '',
        RETURN: '终',
        DROPOFF: '',
        LOAD: '',
        UNLOAD: ''
      }
    },
    computed: {
      currentGoIndex: function () {
        const newStopListReverse = JSON.parse(JSON.stringify(this.stopList));
        const goIndex = newStopListReverse
          .reverse()
          .findIndex((item: any, index: number) => {
            return item.travelStatus === 'START';
          });
        const go = newStopListReverse.length - goIndex - 1;
        return goIndex >= 0 ? go : -1;
      },
      formatStopList: function () {
        return this.stopList.map((v: any) => {
          return {
            ...v,
            arrivedTime: v.arrivedTime
              ? this.formatTime(v.arrivedTime, 'HH:mm:ss')
              : null,
            departTime: v.departTime
              ? this.formatTime(v.departTime, 'HH:mm:ss')
              : null,
            estDepartTime: v.estDepartTime
              ? this.formatTime(v.estDepartTime, 'HH:mm')
              : null,
            stopIconClass: this.handleStopClass(v.stopAction, v.travelStatus)
          };
        });
      }
    },
    methods: {
      handleStopClass(type: any, status: any) {
        const stopType = this.StopTypeClass[type]; // ['已过点'， '未过'， '当前']
        if (!stopType) {
          return '';
        }
        if (['DEPART', 'SKIP'].includes(status)) {
          return stopType[0];
        } else if (status == 'STAY') {
          return stopType[2];
        } else {
          return stopType[1];
        }
      },
      formatTime(timeStr: string, type: string) {
        return dayjs(timeStr).format(type);
      }
    },
    lifetimes: {
      created: function () {}
    }
  });
</script>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "stop-item": "shared/ui/stopItem.mpx"
    }
  }
</script>

<style lang="scss" scoped>
  .schedule-roadMap {
    background: white;
    border-top: none;
    border-radius: 0 0 8px 8px;
    .road-content {
      font-size: 24px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(102, 102, 102, 1);
      .stop-info {
        display: flex;
        align-items: center;
        height: 20px;
        .stop-icon {
          font-size: 12px;
          font-family: JDLANGZHENGTI;
          font-weight: normal;
          margin-left: 1px;
          &.circle {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            background: rgba(230, 230, 230, 1);
            border-radius: 50%;
          }
          &.circle-active {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            background: rgba(254, 214, 207, 1);
            border-radius: 50%;
            color: rgba(250, 44, 25, 1);
          }

          &.point-gray {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            &::after {
              content: '';
              display: block;
              width: 8px;
              height: 8px;
              background: rgba(230, 230, 230, 1);
              border-radius: 50%;
            }
          }

          &.point-red {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            &::after {
              content: '';
              display: block;
              width: 8px;
              height: 8px;
              background: rgba(254, 214, 207, 1);
              border-radius: 50%;
            }
          }

          &.point-current {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(254, 214, 207, 1);
            &::after {
              content: '';
              display: block;
              width: 8px;
              height: 8px;
              background: rgba(250, 44, 25, 1);
              border-radius: 50%;
              border: 2px solid white;
            }
          }
        }
        .stop-name {
          margin-left: 8px;
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: normal;
          color: rgba(102, 102, 102, 1);
          &.STAY {
            font-size: 14px;
            color: rgba(50, 50, 50, 1);
          }
        }
      }
      &:last-child {
        .line-and-time {
          .icon {
            opacity: 0;
          }
        }
      }
      .line-and-time {
        display: flex;
        height: 100%;
        align-items: center;
        .icon {
          height: 45px;
          width: 10px;
          border-left: 2px solid #e6e6e6;
          margin-left: 9px;
        }
        .iconActive {
          height: 45px;
          width: 10px;
          border-left: 2px solid rgba(254, 214, 207, 1);
          margin-left: 9px;
        }
        .progress {
          width: 22px;
          height: 44px;
        }
        .origin-card {
          font-size: 10px;
          font-weight: normal;
          color: rgba(232, 140, 0, 1);
          border-radius: 8px;
          padding: 0px 8px;
          margin-left: 10px;
          background: rgba(255, 191, 0, 0.1);
          border-radius: 19px;
          opacity: 10;
        }
        .gray-card {
          font-size: 10px;
          font-weight: normal;
          color: rgba(102, 102, 102, 1);
          background: rgba(230, 230, 230, 1);
          border-radius: 8px;
          padding: 0px 8px;
          margin-left: 10px;
        }
      }
    }
  }
</style>
